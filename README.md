instalacja z<PERSON>

```bash
sudo apt install php8.3-cli php8.3-intl php8.3-xml php8.3-curl php8.3-mbstring php8.3-pgsql ghostscript

sudo wget https://github.com/wkhtmltopdf/packaging/releases/download/********-2/wkhtmltox_********-2.jammy_amd64.deb
sudo dpkg -i ./wkhtmltox_********-2.jammy_amd64.deb
```


# Generowanie nakelejek

```
sudo php /srv/beloyal-api/latest/bin/console sticker:generate --env=myjnia10_pl --no-debug --start=9000x00 --length=100 -vvv --lang=pl
```

### Generowanie PDF z HTML - wkhtml2pdf

Aplikacja wymaga wkhtml2pdf w wersji z pachem do qt ze strony https://wkhtmltopdf.org/downloads.html

### <PERSON><PERSON> sprawd<PERSON>ć wersję wkhtml2pdf
``` wkhtmltopdf --version```
powinno zwr<PERSON><PERSON>ć wersję z dopisekiem (with patched qt)

### Instalacja

```
wget https://github.com/wkhtmltopdf/packaging/releases/download/********-2/wkhtmltox_********-2.jammy_amd64.deb
```

```
dpkg -i ./wkhtmltox_********-2.jammy_amd64.deb
```


### ścieżka od 

```
/usr/local/bin/wkhtmltopdf
```