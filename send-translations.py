#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import gspread
from ruamel.yaml import YAML
from oauth2client.service_account import ServiceAccountCredentials
import yaml

# Define the scope of the Google Sheets API
scope = [
    'https://spreadsheets.google.com/feeds',
    'https://www.googleapis.com/auth/drive'
]

# Path to the credentials JSON file downloaded from Google Cloud Console
creds = ServiceAccountCredentials.from_json_keyfile_name('cm-bkf-pl-170e3bd4338b.json', scope)

# Authorize the client using the credentials
client = gspread.authorize(creds)

# Open the Google Sheets spreadsheet by its title
spreadsheet = client.open('bl translations')

# Select a specific worksheet within the spreadsheet
worksheet = spreadsheet.worksheet('Sheet1')

# Get all values from the worksheet as a list of lists
data = worksheet.get_all_values()

langMap = {lang: idx for idx, lang in enumerate(data[0]) if lang}

environment = {env: idx for idx, env in enumerate(data[1]) if env}

langsData = {
    'front': {lang: {} for idx, lang in enumerate(data[0]) if lang},
    'backend': {lang: {} for idx, lang in enumerate(data[0]) if lang}
}

saveConfig = {
    'front': { 'path': '.src/i18n/', 'ext': 'yaml' },
    'backend': { 'path' :'./translations/', 'ext': 'yml' },
}

langs = ['sv']
source = 'backend'

translation_directory = saveConfig['backend']['path']

# Function to load translations from YAML files for a specific language
def load_translations(language):
    filename = f"messages.{language}.yml"

    with open(os.path.join(translation_directory, filename), "r") as file:
        content = yaml.safe_load(file)
        return extract_keys_from_yaml(content)

def extract_keys_from_yaml(data):
    """Rekursywne wydobywanie kluczy z zagnieżdżonych struktur YAML."""
    translate = list()
    if isinstance(data, dict):
        for k, v in data.items():
            translate.append([source, k, v])
    return translate

for lang in langs:
    translations = load_translations(lang)

# uncoment to send translations to google sheet file
    worksheet.update(range_name='C130', values=translations)
