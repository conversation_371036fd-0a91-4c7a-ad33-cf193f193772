{% extends info.baseTemplate %}
{% block body %}

    <div class="row">
        <div class="col s12 m12 l12">
            <ul>
                {% for row in header %}
                    <li>{{ row|join(' ') }}</li>
                {% endfor %}
            </ul>
        </div>
    </div>

    <div class="row">
        <div class="col s12 m12 l12">
            <h2 class="bold">{{ reportName | trans}}</h2>
        </div>
    </div>
    <div class="pt-2 pb-2">
        {% for table in tables %}
            <div>
                {% if table.name is not empty %}
                    <h3 class="bold pt-25">{{ table.name }}</h3>
                {% endif %}
                <div class="bold">{{ table.description }}</div>
                <div class="row">
                    <div class="col m12 s12">
                        <div id="pdf-turnover-table" class="pdf-table">
                            <table class="display table no-footer pdf-table striped">
                                <thead>
                                <tr class="table-header">
                                    {% for column in table.columns  %}
                                        <th class="{{ column.class }}">{{ column.name|trans }}</th>
                                    {% endfor %}
                                </tr>
                                </thead>
                                <tbody>
                                {% for item in table.items %}
                                    <tr>
                                        {% for column in table.columns  %}
                                            {% if item[column.key] is not null %}
                                                <td class="{{ column.class }}">
                                                    {% if column.numberFormat is not null %}
                                                        {{ item[column.key] | number_format(column.numberFormat) }}
                                                    {% else %}
                                                        {{ item[column.key] }}
                                                    {% endif %}
                                                    {{ column.unit }}
                                                </td>
                                            {% else %}
                                                <td class="{{ column.class }}"> - {{ column.unit }}</td>
                                            {% endif %}
                                        {% endfor %}
                                    </tr>
                                {% endfor %}
                                {% if table.summary is null %}
                                {% else %}
                                    <tfoot>
                                        <tr class="table-header">
                                            {% for column in table.columns  %}
                                                {% if  table.summary[column.key] is not null %}
                                                    <th class="{{ column.class }}">
                                                        {% if column.numberFormat is not null %}
                                                            {{ table.summary[column.key] | number_format(column.numberFormat) }}
                                                        {% else %}
                                                            {{ table.summary[column.key] }}
                                                        {% endif %}
                                                        {{ column.unit }}
                                                    </th>
                                                {% else %}
                                                    <th class="{{ column.class }}"></th>
                                                {% endif %}
                                            {% endfor %}
                                        </tr>
                                    </tfoot>
                                {% endif %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>
{% endblock %}
