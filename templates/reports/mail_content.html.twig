{% extends 'base_email.html.twig' %}

{% block body %}
    <tr>
        <td style="padding: 30px 20px 0px 20px; background-color: #fff; color: #000">
            <p>
                <b>{{ 'email.welcome'|trans({}, null, language) }}</b>
                {{ userEmail }} !
            </p>
        </td>
    </tr>
    <tr>
        <td style="padding: 20px; background-color: #fff; color: #000">
            {{ 'report_email_content'|trans({'%name%' :reportName}, null, language) }}
        </td>
    </tr>
    <tr>
        <td style="padding: 20px; background-color: #fff; color: #000">
            <p>
                {{ 'email.greetings'|trans({}, null, language) }}<br/>
            </p>
        </td>
    </tr>
{% endblock body %}