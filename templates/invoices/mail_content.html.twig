{% extends 'base_email.html.twig' %}

{% block body %}
    <tr>
        <td style="padding: 30px 20px 0px 20px; background-color: #fff; color: #000">
            <p>
                <b>{{ 'email.welcome'|trans({}, null, language) }}</b>
                {{ entity.name|default(emailAddress) }}!
            </p>
            {% if entity.paymentDate|date("Ymd") > "now"|date("Ymd") or entity.paymentMethod == 'post-paid' %}
                <p>{{ 'invoices.check-attachment-facture'|trans({'%issue-date%': entity.invoiceDate|date("Y-m-d"), '%number%': entity.number, '%amount%': entity.totalGross|number_format(2)  ~ ' ' ~ entity.currency.symbol}, null, language) |raw|nl2br }}{% if entity.paymentDate|date("Ymd") > "now"|date("Ymd") %}{{ 'invoices.check-attachment-payment-date'|trans({'%payment-date%': entity.paymentDate|date("Y-m-d")}, null, language) |raw|nl2br }}{% endif %}. {{ 'invoices.check-attachment-document'|trans({}, null, language) |raw|nl2br }}{% if entity.paymentMethod == 'transfer' and issuer.bankAccountNumber %}{{ 'invoices.check-attachment-payments-data'|trans({}, null, language) }}.{% endif %}</p>
            {% else %}
                <p>{{ 'invoices.check-attachment'|trans({'%issue-date%': entity.invoiceDate|date("Y-m-d"), '%number%': entity.number, '%amount%': entity.totalGross|number_format(2)  ~ ' ' ~ entity.currency.symbol}, null, language) |raw|nl2br }}{% if entity.paymentDate|date("Ymd") > "now"|date("Ymd") %}{{ 'invoices.check-attachment-payment-date'|trans({'%payment-date%': entity.paymentDate|date("Y-m-d")}, null, language) |raw|nl2br }}{% endif %}. {{ 'invoices.check-attachment-document'|trans({}, null, language) |raw|nl2br }}{% if entity.paymentMethod == 'transfer' and issuer.bankAccountNumber %}{{ 'invoices.check-attachment-payments-data'|trans({}, null, language) }}.{% endif %}</p>
            {% endif %}
        </td>
    </tr>
    {% if entity.paymentMethod == 'transfer' and issuer.bankAccountNumber %}
        <tr>
            <td style="padding: 20px; background-color: #fff; color: #000">
                <h3 style="color: #000">
                    {{ 'invoices.bank-transfer.details'|trans({}, null, language) }}:
                </h3>
                <hr>
                <p style="color: #000">
                    <strong>{{ 'transfer.bank-account.number'|trans({}, null, language) }}:</strong> {{ issuer.bankAccountNumber }}
                </p>
                <p style="color: #000">
                    <strong>{{ 'transfer.recipient'|trans({}, null, language) }}:</strong> {{ issuer.name }}
                </p>
                <p style="color: #000">
                    <strong>{{ 'transfer.title'|trans({}, null, language) }}:</strong> {{ entity.number }}
                </p>
                <p style="color: #000">
                    <strong>{{ 'transfer.amount'|trans({}, null, language) }}
                        :</strong> {{ entity.price|number_format(2)  ~ ' ' ~ entity.currency.symbol }}
                </p>
            </td>
        </tr>
    {% endif %}
    <tr>
        <td style="padding: 20px; background-color: #fff; color: #000">
            <p>
                {{ 'email.greetings'|trans({}, null, language) }}, <br/>
                {{ issuer.name }}
            </p>
        </td>
    </tr>
{% endblock body %}
