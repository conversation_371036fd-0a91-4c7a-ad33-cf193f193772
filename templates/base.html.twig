<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=0">

        <title>
            {% block title %}
                {{ app_name }}
            {% endblock %}
        </title>

        <link rel="icon" type="image/png" href="{{ asset(brandingAssets ~ "/favicon.ico") }}">
        <meta name="msapplication-TileColor" content="#ffffff">
        <meta name="msapplication-TileImage" content="{{ asset(brandingAssets ~ "/favicon.ico") }}">
        <meta name="theme-color" content="#ffffff">

        {# Run `composer require symfony/webpack-encore-bundle`
           and uncomment the following Encore helpers to start using Symfony UX #}
        {% block stylesheets %}
            <link href="{{ asset(brandingAssets ~ '/css/style.css') }}" rel="stylesheet"/>
        {% endblock %}

        {% block javascripts %}
            {#{{ encore_entry_script_tags('app') }}#}
        {% endblock %}
    </head>
    <body>
        {% block fos_user_content %}
        {% endblock %}
    </body>
</html>
