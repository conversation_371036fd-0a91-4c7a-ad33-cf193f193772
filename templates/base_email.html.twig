{% set backgroundColor = '#d4d4d4' %}
{% set footerColor = '#263238' %}

{% set iosUrlLogo = domainName ~ asset(assetsDir ~ '/badges/' ~ language ~ '/appstore.png') %}
{% set androidUrlLogo = domainName ~ asset(assetsDir ~ '/badges/' ~ language ~ '/googleplay.png') %}
{% set issuerLogo = domainName ~ asset(brandingAssets ~ '/logo.png') %}

<html>
<head>
    <title></title>
</head>

<body style="background-color: {{ backgroundColor }}; padding: 0 0 20px 0; margin: 0; font-family: Arial;">
<table align="center" border="0" cellpadding="0" cellspacing="0" id="wrapper" style="width: 100%; max-width: 740px; margin-top: 20px; line-height: 120%;">
    <tbody>
    <tr>
        {% block header %}
            <td style="background-color: {{ color.primary }}">
                <table border="0" cellpadding="0" cellspacing="0">
                    <tbody>
                    <tr>
                        <td style="padding: 10px 10px 10px 20px">
                            {% block topLogo %}
                                <a href="{{ domainName }}">
                                    <img alt="{{ issuer.name }}" src="{{ issuerLogo }}" style="max-height: 30px" />
                                </a>
                            {% endblock topLogo %}
                        </td>
                        <td style="padding: 20px 20px 16px 10px;">
                            {% block slogan %}
                            {% endblock slogan %}
                        </td>
                    </tr>
                    </tbody>
                </table>
            </td>
        {% endblock header %}
    </tr>

    {% block body %}
    {% endblock body %}

    {% block footer %}
        {# MOBILE APP DOWNLOAD #}
        <tr style="padding-top: 10px; padding-bottom: 10px; background-color: {{ color.primary  }}">
            <td style="display: inline-block; padding-right: 15px; padding-left: 15px; padding-top: 10px; padding-bottom: 10px">
                <table border="0" cellpadding="8" cellspacing="0" style="width: 100%;" width="100%">
                    <tbody>
                    <tr style="background-color: {{ color.primary  }}">
                        {% block mobileAppDownload %}
                            <a href="{{ mobileApp.android }}">
                                <img class="badge-android" style="height:40px;"
                                     src="{{ androidUrlLogo }}" alt="Google Play">
                            </a>
                            <a href="{{ mobileApp.iOS }}">
                                <img class="badge-ios" style="height:40px;"
                                     src="{{ iosUrlLogo }}" alt="Apple App Store">
                            </a>
                        {% endblock mobileAppDownload %}
                    </tr>
                    </tbody>
                </table>
            </td>
        </tr>

        {#  FOOTER  #}
        <tr>
            <td>
                <table border="0" cellpadding="8" cellspacing="0" style="width: 100%;" width="100%">
                    <tbody>
                    <tr>
                        <td style="background: {{ footerColor }}; vertical-align: top; padding-left: 20px; width: 33%; font-size: 10pt; color: {{ backgroundColor }};" valign="top">
                            <strong style="color: {{ backgroundColor }} ">{{ issuer.name }}</strong><br />
                            {{ issuer.address }}<br />
                            {{ issuer.code }} {{  issuer.city }}
                        </td>
                        <td align="right" style="color: white; background: {{  footerColor }};
                                vertical-align: middle; padding-right: 10px; font-size:
                                10pt; text-align: right;"
                            valign="middle">
                        </td>
                        <td align="right" style="color: white; text-align: right; vertical-align: middle;
                                background: {{ footerColor }};
                                padding-right: 20px; width: 40px;" valign="middle">
                        </td>
                    </tr>
                    </tbody>
                </table>
            </td>
        </tr>
    {% endblock footer %}
    </tbody>
</table>
</body>
</html>