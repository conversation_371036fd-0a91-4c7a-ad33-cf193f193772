<?php

declare(strict_types=1);

use <PERSON>\CodeQuality\Rector\Class_\InlineConstructorDefaultToPropertyRector;
use <PERSON>\Php81\Rector\ClassConst\FinalizePublicClassConstantRector;
use <PERSON>\Config\RectorConfig;
use <PERSON>\Set\ValueObject\LevelSetList;
use <PERSON>\Set\ValueObject\SetList;

return static function (RectorConfig $rectorConfig): void {
    $rectorConfig->paths([
        __DIR__ . '/src/Entity/'
    ]);

    // register a single rule

//    $rectorConfig->rule(InlineConstructorDefaultToPropertyRector::class);
//    $rectorConfig->rule(FinalizePublicClassConstantRector::class);

    // define sets of rules
        $rectorConfig->sets([
            SetList::CODE_QUALITY,
            Setlist::DEAD_CODE,
        ]);

};
