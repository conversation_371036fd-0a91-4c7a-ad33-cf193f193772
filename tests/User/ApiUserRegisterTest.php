<?php

namespace App\Tests\User;

use App\Entity\User;
use Hautelook\AliceBundle\PhpUnit\RefreshDatabaseTrait;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;

class ApiUserRegisterTest extends WebTestCase
{
    use RefreshDatabaseTrait;

    private const API_REGISTER_URL = '/api/users/registers';

    public function testEmptyRegister(): void
    {
        $client = static::createClient();
        $client->request('POST', self::API_REGISTER_URL);

        $response = $client->getResponse()->getContent();

        self::assertResponseIsUnprocessable();
        self::assertJson($response);
        self::assertEquals($this->incorrectLoginJson(), $response);
    }

    private function incorrectLoginJson(string $message = "No email"): string
    {
        $response = [
            'status'  => false,
            'message' => $message,
        ];
        return json_encode($response);
    }

    private function correctLoginJson(): string
    {
        $response = [
            'status' => true,
        ];
        return json_encode($response);
    }

    public function testIncorrectEmailRegister(): void
    {
        $client = static::createClient();

        $loginData = [
            'email'         => 'fake@<EMAIL>',
            'plainPassword' => 'fakePassword',
            'lang'          => 'pl',
            'token'         => 'randomTokenNotUsed',
        ];

        $client->request('POST', self::API_REGISTER_URL, [], [], [], json_encode($loginData));

        $response = $client->getResponse()->getContent();

        self::assertResponseIsUnprocessable();
        self::assertJson($response);
        self::assertEquals($this->incorrectLoginJson(), $response);
    }

    public function testToShortPasswordRegister(): void
    {
        $client = static::createClient();

        $loginData = [
            'email'         => '<EMAIL>',
            'plainPassword' => 'fake',
            'lang'          => 'pl',
            'token'         => 'randomTokenNotUsed',
        ];

        $client->request('POST', self::API_REGISTER_URL, [], [], [], json_encode($loginData));

        $response = $client->getResponse()->getContent();

        self::assertResponseIsUnprocessable();
        self::assertJson($response);
        self::assertEquals($this->incorrectLoginJson(), $response);
    }

    public function testCorrectUserRegister(): void
    {
        $client = static::createClient();

        $loginData = [
            'email'         => '<EMAIL>',
            'plainPassword' => 'fakePassword',
            'lang'          => 'pl',
            'token'         => 'randomTokenNotUsed',
            'newsletter'    => true,
        ];

        $client->request('POST', self::API_REGISTER_URL, [], [], [], json_encode($loginData));

        $response = $client->getResponse()->getContent();

        self::assertResponseIsSuccessful();
        self::assertJson($response);
        self::assertEquals($this->correctLoginJson(), $response);

        $entityManager = self::getContainer()->get('doctrine')->getManager();

        /** @var User $user */
        $user = $entityManager->getRepository(User::class)->findOneBy(['email' => '<EMAIL>']);

        self::assertNotNull($user);
        self::assertSame($user->getNewsletterAccepted(), true);
        self::assertSame($user->isVerified(), false);
    }


    public function testExistingUserRegister(): void
    {
        $client = static::createClient();

        $loginData = [
            'email'         => '<EMAIL>',
            'plainPassword' => 'fakePassword',
            'lang'          => 'pl',
            'token'         => 'randomTokenNotUsed',
            'newsletter'    => true,
        ];

        $client->request('POST', self::API_REGISTER_URL, [], [], [], json_encode($loginData));

        $response = $client->getResponse()->getContent();

        self::assertJson($response);
        self::assertEquals($this->incorrectLoginJson("Twoje konto już istnieje w systemie"), $response);
    }
}
