<?php

namespace App\Tests\User;

use App\Entity\ResetPasswordRequest;
use Hautelook\AliceBundle\PhpUnit\RefreshDatabaseTrait;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;

class ApiUserResetPasswordTest extends WebTestCase
{
    use RefreshDatabaseTrait;

    private const API_RESET_URL = '/api/users/resets';
    private const WEB_CHANGE_PASSWORD_URL = '/reset-password/reset/';

    public KernelBrowser $client;

    public function setUp(): void
    {
        parent::setUp(); // TODO: Change the autogenerated stub
        $this->client = self::createClient();
    }

    public function testEmptyResetPassword(): void
    {
        $client = $this->client;
        $client->request('POST', self::API_RESET_URL);

        $response = $client->getResponse()->getContent();

        self::assertResponseStatusCodeSame(400);
        self::assertJson($response);
        self::assertEquals($this->incorrectLoginJson(), $response);
    }

    public function testNotExistingUserReset(): void
    {
        $client = $this->client;

        $loginData = [
            'email' => '<EMAIL>',
        ];

        $client->request('POST', self::API_RESET_URL, [], [], [], json_encode($loginData));

        $response = $client->getResponse()->getContent();

        self::assertResponseIsSuccessful();
        self::assertJson($response);
        self::assertEquals('[]', $response);
    }

    public function testCorrectUserReset(): void
    {
        $entityManager = self::getContainer()->get('doctrine')->getManager();
        /** @var ResetPasswordRequest $user */
        $resetRequest = $entityManager->getRepository(ResetPasswordRequest::class)->findAll ();
        self::assertCount(0, $resetRequest);

        $client = $this->client;

        $loginData = [
            'email' => '<EMAIL>',
        ];

        $client->request('POST', self::API_RESET_URL, [], [], [], json_encode($loginData));

        $response = $client->getResponse()->getContent();

        self::assertResponseIsSuccessful();
        self::assertJson($response);
        self::assertEquals('[]', $response);



        /** @var ResetPasswordRequest $user */
        $resetRequest = $entityManager->getRepository(ResetPasswordRequest::class)->findAll ();

        self::assertCount(1, $resetRequest);
        self::assertSame('<EMAIL>', $resetRequest[0]->getUser()->getEmail());
        self::assertFalse($resetRequest[0]->isExpired());
    }

    private function incorrectLoginJson(string $message = "no data"): string
    {
        $response = [
            'error' => $message,
        ];
        return json_encode($response);
    }
}