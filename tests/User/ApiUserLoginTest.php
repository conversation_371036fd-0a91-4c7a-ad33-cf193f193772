<?php

namespace App\Tests\User;

use App\Entity\Token;
use App\Entity\User;
use Hautelook\AliceBundle\PhpUnit\RefreshDatabaseTrait;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;

class ApiUserLoginTest extends WebTestCase
{
    use RefreshDatabaseTrait;

    private const API_LOGIN_URL = '/api/v3/user/login';

    public function testEmptyLogin(): void
    {
        $client = static::createClient();
        $client->request('POST', self::API_LOGIN_URL);
        self::assertEquals(401 , $client->getResponse()->getStatusCode());
    }


    public function testIncorrectUser(): void
    {
        $client = static::createClient();

        $loginData = [
            'email'         => '<EMAIL>',
            'plainPassword' => 'fakePassword',
        ];

        $client->request('POST', self::API_LOGIN_URL, [], [], [], json_encode($loginData));

        $response = $client->getResponse()->getContent();

        self::assertEquals(401 , $client->getResponse()->getStatusCode());

    }

    public function testCorrectUserAndIncorrectPassword(): void
    {
        $client = static::createClient();

        $loginData = [
            'email'         => '<EMAIL>',
            'plainPassword' => 'testPassword1',
        ];

        $client->request('POST', self::API_LOGIN_URL, [], [], [], json_encode($loginData));

        $response = $client->getResponse()->getContent();

        self::assertEquals(401 , $client->getResponse()->getStatusCode());

    }

    public function testCorrectUserAndPassword(): void
    {
        $client = static::createClient();

        $loginData = [
            'email'         => '<EMAIL>',
            'plainPassword' => 'testPassword',
        ];

        $client->request('POST', self::API_LOGIN_URL, [], [], [], json_encode($loginData));

        $response = $client->getResponse()->getContent();

        self::assertResponseIsSuccessful();
        self::assertJson($response);
        $contentArray = json_decode($response, true);
        static::assertArrayHasKey('status', $contentArray);
        static::assertArrayHasKey('token', $contentArray);
        static::assertArrayHasKey('tokenV2', $contentArray);
        static::assertArrayHasKey('userId', $contentArray);
        static::assertArrayHasKey('isFleetManager', $contentArray);
        static::assertArrayHasKey('isTrustedPartner', $contentArray);
        static::assertArrayHasKey('isUsingFleetApplication', $contentArray);
        static::assertArrayHasKey('terms', $contentArray);
    }

    public function testUnverifiedUser(): void
    {
        $client = static::createClient();

        $loginData = [
            'email'         => '<EMAIL>',
            'plainPassword' => 'testPassword',
        ];

        $client->request('POST', self::API_LOGIN_URL, [], [], [], json_encode($loginData));

        $response = $client->getResponse()->getContent();

        self::assertEquals(401 , $client->getResponse()->getStatusCode());

    }

    public function testLoginDeletedUser(): void
    {
        $client = static::createClient();

        $loginData = [
            'email'         => '<EMAIL>',
            'plainPassword' => 'testPassword',
        ];

        $client->request('POST', self::API_LOGIN_URL, [], [], [], json_encode($loginData));


        self::assertEquals(401 , $client->getResponse()->getStatusCode());

    }
}
