<?php

namespace App\Tests\Controller;

use App\Controller\User\MobilePaymentController;
use Doctrine\ORM\EntityManager;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;

/**
 * Test for Mobile Payments API
 * "Protokół komunikacji pomiędzy systemami BKF i zewnętrznymi systemami płatniczymi"
 *
 * covers payments type: BkfPay, Alpha(and others similar), Credit Card is separated tested(is not called from this API)
 *
 * Class MobilePaymentControllerTest
 * @package BKFPayBundle\Tests\Controller
 */
class MobilePaymentControllerTest extends WebTestCase
{
    protected static KernelBrowser $client;

    /**
     * @var EntityManager
     */
    protected static $em;

    public function setUp(): void
    {
        self::$client = self::createClient();
        self::$client->setServerParameter('HTTP_AUTHORIZATION', '1234567890');
        self::$em = static::$kernel->getContainer()->get('doctrine')->getManager();
    }

    /**
     * @covers MobilePaymentController::indexAction()
     */
    public function testInitPaymentBkfPaySuccessful()
    {
        $timestamp = date('Y-m-d H:i:s');
        $content = json_encode(
            [
            "methodId" => 'initPayment',
            "standId" => "00150019",
            "pay_system" => "BKFPAY",
            "timestamp" => $timestamp,
            "value" => "1"
                ]
        );

        self::$client->request(
            'POST',
            '/api/users/mobilepayment',
            [],
            [],
            [],
            $content
        );

        $response = self::$client->getResponse();

        $content = $response->getContent();
        $contentArray = json_decode($content, true);

        static::assertEquals('success', $contentArray['status'], json_encode($contentArray) ?? '');

        return $contentArray['paymentId'] ?? null;
    }

    /**
     * @covers MobilePaymentController::indexAction()
     */
    public function testAvailableCarwashes()
    {

        $content = json_encode(
            [
                "methodId" => 'availableCarwashes',
            ]
        );

        self::$client->request(
            'POST',
            '/api/users/mobilepayment',
            [],
            [],
            [],
            $content
        );

        $response = self::$client->getResponse();

        $content = $response->getContent();
        $contentArray = json_decode($content, true);

        static::assertEquals('success', $contentArray['status'], json_encode($contentArray) ?? '');
    }
}
