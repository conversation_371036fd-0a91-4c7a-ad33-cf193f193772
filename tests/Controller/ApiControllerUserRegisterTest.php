<?php

namespace BKFPayBundle\Tests\Controller;

use App\Entity\User;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Bundle\FrameworkBundle\Client;

/**
 * Class ApiControllerUserRegisterTest
 *
 * @package BKFPayBundle\Tests\Controller
 */
class ApiControllerUserRegisterTest extends WebTestCase
{
    /** @var Client $client * */
    protected static $client;

    protected static $em;

    public function setUp(): void
    {
        self::$client = self::createClient();

        self::$em = self::getContainer()->get('doctrine')->getManager();
    }

    public function testRegisterNewUserSuccess()
    {
        $email = '<EMAIL>';
        $plainPassword = 'test12';

        $token =  hash('sha256', $email . '=' . $plainPassword . '=userRegister');

        self::$client->request(
            'POST',
            '/api/users/registers',
            [],
            [],
            [],
            json_encode(
                [
                    'email' => $email,
                    'plainPassword' => $plainPassword,
                    'token' => $token
                ]
            )
        );

        $response = self::$client->getResponse();
        $content = $response->getContent();
        $contentArray = json_decode($content, true);

        static::assertTrue($contentArray['status']);
    }

    /**
     * @depends testRegisterNewUserSuccess
     */
    public function testRegisterExistingUserFail()
    {
        $email = '<EMAIL>';
        $plainPassword = 'test';

        $token =  hash('sha256', $email . '=' . $plainPassword . '=userRegister');

        self::$client->request(
            'POST',
            '/api/users/registers',
            [
                'email' => $email,
                'plainPassword' => $plainPassword,
                'token' => $token
            ]
        );

        $response = self::$client->getResponse();
        $content = $response->getContent();
        $contentArray = json_decode($content, true);

        static::assertFalse($contentArray['status']);
    }

}
