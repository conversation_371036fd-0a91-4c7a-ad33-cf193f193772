<?php

namespace App\Tests\Controller;

use DateTime;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;

class LoyalAppPromotionalCodesControllerTest extends WebTestCase
{
    const USER_TOKEN = 'tokenCmApi';
    const USER_NON_MANAGER_TOKEN = 'user2Token';
    const USER_NON_NON_EXZISTING_TOKEN = 'sdfgsdfgdSDFd3454';

    /**
     *  test new promotion code
     * @group promotialcodes
     */
    public function testNewCodeUnegzistingUserAction()
    {
        $client = static::createClient();
        $client->setServerParameter('HTTP_Authorization', self::USER_NON_MANAGER_TOKEN);

        $client->request(
            'POST',
            '/api/admin/promotionalcodes',
            [],
            [],
            [],
            json_encode(
                [
                    'value' => 10,
                    'name' => 'test',
                    'type' => 1,
                    'quantity' => 1,
                    'startDate' => '2019-01-01',
                    'expirationDate' => '2019-02-02'
                ]
            )
        );

        /** @var Response $response */
        $response = $client->getResponse();

        static::assertEquals(403, $response->getStatusCode());
    }


    /**
     *  test new promotion code
     * @group promotialcodes
     */
    public function testNewCodeUserWithoutRoleAction()
    {
        $client = static::createClient();
        $client->setServerParameter('HTTP_Authorization', self::USER_NON_NON_EXZISTING_TOKEN);

        $client->request(
            'POST',
            '/api/admin/promotionalcodes',
            [],
            [],
            [],
            json_encode(
                [
                    'value' => 10,
                    'name' => 'test',
                    'type' => 1,
                    'quantity' => 1,
                    'startDate' => '2019-01-01',
                    'expirationDate' => '2019-02-02'
                ]
            )
        );

        /** @var Response $response */
        $response = $client->getResponse();

        static::assertEquals(401, $response->getStatusCode());
    }

    /**
     *  test new promotion code
     * @group promotialcodes
     */
    public function testListCodeBPAction()
    {
        $client = static::createClient();
        $client->setServerParameter('HTTP_Authorization', self::USER_TOKEN);

        $client->request(
            'POST',
            '/api/admin/promotionalcodes',
            [],
            [],
            [],
            json_encode(
                [
                    'value' => 10,
                    'name' => 'test',
                    'type' => 1,
                    'quantity' => 1,
                    'startDate' => '2019-01-01',
                    'expirationDate' => '2019-02-02'
                ]
            )
        );

        $response = $client->getResponse();

        $content = $response->getContent();
        $contentArray = json_decode($content, true);
        static::assertEquals(200, $response->getStatusCode());

        static::assertEquals(
            true,
            $contentArray
        );


        $client->setServerParameter('HTTP_Authorization', self::USER_TOKEN);

        $queryParams = http_build_query(
            [
                'page' => 1,
                'orderBy' => 'lastUpdate',
                'orderDescending' => 1,
                'itemsPerPage' => 25,
                'dateFrom' => (new DateTime('now'))
                    ->setTime(0, 0, 0)->format('Y-m-d H:i:s'),
                'dateTo' => (new DateTime('now'))
                    ->setTime(23, 59, 59)->format('Y-m-d H:i:s'),
            ]
        );

        $client->request(
            'GET',
            '/api/admin/promotionalcodes?' . $queryParams,

        );

        /** @var Response $response */
        $response = $client->getResponse();

        $content = $response->getContent();
        $contentArray = json_decode($content, true);

        static::assertEquals(200, $response->getStatusCode());
        static::assertArrayHasKey('items', $contentArray);
        static::assertArrayHasKey('count', $contentArray);
        //static::assertCount(1, $contentArray['items']);
    }
}
