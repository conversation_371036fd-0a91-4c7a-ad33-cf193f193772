<?php

namespace App\Tests\Controller\Rest;

use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Bundle\FrameworkBundle\Client;

/**
 * Class FleetUserControllerTest
 *
 * @package BKFPayBundle\Tests\Controller\Rest
 */
class FleetUserControllerFleetUserTest extends WebTestCase
{
    protected static $em;

    /**
     * @var Client
     */
    protected static $clientFleetManager;

    public static function setUpBeforeClass(): void
    {
        self::$clientFleetManager = static::createClient();
        self::$clientFleetManager->setServerParameter('HTTP_Authorization', 'userFleetTrustedManagerToken');
    }

    /**
     * @return array
     */
    public function testCgetFleetUsersListSuccess()
    {
        self::$clientFleetManager->request(
            'GET',
            '/api/users/fleet/users'
        );

        $response = self::$clientFleetManager->getResponse();
        $content = $response->getContent();
        $contentArray = json_decode($content, true);

        static::assertEquals(200, $response->getStatusCode(), $content);
        static::assertJson($content);

        static::assertArrayHasKey('items', $contentArray);
        static::assertArrayHasKey('totalItems', $contentArray);
        static::assertArrayHasKey(0, $contentArray['items']);
        $item = $contentArray['items'][0];

        static::assertArrayHasKey('id',$item);
        static::assertArrayHasKey('email',$item);
    }
}
