<?php

namespace App\Tests\Controller\Rest;

use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use PHPUnit\Framework\Attributes\DataProvider;

/**
 * Class UserControllerTest
 *
 * @package BKFPayBundle\Tests\Controller\Rest
 */
class UserControllerTest extends WebTestCase
{
    const NOT_ASSIGNED_CLIENT_USER_ID = 4;
    const FLEET_USER_MEMBER = 6;

    const USER_TOKENS = [
        1 => '**********',
        2 => 'user2Token',
        3 => 'user3token',
        6 => 'userFleetMemberToken'
    ];

    protected static $em;

    protected KernelBrowser $client;

    public function setUp(): void
    {
        parent::setUp(); // TODO: Change the autogenerated stub

        $this->client = static::createClient();
    }

    /**
     * Check if user get from database after fixtures are generated is the same as returned from method
     */
    public function testGetInvoiceData()
    {
        $client = $this->client;
        $client->setServerParameter('HTTP_Authorization', self::USER_TOKENS[2]);

        $client->request(
            'GET',
            '/api/users/invoice_data',
            [
                'user_id' => 1
            ]
        );

        $response = $client->getResponse();
        $content = $response->getContent();
        $contentArray = json_decode($content, true);

        static::assertEquals(200, $response->getStatusCode(), $content);
        static::assertJson($content);
        static::assertArrayHasKey('name', $contentArray);
        static::assertArrayHasKey('address', $contentArray);
        static::assertArrayHasKey('post_code', $contentArray);
        static::assertArrayHasKey('city', $contentArray);
    }

    /**
     * @return array
     */
    public static function getCardsBeforeDeleteProvider()
    {
        return [
            [
                2, //UserId
                [
                    'trans' => [
                        [
                            'type_key' => 'BKFPAY',
                            'balance' => 437.27,
                            'end_time' => null,
                        ],
                    ],
                    'transSum' => 437.27
                ],
            ],
        ];
    }

    #[DataProvider('getCardsBeforeDeleteProvider')]
    public function testGetCardsBeforeDelete($userId, $expectedResponse)
    {
        $client = $this->client;
        $client->setServerParameter('HTTP_Authorization', self::USER_TOKENS[$userId]);

        $client->request(
            'GET',
            '/api/users/cards'
        );

        $response = $client->getResponse();
        $content = $response->getContent();
        $contentArray = json_decode($content, true);

        static::assertEquals(200, $response->getStatusCode(), $content);
        static::assertJson($content);

        static::assertEquals(
            $expectedResponse,
            $contentArray,
            $content
        );
    }

    /**
     * @return array
     */
    public static function getCardsAfterDeleteProvider()
    {
        return [
            [
                2, //UserId
                [
                    'trans' => [
                        [
                            'type_key' => 'BKFPAY',
                            'balance' => 437.27,
                            'end_time' => null,
                        ],
                    ],
                    'transSum' => 437.27
                ],
            ],
        ];
    }

    #[DataProvider('getCardsAfterDeleteProvider')]
    public function testGetCardsAfterDelete($userId, $expectedResponse)
    {
        $client = $this->client;
        $client->setServerParameter('HTTP_Authorization', self::USER_TOKENS[$userId]);

        $client->request(
            'GET',
            '/api/users/cards'
        );

        $response = $client->getResponse();
        $content = $response->getContent();
        $contentArray = json_decode($content, true);

        static::assertEquals(200, $response->getStatusCode(), $content);
        static::assertJson($content);

        static::assertEquals(
            $expectedResponse,
            $contentArray
        );
    }

    public function testGetSettingsCountriesAction()
    {
        $client = $this->client;
        $client->setServerParameter('HTTP_Authorization', self::USER_TOKENS[1]);

        $client->request(
            'GET',
            '/api/users/countries'
        );

        $response = $client->getResponse();
        $content = $response->getContent();
        $contentArray = json_decode($content, true);

        static::assertEquals(200, $response->getStatusCode(), $content);
        static::assertJson($content);
        static::assertEquals(
            [
                [
                    'short_name' => 'PL',
                    'name' => 'Polska'
                ]
            ],
            $contentArray,
            $response
        );
    }

    /**
     * @depends testEditInvoiceDataForUserWithoutAssignedClientAction
     */
    public static function tearDownAfterClass(): void
    {
        return ;
        $client = self::$em->getRepository(Clients::class)->findOneBy(['nip' => '1000000000']);
        if (isset($client)) {
            self::$em->remove($client);
        }

        $client = self::$em->getRepository(Clients::class)->findOneBy(['nip' => '1000000012']);
        if (isset($client)) {
            self::$em->remove($client);
        }

        $user = self::$em->getRepository(User::class)->findOneBy(['id' => self::NOT_ASSIGNED_CLIENT_USER_ID]);
        if (!empty($user)) {
            $user->setClient(null);
            self::$em->persist($user);
        }

        $user = self::$em->getRepository(User::class)->findOneBy(['id' => 1]);
        if (!empty($user)) {
            $client = self::$em->getRepository(Clients::class)->find(1);
            $user->setClient($client);
            self::$em->persist($user);
        }

        self::$em->flush();
    }
}
