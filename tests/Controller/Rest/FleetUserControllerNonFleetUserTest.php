<?php

namespace App\Tests\Controller\Rest;

use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Bundle\FrameworkBundle\Client;

/**
 * Class FleetUserControllerTest
 *
 * @package BKFPayBundle\Tests\Controller\Rest
 */
class FleetUserControllerNonFleetUserTest extends WebTestCase
{
    /**
     * @var Client
     */
    protected static $client;

    public static function setUpBeforeClass(): void
    {
        self::$client = static::createClient();
        self::$client->setServerParameter('HTTP_Authorization', '1234567890');
    }

    /**
     * @return array
     */
    public function testCgetFleetUsersListSuccess()
    {
        self::$client->request(
            'GET',
            '/api/users/fleet/users'
        );

        $response = self::$client->getResponse();
        $content = $response->getContent();
        $contentArray = json_decode($content, true);

        static::assertEquals(403, $response->getStatusCode(), $content);
        static::assertJson($content);

        static::assertEquals(
            [
                'error' => true,
                'error_code' => 'no_permissions_to_get_users',
                'error_description' => 'Brak uprawnień'
            ],
            $contentArray
        );
    }
}
