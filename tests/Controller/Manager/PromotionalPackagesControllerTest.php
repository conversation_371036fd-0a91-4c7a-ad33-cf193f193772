<?php

namespace App\Tests\Controller\Manager;

use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;

/**
 * Class UserControllerTest
 *
 * @package App\Tests\Controller\Rest\Manager
 */
class PromotionalPackagesControllerTest extends WebTestCase
{
    const USER_TOKEN = 'tokenCmApi';
    const USER_NON_MANAGER_TOKEN = 'user2Token';

    protected static KernelBrowser $client;

    /**
     */
    public function testUserListSuccessData()
    {
        $client = static::createClient();
        $client->setServerParameter('HTTP_Authorization', self::USER_TOKEN);

        $client->request(
            'GET',
            '/api/admin/packages'
        );

        $response = $client->getResponse();
        $content = $response->getContent();
        $contentArray = json_decode($content, true);

        static::assertEquals(200, $response->getStatusCode(), $content);
        static::assertJson($content);
    }

    /**
     */
    public function testUserListFailData()
    {
        $client = static::createClient();
        $client->setServerParameter('HTTP_Authorization', self::USER_NON_MANAGER_TOKEN);

        $client->request(
            'GET',
            '/api/admin/packages'
        );

        $response = $client->getResponse();
        $content = $response->getContent();

        static::assertEquals(403, $response->getStatusCode(), $content);
    }
}
