<?php

namespace App\Tests;

use App\Entity\Enum\MobilePaymentStatus;
use App\Entity\ExternalPayment;
use App\Entity\MobilePayment;
use App\Repository\ExternalPaymentRepository;
use App\Repository\MobilePaymentRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;

use function PHPUnit\Framework\assertEquals;

class MobilePaymentEspagoTest extends WebTestCase
{
    /** @var Client $client * */
    protected static $client;
    /** @var EntityManagerInterface */
    protected static $em;

    /** @var MobilePaymentRepository */
    protected static $mobilePaymentRepository;
    public function setUp(): void
    {
        self::$client = self::createClient();
        self::$client->setServerParameter('HTTP_AUTHORIZATION', '1234567890');
        self::$em = self::$client->getContainer()->get('doctrine.orm.entity_manager');
        self::$mobilePaymentRepository = self::$em->getRepository(MobilePayment::class);
    }

    public function testRegisterCard()
    {
        $contentArray = $this->makePost(
            '/api/users/cards/register',

                [
                    "first_name"     => 'Michał',
                    "last_name"   => "Łukasik",
                    "number"  => "4242 4242 4242 4242", // zgodnie z dokumentacja sandbox
                    "verification_value" => "999",
                    "year"     => "2028",
                    "month"       => "02"
                ]
        );

        static::assertArrayHasKey("card_token", $contentArray);
        return $contentArray;
    }

    public function testMakePayment(){

        $cardRegister = $this->testRegisterCard();



        $contentArray = $this->makePost('/api/users/mobilepayment',
                [
                    "methodId"     => 'initPayment',
                    "pay_system"     => 'CREDIT_CARD',
                    "appName"   => "test",
                    "appVersion"   => "v0.0.0",
                    "timestamp"   => (new \DateTime())->getTimestamp(),
                    "standId"   => "00150019",
                    "value"     => 1,
                    "skip3DSecure" => true,
                    "token" => $cardRegister['card_token']
                ]
        );

        static::assertArrayHasKey("status", $contentArray);
        static::assertEquals('success', $contentArray["status"]);

        $mp = self::$mobilePaymentRepository->find($contentArray["paymentId"]);

        self::assertEquals(MobilePaymentStatus::INITIATED, $mp->getStatus());
        return $mp;
    }

    public function testBackRequest(){
        $mp = $this->testMakePayment();

        $this->makePost("/external_payment/espago/back_request", [
            "id"=> $mp->getExternalPayment()->getExternalId(),
            "state" => "executed"
        ]);
        self::$em->refresh($mp);

        self::assertEquals(MobilePaymentStatus::CONFIRMED, $mp->getStatus());
    }

    public function makePost($url, $content){
        self::$client->request(
            'POST',
            $url,
            [

            ],
            [],
            [],
            json_encode($content)
        );

       $response = self::$client->getResponse();
       $content = $response->getContent();

       return json_decode($content, true);
    }

}