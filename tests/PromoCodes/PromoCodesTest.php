<?php

namespace App\Tests\PromoCodes;

use Hautelook\AliceBundle\PhpUnit\RefreshDatabaseTrait;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;

class PromoCodesTest extends WebTestCase
{
    use RefreshDatabaseTrait;

    private const API_URL = '/api/users/topups/promotional';

    public function testEmptyCodePromoCode(): void
    {
        $client = static::createClient();
        $client->setServerParameter('HTTP_Authorization', '1234567890');
        $client->jsonRequest('POST', self::API_URL, ['code' => '']);

        $response = $client->getResponse();

        self::assertResponseStatusCodeSame(400);
        self::assertJson($response->getContent());
        self::assertEquals(
            '{"error":"Podano pusty kod promocyjny","error_description":"Podano pusty kod promocyjny"}',
            $response->getContent()
        );
    }

    public function testIncorrectCodePromoCode(): void
    {
        $client = static::createClient();
        $client->setServerParameter('HTTP_Authorization', '1234567890');
        $client->jsonRequest('POST', self::API_URL, ['code' => '1234']);

        $response = $client->getResponse();

        self::assertResponseStatusCodeSame(400);
        self::assertJson($response->getContent());
        self::assertEquals(
            '{"error":"Brak aktywnego kodu promocyjnego","error_description":"Brak aktywnego kodu promocyjnego"}',
            $response->getContent()
        );
    }

    public function testCorrectCodePromoCode(): void
    {
        $client = static::createClient();
        $client->setServerParameter('HTTP_Authorization', '1234567890');
        $client->jsonRequest('POST', self::API_URL, ['code' => '660']);

        $response = $client->getResponse();

        self::assertResponseIsSuccessful();
        self::assertJson($response->getContent());
        self::assertEquals(
            '{"status":true,"message":"Do\u0142adowanie trafi\u0142o na twoje konto"}',
            $response->getContent()
        );
    }
}
