# define your env variables for the test env here
KERNEL_CLASS='App\Kernel'
APP_SECRET='$ecretf0rt3st'
SYMFONY_DEPRECATIONS_HELPER=999999
PANTHER_APP_ENV=panther
PANTHER_ERROR_SCREENSHOT_DIR=./var/error-screenshots

#DATABASE_URL="************************************************/bl-${CI_JOB_ID:-local}?serverVersion=12&charset=utf8"
DATABASE_URL="postgresql://cm_dev:5fGXxPHkd5Sk@127.0.0.1:5432/bl-${CI_JOB_ID:-local}?serverVersion=12&charset=utf8"

SENTRY_DSN=
MAILER_DSN=null://null
APP_ENV=test
BRANDING_CURRENCY_CODE=PLN
BRANDING_TIMEZONE=UTC
BKFPAY_LIMIT_COUNT_10S=0
BKFPAY_LIMIT_COUNT_60S=0
SIMPLE_SUPPORT_EMAIL=1
CREDIT_CARD_PROVIDER=ESPAGO
