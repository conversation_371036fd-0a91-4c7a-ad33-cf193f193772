<?php

namespace App\Support;

class MobileAppSupportReport
{
    private ?string $phone;
    private string $problem;
    private SupportType $type;
    private ?string $platform;
    private ?string $version;

    private ?string $firstName;

    private ?string $lastName;

    private ?string $bankAccount;

    public function getPhone(): ?string
    {
        return $this->phone;
    }

    public function setPhone(?string $phone): MobileAppSupportReport
    {
        $this->phone = $phone;
        return $this;
    }

    public function getProblem(): string
    {
        return $this->problem;
    }

    public function setProblem(string $problem): MobileAppSupportReport
    {
        $this->problem = $problem;
        return $this;
    }

    public function getType(): SupportType
    {
        return $this->type;
    }

    public function setType(SupportType $type): MobileAppSupportReport
    {
        $this->type = $type;
        return $this;
    }

    public function getPlatform(): ?string
    {
        return $this->platform;
    }

    public function setPlatform(?string $platform): MobileAppSupportReport
    {
        $this->platform = $platform;
        return $this;
    }

    public function getVersion(): ?string
    {
        return $this->version;
    }

    public function setVersion(?string $version): MobileAppSupportReport
    {
        $this->version = $version;
        return $this;
    }

    public function getFirstName(): ?string
    {
        return $this->firstName;
    }

    public function setFirstName(?string $firstName): MobileAppSupportReport
    {
        $this->firstName = $firstName;
        return $this;
    }

    public function getLastName(): ?string
    {
        return $this->lastName;
    }

    public function setLastName(?string $lastName): MobileAppSupportReport
    {
        $this->lastName = $lastName;
        return $this;
    }

    public function getBankAccount(): ?string
    {
        return $this->bankAccount;
    }

    public function setBankAccount(?string $bankAccount): MobileAppSupportReport
    {
        $this->bankAccount = $bankAccount;
        return $this;
    }

    public function addProblem(string $problem): MobileAppSupportReport
    {
        $this->problem .= "\n" . $problem;
        return $this;
    }
}
