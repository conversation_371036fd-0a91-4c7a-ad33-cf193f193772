<?php

namespace App\Support;

use App\Entity\MobilePayment;
use App\Entity\User;
use App\Repository\MobilePaymentRepository;
use App\User\UserService;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

class SupportBodyGenerator
{
    public function __construct(
        private TranslatorInterface $translator,
        private MobilePaymentRepository $mobilePaymentRepository,
        private UserService $userService,
        private ParameterBagInterface $parameterBag,
    ) {
    }

    public function getBody(User $user, MobileAppSupportReport $report): string
    {
        return $this->parameterBag->get('simple_support_email') ?
            $this->getSimpleBody($user, $report) :
            $this->getFullBody($user, $report)
            ;
    }
    public function getFullBody(User $user, MobileAppSupportReport $report): string
    {
        return <<<MAILBODY

        {$this->getSimpleBody($user, $report)}

        {$this->getLiveStatusSection($user)}

        {$this->getCardsSection($user)}

        {$this->getPaymentsSection($user)}

        MAILBODY;
    }
    public function getSimpleBody(User $user, MobileAppSupportReport $report): string
    {

        return <<<MAILBODY

        * {$this->translator->trans('email')}: {$user->getEmail()}
        * Id: {$user->getId()}   
        * {$this->translator->trans('phone_number')}: {$report->getPhone()}
        * FleetManager: {$user->getManagerEmail()}
        * {$this->translator->trans('support_ticket_type')}: {$report->getType()->value}
        * {$this->translator->trans('platform')}: {$report->getPlatform()}
        * {$this->translator->trans('version')}: {$report->getVersion()}
               
        
        {$this->getRefundSection($report)}
        
        # {$this->translator->trans('comment')}:

        ```
        {$report->getProblem()}
        ```
        MAILBODY;
    }

    public function getLiveStatusSection(User $user): string
    {

        $status = $user->getLiveStatus();
        $lastTransaction = $status->getLastMobilePayment();
        $section =
            <<<MAILBODY
<details><summary>Status</summary>

* ostatnia udane skanowanie stanowiska UTC: {$status->getLastStandCodeSuccess()} {$status->getLastStandCodeSuccessTime()?->format('Y-m-d H:i:s')}
* ostatnia nieudane skanowanie stanowiska UTC: {$status->getLastStandCodeFail()} {$status->getLastStandCodeFailTime()?->format('Y-m-d H:i:s')}
* ostatnia wersja softu: {$status->getAppVersion()}
* ostatnia transakcja: 
  * issuer: {$lastTransaction?->getIssuer()->name} 
  * czas (UTC): {$lastTransaction?->getTime()?->format('Y-m-d H:i:s')} 
  * wartosc: {$lastTransaction?->getValue()}
  * status: {$lastTransaction?->getStatus()->value}
  
  </details>
MAILBODY;

        return $section;
    }

    public function getCardsSection(User $user): string
    {
        $cards = $this->userService->getUserCards($user);


        $invoiceSection =
            <<<MAILBODY
<details><summary>Saldo klienta</summary>

| Paczka | Wartość | Data ważności |
|---|---|---|

MAILBODY;

        foreach ($cards as $c) {
            $invoiceSection .=
                <<<MAILBODY
|{$c->getTypeKey()->value}|{$c->getBalance()}|{$c->getEndTime()?->format('Y-m-d H:i:s')}|

MAILBODY;
        }

        return $invoiceSection . '</details>';
    }

    private function getRefundSection(MobileAppSupportReport $report): ?string
    {
        if (empty($report->getBankAccount())) {
            return null;
        }

        return
            <<<MAILBODY

# Dane do zwrotu:
* Imię: {$report->getFirstName()}
* Nazwisko: {$report->getLastName()}
* Nr rachunku bankowego do zwrotu: {$report->getBankAccount()}




MAILBODY;
    }

    private function getPaymentsSection(User $user): string
    {
        $mps = $this->mobilePaymentRepository->findPaymentsByDateRangeAndUser(
            $user
        )['items'];

        $invoiceSection =
            <<<MAILBODY
<details><summary>Ostatnie transakcje</summary>

| Id |Status| Data płatności | issuer | Kod stanowiska | Wartośc Brutto | Nr. dokumentu sprzedaży |
|---|---|---|---|---|---|---|

MAILBODY;

        foreach ($mps as $p) {
            /** @var MobilePayment $p */
            $invoiceSection .=
                <<<MAILBODY
|{$p->getId()}|{$p->getStatus()->value}|{$p->getTime()->format('Y-m-d H:i:s')}|{$p->getIssuer()->value}/{$p->getTransactionType()->value}|{$p->getCarwash()?->getSerialNumber()}/{$p->getStandCode()}|{$p->getValue()} {$p->getCurrency()?->getCode()}|{$p->getSalesDocumentType()}/{$p->getSalesDocumentNumber()}|

MAILBODY;
        }

        return $invoiceSection . '</details>';
    }
}
