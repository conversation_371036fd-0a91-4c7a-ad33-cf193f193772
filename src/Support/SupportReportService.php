<?php

namespace App\Support;

use App\Entity\User;
use App\Repository\MobilePaymentRepository;
use App\Repository\TokenRepository;
use App\Repository\UserRepository;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\Mailer\MailerInterface;
use Symfony\Component\Mime\Email;

use function Sentry\captureMessage;

class SupportReportService
{
    public function __construct(
        private ParameterBagInterface $parameterBag,
        private SupportBodyGenerator $bodyGenerator,
        private MailerInterface $mailer,
        private TokenRepository $tokenRepo,
        private MobilePaymentRepository $mobilePaymentRepository,
        private UserRepository $userRepository,
    ) {
    }

    public function supportAction(User $user, MobileAppSupportReport $supportReport): int
    {
        $userEmail = $user->getEmail();
        switch ($supportReport->getType()) {
            case SupportType::DELETE_ACCOUNT:
                $deletedBalance = $this->deleteAccount($user);


                if ($deletedBalance > 0) {
                    $supportReport->addProblem("skasowane srodki: $deletedBalance");
                    $this->generateEmail($userEmail, $user, $supportReport);
                    return 1;
                }
                return 0;

            default:
                $this->generateEmail($userEmail, $user, $supportReport);
                return 1;
        }
    }


    private function deleteAccount(User $user)
    {
        $balance = 0;
        $balance += $this->mobilePaymentRepository->deleteBkfPayBalance($user);
        $balance += $this->mobilePaymentRepository->deleteSubBalance($user);


        $userEmail = $user->getEmail();
        if ($user->getManager() !== null) {
            throw new SupportException('Próba usunięcia konta przez użytkownika flotowego o id ' . $user->getId());
        }

        captureMessage("Usunięcia konta przez użytkownika {$user->getEmail()}, aktualne środki: {$balance}");
        $user->setEmail(time() . '_' . $userEmail);
        $user->setStatus('d');
        $this->tokenRepo->invalidateAllActiveTokensByUser($user);
        $this->userRepository->save($user);
        return $balance;
    }
    public function generateEmail(string $userMail, User $user, MobileAppSupportReport $report)
    {
        $emailObj = (new Email())
            ->subject('Zgłoszenie BELOYAL ' . $userMail)
            ->replyTo($userMail)
            ->from($this->parameterBag->get('mailer_address_no_replay'))
            ->to($this->parameterBag->get('invoice_issuer_support'));

        $text = $this->bodyGenerator->getBody($user, $report);

        $emailObj->text($text);
        $this->mailer->send($emailObj);
    }
}
