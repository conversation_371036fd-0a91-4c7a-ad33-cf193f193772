<?php

namespace App\Model\Auth;

class ResetPasswordDTO
{
    private ?string $token = null;
    private ?string $plainPassword = null;
    private string $plainPasswordRepeated;
    private ?string $lang = null;

    public function getToken(): ?string
    {
        return $this->token;
    }

    public function setToken(?string $token): ResetPasswordDTO
    {
        $this->token = $token;
        return $this;
    }

    public function getPlainPassword(): ?string
    {
        return $this->plainPassword;
    }

    public function setPlainPassword(?string $plainPassword): ResetPasswordDTO
    {
        $this->plainPassword = $plainPassword;
        return $this;
    }

    public function getPlainPasswordRepeated(): ?string
    {
        return $this->plainPasswordRepeated;
    }

    public function setPlainPasswordRepeated(?string $plainPasswordRepeated): ResetPasswordDTO
    {
        $this->plainPasswordRepeated = $plainPasswordRepeated;
        return $this;
    }

    public function getLang(): ?string
    {
        return $this->lang;
    }

    public function setLang(?string $lang): ResetPasswordDTO
    {
        $this->lang = $lang;
        return $this;
    }
}
