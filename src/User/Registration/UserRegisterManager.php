<?php

namespace App\User\Registration;

use App\Entity\User;
use App\MobilePayment\MobilePaymentFacade\Facade\BkfPayTopUpBonusFacade;
use App\Repository\UserSettingsRepository;
use App\Security\EmailVerifier;
use App\Service\AppConfig\AppConfigService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bridge\Twig\Mime\TemplatedEmail;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Mime\Address;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Symfony\Contracts\Translation\TranslatorInterface;
use SymfonyCasts\Bundle\ResetPassword\ResetPasswordHelperInterface;

class UserRegisterManager
{
    private string $emailName;


    private ?UserRegisterDTO $userRegisterDTO;

    private ?UserCreateDTO $userCreateDTO;

    private string $frontUrl;

    private string $noReplayEmailAddress;


    /**
     * UserRegisterManager constructor.
     *
     */
    public function __construct(
        private SerializerInterface $serializer,
        private ValidatorInterface $validator,
        private UserPasswordHasherInterface $passwordHasher,
        private EntityManagerInterface $entityManager,
        private EmailVerifier $emailVerifier,
        private TranslatorInterface $translator,
        ParameterBagInterface $parameterBag,
        private ResetPasswordHelperInterface $resetPasswordHelper,
        private UserSettingsRepository $userSettingsRepository,
        private AppConfigService $appConfigService,
        private BkfPayTopUpBonusFacade $bkfPayTopUpBonusFacade,
    ) {

        $this->emailName = $appConfigService->getAppName();
        $this->frontUrl = $parameterBag->get('front_url');
        $this->noReplayEmailAddress = $parameterBag->get('mailer_address_no_replay');
    }

    public function isApiRegisterRequestValid(Request $request): bool
    {
        if (empty($request->getContent())) {
            return false;
        }

        $this->userRegisterDTO = $this->serializer
            ->deserialize(
                $request->getContent(),
                UserRegisterDTO::class,
                'json'
            );

        $validationErrors = $this->validator->validate($this->userRegisterDTO);

        if (count($validationErrors)) {
            $this->userRegisterDTO = null;
            return false;
        }

        return true;
    }

    public function registerApiUser(): void
    {
        $language = $this->userRegisterDTO->getLang() ?? $this->appConfigService->getLanguage();
        $user = (new User())
            ->setEmail($this->userRegisterDTO->getEmail())
            ->setRoles([])
            ->setCurrency($this->appConfigService->getCurrency2())
            ->setLanguage($language)
            ->setIsVerified(false)
            ->setNewsletterAccepted($this->userRegisterDTO->isNewsletter())
            ->setTermsOfUseAccepted(true)
            ;

        $password = $this->passwordHasher->hashPassword($user, $this->userRegisterDTO->getPlainPassword());

        $user->setPassword($password);

        $this->entityManager->persist($user);
        $this->entityManager->flush();

        $this->userSettingsRepository->create($user);

        $this->sendValidationEmail($user);
    }

    public function registerWebUser(User $user, string $plainPassword): void
    {
        $password = $this->passwordHasher->hashPassword($user, $plainPassword);

        $user->setPassword($password);

        $this->entityManager->persist($user);
        $this->entityManager->flush();

        $this->sendValidationEmail($user);
    }

    private function sendValidationEmail(User $user): void
    {
        /** @phpstan-ignore-next-line */
        $this->translator->setLocale($user->getLocale());
        $this->emailVerifier->sendEmailConfirmation(
            'app_verify_email',
            $user,
            (new TemplatedEmail())
                ->from(new Address($this->noReplayEmailAddress, $this->emailName))
                ->to(new Address($user->getEmail()))
                ->subject($this->translator->trans('email.password-reset.confirm-button'))
                ->htmlTemplate('registration/confirmation_email.html.twig')
                ->context([
                    'user' => $user
                ])
        );
    }

    public function createApiUser(): void
    {
        $user = (new User())
            ->setEmail($this->userCreateDTO->getEmail())
            ->setRoles([])
            ->setCurrency($this->appConfigService->getCurrency2())
            ->setLanguage($this->appConfigService->getLanguage())
            ->setIsVerified(true)
            ;

        $password = $this->passwordHasher->hashPassword($user, bin2hex(random_bytes(16)));
        $user->setPassword($password);

        if ($this->userCreateDTO->hasCompanyData()) {
            $client = $this->userCreateDTO->getCompanyData()->toClient($user);
            $user->setClient($client);
            $this->entityManager->persist($client);
        }

        $this->entityManager->persist($user);
        $this->entityManager->flush();

        $this->userSettingsRepository->create($user);

        $this->sendInvitationEmail($user);

        $packageId = $this->userCreateDTO->getPackageId();
        if ($packageId !== null) {
            $this->bkfPayTopUpBonusFacade->initPayment($user, [
                'packageId' => $packageId,
                'appName' => $this->appConfigService->getAppName(),
            ]);
        }
    }

    public function isApiUserCreationRequestValid(Request $request): bool
    {
        if (empty($request->getContent())) {
            return false;
        }

        $this->userCreateDTO = $this->serializer
            ->deserialize(
                $request->getContent(),
                UserCreateDTO::class,
                'json'
            );

        $validationErrors = $this->validator->validate($this->userCreateDTO);

        if (count($validationErrors)) {
            $this->userCreateDTO = null;
            return false;
        }

        return true;
    }

    private function sendInvitationEmail(User $user): void
    {
        /** @phpstan-ignore-next-line */
        $this->translator->setLocale($user->getLocale());
        $resetToken = $this->resetPasswordHelper->generateResetToken($user);

        $email = (new TemplatedEmail())
            ->from(new Address($this->noReplayEmailAddress, $this->emailName))
            ->to(new Address($user->getEmail()))
            ->subject($this->translator->trans('email.invitation-email'))
            ->htmlTemplate('fleet/invitation_email.html.twig')
            ->context([
                'resetToken' => $resetToken,
                'user' => $user,
                'frontUrl' => $this->frontUrl
            ]);

        $this->emailVerifier->sendEmailConfirmation(
            'app_verify_email',
            $user,
            $email
        );
    }
}
