<?php

namespace App\User\Registration;

use App\Entity\Client;
use App\Entity\User;
use I2m\StandardTypes\Enum\Country;

class CompanyDataDTO
{
    private ?string $name = null;

    private ?string $taxNumber = null;

    private ?string $address = null;

    private ?string $postCode = null;

    private ?string $city = null;

    private ?Country $country = null;

    private ?string $email = null;

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(?string $name): void
    {
        $this->name = $name;
    }

    public function getTaxNumber(): ?string
    {
        return $this->taxNumber;
    }

    public function setTaxNumber(?string $taxNumber): void
    {
        $this->taxNumber = $taxNumber;
    }

    public function getAddress(): ?string
    {
        return $this->address;
    }

    public function setAddress(?string $address): void
    {
        $this->address = $address;
    }

    public function getPostCode(): ?string
    {
        return $this->postCode;
    }

    public function setPostCode(?string $postCode): void
    {
        $this->postCode = $postCode;
    }

    public function getCity(): ?string
    {
        return $this->city;
    }

    public function setCity(?string $city): void
    {
        $this->city = $city;
    }

    public function getCountry(): ?Country
    {
        return $this->country;
    }

    public function setCountry(?Country $country): void
    {
        $this->country = $country;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function setEmail(?string $email): void
    {
        $this->email = $email;
    }

    public function hasData(): bool
    {
        return $this->name !== null ||
               $this->taxNumber !== null ||
               $this->address !== null ||
               $this->postCode !== null ||
               $this->city !== null ||
               $this->country !== null ||
               $this->email !== null;
    }

    public function toClient(User $user): Client
    {
        $client = new Client();

        if ($this->name !== null) {
            $client->setName($this->name);
        }

        if ($this->taxNumber !== null) {
            $client->setTaxNumber($this->taxNumber);
        }

        if ($this->address !== null) {
            $client->setAddress($this->address);
        }

        if ($this->postCode !== null) {
            $client->setPostCode($this->postCode);
        }

        if ($this->city !== null) {
            $client->setCity($this->city);
        }

        if ($this->country !== null) {
            $client->setCountry($this->country);
        }

        if ($this->email !== null) {
            $client->setEmail($this->email);
        }

        $client->setUser($user);

        return $client;
    }
}
