<?php

namespace App\User\Registration;

use App\Entity\Enum\Languages;
use Symfony\Component\Validator\Constraints as Assert;

class UserRegisterDTO
{
    /**
     * @Assert\Email
     */
    private string $email;
    /**
     * @Assert\NotBlank
     * @Assert\Length(min = 6)
     */
    private string $plainPassword;

    private string $token = '';

    private ?string $nip = null;

    private bool $newsletter = false;

    private ?Languages $lang = null;

    public function getEmail(): string
    {
        return $this->email;
    }

    public function setEmail(string $email): void
    {
        $this->email = $email;
    }

    public function getPlainPassword(): string
    {
        return $this->plainPassword;
    }

    public function setPlainPassword(string $plainPassword): void
    {
        $this->plainPassword = $plainPassword;
    }

    public function getToken(): string
    {
        return $this->token;
    }

    public function setToken(string $token): void
    {
        $this->token = $token;
    }

    public function isNewsletter(): bool
    {
        return $this->newsletter;
    }

    public function setNewsletter(bool $newsletter): void
    {
        $this->newsletter = $newsletter;
    }

    public function getLang(): ?Languages
    {
        return $this->lang;
    }

    public function setLang(?Languages $lang): void
    {
        $this->lang = $lang;
    }

    public function getNip(): ?string
    {
        return $this->nip;
    }

    public function setNip(?string $nip): UserRegisterDTO
    {
        $this->nip = $nip;
        return $this;
    }
}
