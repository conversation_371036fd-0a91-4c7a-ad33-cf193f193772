<?php

namespace App\User;

use App\Entity\Enum\Issuer;
use App\Entity\MobilePaymentSubscriptionPackage;
use App\Entity\User;
use App\Repository\MobilePaymentRepository;

class UserService
{
    public function __construct(
        private readonly MobilePaymentRepository $mobilePaymentRepository,
    ) {
    }

    /**
     * @param User $user
     * @return UserCardsDto[]
     */
    public function getUserCards(User $user)
    {
        $myTransactions = [];

        $subs = $this->mobilePaymentRepository->getSubscriptionsBalance($user);
        foreach ($subs as $sub) {
            $myTransactions[] =
                (new UserCardsDto())
                    ->setTypeKey(Issuer::SUBSCRIPTION)
                    ->setEndTime($sub['endTime'])
                    ->setBalance($sub['balance'])
            ;
        }
        $balance = $this->getBalance($user);
        if ($balance) {
            $myTransactions[] = (new UserCardsDto())
                ->setTypeKey(Issuer::BKF_PAY)
                ->setEndTime(null)
                ->setBalance($balance)

            ;
        }

        if (empty($myTransactions)) {
            $myTransactions[] = (new UserCardsDto())
                ->setTypeKey(Issuer::BKF_PAY)
                ->setEndTime(null)
                ->setBalance(0)
            ;
        }

        return $myTransactions;
    }

    /**
     * Summarize user cards
     *
     * @param UserCardsDto[] $cards
     */
    public function summarizeUserCards(array $cards): float
    {
        $sum = 0;

        foreach ($cards as $card) {
            $sum += $card->getBalance();
        }

        return (float) number_format($sum, 2, '.', '');
    }

    public function getBalance(User $user, Issuer $issuer = Issuer::BKF_PAY, ?MobilePaymentSubscriptionPackage $subscription = null)
    {
        $balance[] = $this->mobilePaymentRepository->getBkfPayBalance($user, $issuer, $subscription);

        if (!$this->getLimitPeriod($user, "hour", $user->getSettings()?->getLimitHourly(), $balance)) {
            return 0;
        }

        if (!$this->getLimitPeriod($user, "day", $user->getSettings()?->getLimitDaily(), $balance)) {
            return 0;
        }

        if (!$this->getLimitPeriod($user, "month", $user->getSettings()?->getLimitMonthly(), $balance)) {
            return 0;
        }
        return min($balance);
    }

    public function getLimits(User $user)
    {
        return [

            'hourly' =>
                [
                    'limit' => $user->getSettings()?->getLimitHourly(),
                    'usage' => $this->mobilePaymentRepository->limitValue($user, new \DateTime("-1 hour"))['value']
                ],
            'daily' =>
                [
                    'limit' => $user->getSettings()?->getLimitDaily(),
                    'usage' => $this->mobilePaymentRepository->limitValue($user, new \DateTime("-1 day"))['value']
                ],
            'monthly' =>
                [
                    'limit' => $user->getSettings()?->getLimitMonthly(),
                    'usage' => $this->mobilePaymentRepository->limitValue($user, new \DateTime("-1 month"))['value']
                ],

        ];
    }

    /**
     * funkcja uzupałnia tabele z możliwimy stanem konta, oraz zwraca 0 gdy stan konta 0
     */
    public function getLimitPeriod(User $user, string $period, ?float $max, &$balance): float
    {
        if (is_null($max)) {
            return 1;
        }

        $limit = $max - $this->mobilePaymentRepository->limitValue($user, new \DateTime("-1 $period"))['value'];
        $limit = $limit < 0 ? 0 : $limit;
        $balance[] = $limit;

        if ($limit) {
            return 1;
        }
        return 0;
    }
}
