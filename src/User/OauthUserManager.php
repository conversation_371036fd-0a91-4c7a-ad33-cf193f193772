<?php

namespace App\User;

use App\Entity\Enum\Languages;
use App\Entity\Token;
use App\Entity\User;
use App\Repository\UserRepository;
use App\Security\ApiTokenGenerator;
use App\Service\AppConfig\AppConfigService;
use DateInterval;
use DateTimeImmutable;
use Doctrine\ORM\EntityManagerInterface;
use Exception;

class OauthUserManager
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private ApiTokenGenerator $tokenGenerator,
        private UserRepository $userRepository,
        private AppConfigService $appConfigService
    ) {
    }

    public function generateAndSaveToken(User $user, string $duration = 'P1Y'): string
    {
        $tokenString = $this->tokenGenerator->generate();
        $expiresAt = (new DateTimeImmutable())->add(new DateInterval($duration));
        $token = (new Token())->setToken($tokenString)->setUser($user)->setExpiresAt($expiresAt);

        $this->entityManager->persist($token);
        $this->entityManager->persist($user);
        $this->entityManager->flush();

        return $tokenString;
    }

    /**
     * @throws Exception
     */
    public function createNewUser(string $email, array $roles = []): User
    {
        $user = $this->userRepository->findOneBy(['email' => $email]);
        if ($user) {
            return $user;
        }

        $languageLocale = $this->appConfigService->getLanguage()->value;



        return (new User())
            ->setEmail($email)
            ->setIsVerified(true)
            ->setCurrency($this->appConfigService->getCurrency2())
            ->setLanguage(Languages::from($languageLocale))
            ->setStatus('a')
            ->setPassword($this->generatePassword())
            ->setRoles($roles);
    }

    /**
     * @throws Exception
     */
    private function generatePassword(): string
    {
        return sha1(random_bytes(25));
    }
}
