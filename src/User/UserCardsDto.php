<?php

namespace App\User;

use App\Entity\Currency;
use App\Entity\Enum\Issuer;
use Symfony\Component\Serializer\Attribute\Groups;

class UserCardsDto
{
    #[Groups(["default:basic"])]
    private Issuer $typeKey;
    #[Groups(["default:basic"])]
    private float $balance;
    #[Groups(["default:basic"])]
    private ?\DateTimeInterface $endTime;

    public function getTypeKey(): Issuer
    {
        return $this->typeKey;
    }

    public function setTypeKey(Issuer $typeKey): UserCardsDto
    {
        $this->typeKey = $typeKey;
        return $this;
    }

    public function getBalance(): float
    {
        return $this->balance;
    }

    public function setBalance(float $balance): UserCardsDto
    {
        $this->balance = $balance;
        return $this;
    }

    public function getEndTime(): ?\DateTimeInterface
    {
        return $this->endTime;
    }

    public function setEndTime(?\DateTimeInterface $endTime): UserCardsDto
    {
        $this->endTime = $endTime;
        return $this;
    }
}
