<?php

namespace App\Serializer;

use App\Entity\MobilePaymentSubscriptionPackage;
use App\Repository\MobilePaymentRepository;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;
use Symfony\Component\Serializer\Normalizer\ObjectNormalizer;

class PaymentSubscriptionPackageNormalizer implements NormalizerInterface
{
    public function __construct(
        private ObjectNormalizer $normalizer,
        private MobilePaymentRepository $mobilePaymentRepository,
    ) {
    }

    public function normalize($object, string $format = null, array $context = [])
    {
        $groups = $context['groups'] ?? null;

        /** @var MobilePaymentSubscriptionPackage $object */
        if (is_array($groups) && in_array("subscription:left", $groups)) {
            $usage = $this->mobilePaymentRepository->getSubscriptionBalance($object);
            $object->setLeft($usage[0]['balance'] ?? null);
        }

        return $this->normalizer->normalize($object, $format, $context);
    }

    public function supportsNormalization($data, string $format = null, array $context = [])
    {
        return  ($data instanceof MobilePaymentSubscriptionPackage);
    }
}
