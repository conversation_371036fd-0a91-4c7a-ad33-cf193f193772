<?php

namespace App\Serializer;

use App\Entity\Locale;
use App\Service\AppConfig\AppConfigService;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;
use Symfony\Component\Serializer\Normalizer\ObjectNormalizer;

class LocaleNormalizer implements NormalizerInterface
{
    public function __construct(
        private ObjectNormalizer $normalizer,
        private AppConfigService $appConfigService
    ) {
    }

    public function normalize($object, string $format = null, array $context = [])
    {
        /** @var Locale $object */
        $object->setDateTimeZone(
            $this->appConfigService->getTimezone()
        );
        $object->setCurrency($this->appConfigService->getCurrency2());
        return $this->normalizer->normalize($object, $format, $context);
    }

    public function supportsNormalization($data, string $format = null, array $context = [])
    {
        return  ($data instanceof Locale);
    }
}
