<?php

namespace App\Serializer;

use App\Entity\MobilePaymentPackage;
use App\Service\AppConfig\AppConfigService;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use <PERSON><PERSON><PERSON>ny\Component\Serializer\Normalizer\NormalizerInterface;
use Symfony\Component\Serializer\Normalizer\ObjectNormalizer;

class PackageNormalizer implements NormalizerInterface
{
    public function __construct(
        private ObjectNormalizer $normalizer,
        private AppConfigService $appConfigService
    ) {
    }

    public function normalize($object, string $format = null, array $context = [])
    {
        $timezone = $this->appConfigService->getTimezone();
        /** @var MobilePaymentPackage $object */

        // zamieniam na czas aplikacji z UTC
        $object->getStartTime()?->setTimezone($timezone);
        $object->getEndTime()?->setTimezone($timezone);
        return $this->normalizer->normalize($object, $format, $context);
    }

    public function supportsNormalization($data, string $format = null, array $context = [])
    {
        return ($data instanceof MobilePaymentPackage);
    }
}
