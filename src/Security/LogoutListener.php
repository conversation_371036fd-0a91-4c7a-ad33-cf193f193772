<?php

namespace App\Security;

use App\Entity\Token;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Security\Http\Event\LogoutEvent;

class LogoutListener
{
    private EntityManagerInterface $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;
    }


    public function onSymfonyComponentSecurityHttpEventLogoutEvent(LogoutEvent $logoutEvent): void
    {
        $token = $logoutEvent->getToken();

        if ($token === null) {
            $logoutEvent->setResponse(new JsonResponse(null, 401));
            return;
        }

        $this->invalidateToken($logoutEvent);
    }

    private function invalidateToken(LogoutEvent $logoutEvent)
    {
        $token = $this->entityManager->getRepository(Token::class)->findOneBy(
            [
                'token' => $this->getTokenFromRequest($logoutEvent->getRequest()),
            ]
        );

        $token->invalidate();
        $this->entityManager->flush();

        $logoutEvent->setResponse(new JsonResponse(null, 204));
    }

    private function getTokenFromRequest(Request $request): string
    {
        return $request->headers->get('authorization');
    }
}
