<?php

namespace App\Security;

use App\Entity\User;
use App\Repository\UserRepository;
use Exception;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;

class LegacyUserSecurityService
{
    private const BAD_LOGIN = 'Bad login or password';
    private UserRepository $userRepository;
    private UserPasswordHasherInterface $passwordHasher;

    public function __construct(
        UserRepository $userRepository,
        UserPasswordHasherInterface $passwordHasher
    ) {
        $this->userRepository = $userRepository;
        $this->passwordHasher = $passwordHasher;
    }

    /**
     *
     * @throws Exception
     */
    public function validateUserLogin(Request $request): User
    {

        if (empty($request->getContent())) {
            throw new Exception(self::BAD_LOGIN);
        }

        $requestArray = $request->toArray();

        if (!isset($requestArray['email'])) {
            throw new Exception(self::BAD_LOGIN);
        }

        $user = $this->userRepository->findOneBy(['email' => $requestArray['email']]);

        if ($user === null) {
            throw new Exception(self::BAD_LOGIN);
        }

        if (!$user->isVerified()) {
            throw new Exception(self::BAD_LOGIN);
        }

        if (!isset($requestArray['plainPassword'])) {
            throw new Exception(self::BAD_LOGIN);
        }

        if (!$this->passwordHasher->isPasswordValid($user, $requestArray['plainPassword'])) {
            throw new Exception(self::BAD_LOGIN);
        }

        return $user;
    }
}
