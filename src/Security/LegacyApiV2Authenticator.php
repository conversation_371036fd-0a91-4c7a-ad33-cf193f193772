<?php

namespace App\Security;

use App\Entity\User;
use App\Repository\TokenRepository;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Exception\AuthenticationException;
use Symfony\Component\Security\Core\Exception\CustomUserMessageAuthenticationException;
use Symfony\Component\Security\Http\Authenticator\AbstractAuthenticator;
use Symfony\Component\Security\Http\Authenticator\Passport\Badge\UserBadge;
use Symfony\Component\Security\Http\Authenticator\Passport\Credentials\CustomCredentials;
use Symfony\Component\Security\Http\Authenticator\Passport\Passport;

class LegacyApiV2Authenticator extends AbstractAuthenticator
{
    public function __construct(private TokenRepository $tokenRepository)
    {
    }

    public function supports(Request $request): ?bool
    {
        return $request->headers->has('authorization');
    }

    public function authenticate(Request $request): Passport
    {
        return new Passport(
            new UserBadge(
                $request->headers->get('authorization'),
                function ($token) {
                    $token = $this->tokenRepository->findUserByToken($token);

                    if (null === $token) {
                        throw new CustomUserMessageAuthenticationException('Wrong user or password');
                    }

                    return $token->getUser();
                }
            ),
            new CustomCredentials(
                function ($credentials, User $user) {
                    return $user->getValidTokensList()->contains($credentials);
                },
                $request->headers->get('authorization')
            )
        );
    }

    public function onAuthenticationSuccess(Request $request, TokenInterface $token, string $firewallName): ?Response
    {
        return null;
    }

    public function onAuthenticationFailure(Request $request, AuthenticationException $exception): ?Response
    {
        return null;
    }
}
