<?php

namespace App\Command;

use App\Repository\MobilePaymentRepository;
use App\Service\SalesDocument\SalesDocumentService;
use Exception;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputDefinition;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

/**
 * Class InvoiceGenerateForPaymentsCommand
 *
 * @package App\Command
 */
class ReceiptOrInvoiceCommand extends Command
{
    protected OutputInterface $output;
    public function __construct(
        private SalesDocumentService $salesDocumentService,
        private MobilePaymentRepository $mobilePaymentRepository
    ) {
        parent::__construct();
    }

    protected function configure()
    {
        $this
            ->setName('bp:invoice-receipt:generate')
            ->setDescription('Generate invoice or receipt based on mobile_payment')
            ->setHelp('This command allows you to generate invoces for BeLoyal. 
            First step is to check mobile payments and fix client mobile payment relation.')
            ->setDefinition(
                new InputDefinition(array(
                    new InputOption('paymentId', 'pid', InputOption::VALUE_OPTIONAL),
                ))
            );
    }

    /**
     *
     * @throws Exception
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->output = $output;

        $this->output->writeln('<comment>Get payments ...</comment>');
        $paymentId = $input->getOption('paymentId');

        return $this->processPaymentsToInvoice($paymentId);
    }

    private function processPaymentsToInvoice(int $paymentId): int
    {
        $this->output->writeln(
            '<comment> Start generating receipt or invoice </comment>'
        );

        $mobilePayment = $this->mobilePaymentRepository->find($paymentId);
        $this->salesDocumentService->generateSync($mobilePayment);

        $this->output->writeln('<comment>End generating recipe invoice.</comment>');

        return Command::SUCCESS;
    }
}
