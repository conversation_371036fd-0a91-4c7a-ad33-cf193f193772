<?php

namespace App\Command;

use App\Service\SalesDocument\Invoices\InvoiceSyncManager;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(name: 'bkf:invoice-status:sync')]
class InvoiceStatusSyncCommand extends Command
{
    protected static $defaultName = 'bkf:invoice-status:sync';

    private InvoiceSyncManager $syncManager;

    public function __construct(
        InvoiceSyncManager $syncManager
    ) {
        $this->syncManager = $syncManager;

        parent::__construct();
    }

    protected function configure()
    {
        $this
            ->setName(self::$defaultName)
            ->setDescription('Sync carwashes from BKF')
            ->setHelp('This command synchronize carwashes from BKF')
            ->addOption(
                'notify',
                null,
                InputOption::VALUE_OPTIONAL,
                'notify about wrong parameters',
                false
            );
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        return $this->syncManager->syncInvoicesStatus();
    }
}
