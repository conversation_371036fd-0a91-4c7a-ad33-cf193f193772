<?php

namespace App\Command;

use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
    name: 'i2m:version',
    description: 'Add a short description for your command',
)]
class I2mVersionCommand extends Command
{
    public function __construct()
    {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->setDescription('Generates version.h file with build information')
            ->addOption('out_file', null, InputOption::VALUE_OPTIONAL, 'Output file', 'public/version.h');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $outFile = $input->getOption('out_file');
        $date = (new \DateTime())->format('Y-m-d H:i:s');


        if (file_exists($outFile)) {
            unlink($outFile);
        }

        $content = '';
        $content .= sprintf('SOFT_DATE "%s"' . PHP_EOL, $date);

        if (getenv('CI')) {
            if ($commitTag = getenv('CI_COMMIT_TAG')) {
                $content .= sprintf('SOFT_VERSION "%s"' . PHP_EOL, $commitTag);
            } else {
                $commitSha = getenv('CI_COMMIT_SHA');
                $content .= sprintf('SOFT_VERSION "%s"' . PHP_EOL, $commitSha);
            }

            $content .= sprintf('SOFT_USER "%s"' . PHP_EOL, getenv('CI_RUNNER_DESCRIPTION'));

            $content .= sprintf('SOFT_BUILD "%s"' . PHP_EOL, getenv('CI_JOB_ID'));
            $content .= sprintf('SOFT_PATH "%s"' . PHP_EOL, getenv('CI_PROJECT_PATH'));
            $content .= sprintf('SOFT_PIPELINE "%s"' . PHP_EOL, getenv('CI_PIPELINE_ID'));
        } else {
            $content .= 'SOFT_VERSION "NO CI"';
        }

        file_put_contents($outFile, $content);

        // Display content in the console
        $output->writeln(file_get_contents($outFile));

        return Command::SUCCESS;
    }
}
