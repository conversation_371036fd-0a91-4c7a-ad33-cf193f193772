<?php

namespace App\Command;

use App\Entity\Enum\MobilePaymentStatus;
use App\Entity\Enum\ReportType;
use App\Entity\Enum\TransactionType;
use App\Entity\ReportsConfig;
use App\Entity\User;
use App\Repository\MobilePaymentRepository;
use App\Service\AppConfig\AppConfigService;
use App\Service\Reports\Mailer\ReportSender;
use I2m\Reports\Enum\ReportInterval;
use App\Repository\UserRepository;
use App\Service\Reports\ReportGenerator;
use I2m\Reports\Enum\FileExtention;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

use function Sentry\captureException;

#[AsCommand(
    name: 'app:reports:fleet',
    description: 'Add a short description for your command',
)]
class FleetReportGeneratorCommand extends Command
{
    public function __construct(
        private ReportGenerator $reportGenerator,
        private ReportSender $reportSender,
        private MobilePaymentRepository $mobilePaymentRepository,
        private UserRepository $userRepository,
        private TranslatorInterface $translator,
        private AppConfigService $appConfig,
    ) {
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $interval = ReportInterval::MONTH;
        $output->writeln('Generating reports');

        $reports = $this->generateReports($output, $interval);

        $output->writeln('generated reports: ' . $reports);
        return -1;
    }

    public function generateReports(OutputInterface $output, ReportInterval $interval): int
    {
        $reports = 0;
        $perPage = 20;
        $page = 1;
        $dateRange = $interval->getDateRange($this->appConfig->getTimezone());

        do {
            $fleetManagers = $this->mobilePaymentRepository->getFleetManagersWithTransactions(
                startDate: $dateRange->getDateFrom(),
                endDate: $dateRange->getDateTo(),
                page: $page,
                perPage: $perPage,
                type: [TransactionType::PAYMENT],
                statusArray: [MobilePaymentStatus::CONFIRMED],
            );

            $fleetManagers = $this->userRepository->findBy(['id' => $fleetManagers]);

            foreach ($fleetManagers as $fleetManager) {
                /** @var User $fleetManager */
                $output->writeln('<comment>Generating report for ' . $fleetManager->getEmail() . '</comment>');
                $reportConfig = (new ReportsConfig())
                    ->setName($this->translator->trans('report_fleet_title'))
                    ->setReportType(ReportType::TRANSACTIONS_SUMMARY_REPORT)
                    ->setUser($fleetManager)
                    ->setExt(FileExtention::PDF)
                    ->setReportInterval(ReportInterval::LAST_MONTH)
                    ->setEmail(['<EMAIL>', '<EMAIL>'])
                ;

                $document = $this->reportGenerator->generateFile($reportConfig);
                $this->reportSender->send(
                    $document,
                    $reportConfig,
                    $reportConfig->getEmail(),
                    '<EMAIL>'
                );
                $reports++;
            }
            $page++;
        } while (!empty($fleetManagers));

        return $reports;
    }
}
