<?php

namespace App\Command;

use App\Repository\InvoicesRepository;
use App\Service\AppConfig\AppConfigService;
use App\Service\ProfileInfo\Alert;
use App\Service\SalesDocument\MissingSalesDocumentFinder;
use I2m\Invoices\Service\InvoiceService;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

use function Sentry\captureMessage;

#[AsCommand(
    name: 'bp:fix-invoices',
    description: 'Add a short description for your command',
)]
class BpFixInoicesCommand extends Command
{
    public function __construct(
        private InvoicesRepository $invoicesRepository,
        private InvoiceService $invoiceService,
        private AppConfigService $appConfigService
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->addOption(
                'generate',
                'g',
                InputOption::VALUE_OPTIONAL,
                'generate missing documents',
                false
            );
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        $generateMissingInvoices = $input->getOption('generate');
        $invoices = $this->invoicesRepository->findBreakingInvoices();
        $count = count($invoices);
        $issuer = $this->appConfigService->getAppIssuer();
        foreach ($invoices as $invoice) {
            $io->writeln("Naprawiam dokument {$invoice->getId()}, number: {$invoice->getNumber()}, cloud: {$invoice->getCloudPath()}");
            if ($generateMissingInvoices) {
                $this->invoiceService->fixInvoice($issuer->getInvoiceType(), $issuer->getInvoiceConfig(), $invoice);
                $io->writeln("\t -> Naprawiony dokument {$invoice->getId()}, number: {$invoice->getNumber()}, cloud: {$invoice->getCloudPath()}");
            }
        }

        $count ? $io->caution("Znaleziono $count dokumentów") :
                $io->success("brak błędnych dokumentów")
            ;

        return Command::SUCCESS;
    }
}
