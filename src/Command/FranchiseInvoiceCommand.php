<?php

namespace App\Command;

use App\Entity\Enum\AggrementStatus;
use App\Entity\Enum\InvoiceSource;
use App\Entity\Enum\MobilePaymentStatus;
use App\Entity\Enum\ReportType;
use App\Entity\Enum\TransactionType;
use App\Entity\InvoiceFranchise;
use App\Entity\Invoices;
use App\Entity\Owners;
use App\Entity\ReportFile;
use App\Entity\ReportsConfig;
use App\Repository\CarwashRepository;
use App\Repository\InvoicesRepository;
use App\Repository\MobilePaymentRepository;
use App\Repository\OwnerRepository;
use App\Service\AppConfig\AppConfigService;
use App\Service\Reports\ReportGenerator;
use App\Service\SalesDocument\Invoices\InvoiceSender;
use I2m\Invoices\Enum\InvoiceGeneratorType;
use I2m\Invoices\Enum\InvoiceKindType;
use I2m\Invoices\Service\InvoiceService;
use I2m\Reports\Enum\FileExtention;
use I2m\Reports\Enum\ReportInterval;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Contracts\Translation\TranslatorInterface;

use function Sentry\captureMessage;

#[AsCommand(
    name: 'bp:franchise:invoice',
    description: 'Add a short description for your command',
)]
class FranchiseInvoiceCommand extends Command
{
    public function __construct(
        private MobilePaymentRepository $mobilePaymentRepository,
        private OwnerRepository $ownerRepository,
        private AppConfigService $appConfigService,
        private CarwashRepository $carwashRepository,
        private TranslatorInterface $translator,
        private InvoicesRepository $invoicesRepository,
        private InvoiceService $invoiceService,
        private ReportGenerator $reportGenerator,
        private InvoiceSender $invoiceSender,
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        $owners = $this->ownerRepository->findBy(['aggrement' => AggrementStatus::SIGNED]);
        // $owners = $this->ownerRepository->findBy(['id' => 36814]);
        $reportInterval = ReportInterval::LAST_MONTH;
        $dateRange = $reportInterval->getDateRange($this->appConfigService->getTimezone());

        foreach ($owners as $owner) {
            $data = $this->mobilePaymentRepository->findPaymentsByDateRangeAndUser(
                owner: $owner,
                startDate: $dateRange->getDateFrom(),
                endDate: $dateRange->getDateTo(),
                statusArray: [MobilePaymentStatus::CONFIRMED],
                type: [TransactionType::PAYMENT],
                perPage: null,
                showTotal: true
            );

            if (empty($data['totalItems'])) {
                $io->warning("No payments for {$owner->getName()}");
                continue;
            }

            if ($owner->getAggrement() !== AggrementStatus::SIGNED) {
                $io->warning("Owner {$owner->getName()} is not signed");
                continue;
            }

            if (empty($owner->getTaxNumber())) {
                $io->warning("Owner {$owner->getName()} has no tax number");
                continue;
            }

            $invoice = $this->createInvoice(
                $owner,
                $dateRange->getDateTo(),
                $data['carwash']
            );

            $this->invoiceService->generate(
                InvoiceGeneratorType::Internal,
                $owner->getInvoiceConfig() ?? ["numberTemplate" => "{$this->appConfigService->getAppName()}.F-{$owner->getId()}/{m}/{Y}"],
                $invoice
            );

            $reportFile = $this->generateReport(
                $owner,
                $reportInterval,
            );
            $invoice->setAttachment($reportFile);

            $this->invoicesRepository->save($invoice);

            $this->invoiceSender->sendFranchiseInvoiceWithEmail($invoice);
        }


        $io->success('You have a new command! Now make it your own! Pass --help to see your options.');

        return Command::SUCCESS;
    }

    public function generateReport(Owners $owner, ReportInterval $interval): ?ReportFile
    {
        $queryParams = [
            'owner' => $owner->getId(),
        ];

        $reportConfig = (new ReportsConfig())
            ->setConfig($queryParams)
            ->setExt(FileExtention::PDF)
            ->setName(ReportType::FRANCHISE_TRANSACTIONS_REPORT->value)
            ->setReportType(ReportType::FRANCHISE_TRANSACTIONS_REPORT)
            ->setReportInterval($interval)
        ;

        return $this->reportGenerator->generateFile(
            $reportConfig
        );
    }

    public function createInvoice(Owners $owner, ?\DateTime $serviceDate, array $carwashStats): Invoices
    {
        $issuenceDate = new \DateTime();
        $invoice = (new Invoices())
            ->setCtime($issuenceDate)
            ->setInvoiceDate($serviceDate)
            ->setPeriod($this->getCurrentPeriod($serviceDate))
            ->setPaymentMethod(Invoices::PAYMENT_METHOD_POST_PAID)
            ->setClient($this->appConfigService->getFranchiseClient())
            ->setCurrency($this->appConfigService->getCurrency())
            ->setIssuer($owner)
            ->setKind(InvoiceKindType::vat)
            ->setSource(InvoiceSource::FRANCHISE)
        ;

        $invoice->setFranchise(new InvoiceFranchise($invoice));

        //$this->setInvoiceNumber($invoice);
        foreach ($carwashStats as $carwashStat) {
            $carwash = $this->carwashRepository->findOneBySerialNumber(
                $carwashStat['serialNumber']
            );
            $invoice->addPosition(
                $this->translator->trans('invoices.pay-for-washing') . " " . $carwash->getLongName(),
                1,
                abs($carwashStat['sum']) * 0.9,
                $this->appConfigService->getVatTax()->getTaxValue()
            );
        }

        $this->invoicesRepository->save($invoice);
        return $invoice;
    }

    /**
     * Return current period using server php timezone
     *
     * @return string
     */
    public function getCurrentPeriod(\DateTimeInterface $date = null)
    {
        return \DateTime::createFromInterface($date)->format("Y-m");
    }
}
