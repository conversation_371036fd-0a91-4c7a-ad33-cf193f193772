<?php

namespace App\Command\CarwashSync;

use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ContainerBagInterface;

#[AsCommand(name: 'bkf:carwash:sync')]
class CarwashSyncCommand extends Command
{
    protected static $defaultName = 'bkf:carwash:sync';

    private CarwashSyncManager $syncManager;

    private array $paymentsMethods;

    public function __construct(
        CarwashSyncManager $syncManager,
        ContainerBagInterface $parameterBag
    ) {
        $this->syncManager = $syncManager;
        $this->paymentsMethods = explode(',', $parameterBag->get('bkf_api_sync_payments_method'));

        parent::__construct();
    }

    protected function configure()
    {
        $this
            ->setName(self::$defaultName)
            ->setDescription('Sync carwashes from BKF')
            ->setHelp('This command synchronize carwashes from BKF')
            ->addOption(
                'notify',
                null,
                InputOption::VALUE_OPTIONAL,
                'notify about wrong parameters',
                false
            );
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        return $this->syncManager->syncCarwashes(
            $this->paymentsMethods,
            $input->getOption('notify')
        );
    }
}
