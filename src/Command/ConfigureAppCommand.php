<?php

namespace App\Command;

use App\User\OauthUserManager;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'app:configure',
    description: 'Add a short description for your command',
)]
class ConfigureAppCommand extends Command
{
    public function __construct(
        private OauthUserManager $oauthUserManager,
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->addArgument('arg1', InputArgument::OPTIONAL, 'Argument description')
            ->addOption('option1', null, InputOption::VALUE_NONE, 'Option description')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $arg1 = $input->getArgument('arg1');


        // utworz konto dostępowe i wyświetl token
        $user = $this->oauthUserManager->createNewUser("cm_admin", ['ROLE_APP_MANAGER']);
        $token = $this->oauthUserManager->generateAndSaveToken($user, 'P10Y');
        $output->writeln("Token created: {$token}");


        // dodaj obsługiwane jezyki

        // dodaj


        $io->success('You have a new command! Now make it your own! Pass --help to see your options.');

        return Command::SUCCESS;
    }
}
