<?php

namespace App\Command;

use App\Service\StickerGenerator\StickerGenerator;
use App\Service\StickerGenerator\StickerService;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpKernel\KernelInterface;

#[AsCommand(
    name: 'sticker:generate',
    description: 'Add a short description for your command',
)]
class StickerGenerateCommand extends Command
{
    public function __construct(
        private StickerService $stickerGenerator,
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->addOption(
                'start',
                's',
                InputOption::VALUE_REQUIRED,
                'Starting number of the series'
            )
            ->addOption(
                'length',
                'l',
                InputOption::VALUE_OPTIONAL,
                'Length of the series',
                default: 10
            )
            ->addOption(
                'lang',
                'Length of the series',
                InputOption::VALUE_OPTIONAL,
                default: 'pl'
            )
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);


        $this->stickerGenerator->genrateSeries(
            $input->getOption('start'),
            $input->getOption('length'),
        );

        $io->success('Success, naklejki skopiuj do https://drive.google.com/drive/folders/1OQO-8wZLVUcORLMj2I-yzXbK-JQzExre');

        return Command::SUCCESS;
    }
}
