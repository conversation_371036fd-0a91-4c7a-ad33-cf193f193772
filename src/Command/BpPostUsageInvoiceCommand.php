<?php

namespace App\Command;

use App\MobilePayment\MobilePaymentFacade\Facade\BkfPayPostUsageFacade;
use App\MobilePayment\MobilePaymentFacade\Facade\BkfPayTransferFacade;
use App\Repository\MobilePaymentRepository;
use App\Repository\MpApiConfigRepository;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'bp:post-usage-invoice',
    description: 'Add a short description for your command',
)]
class BpPostUsageInvoiceCommand extends Command
{
    public function __construct(
        private MpApiConfigRepository $apiConfigRepository,
        private MobilePaymentRepository $mobilePaymentRepository,
        private BkfPayPostUsageFacade $bkfPayPostUsageFacade
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->addArgument('arg1', InputArgument::OPTIONAL, 'Argument description')
            ->addOption('option1', null, InputOption::VALUE_NONE, 'Option description')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $periodEnd =  (new \DateTime("last day of previous month"))->setTime(23, 59, 59);

        $mpApis = $this->apiConfigRepository->findAll();

        foreach ($mpApis as $mpApi) {
            $user = $mpApi->getUser();

            $balance = $this->mobilePaymentRepository->updateBalance($user);
            $io->writeln("User {$user->getEmail()} balance $balance");
            if ($balance < 0) {
                $this->bkfPayPostUsageFacade->initPayment(
                    $user,
                    [
                        'from' => null,
                        'to' => $periodEnd,
                    ]
                );
            }
        }


        $io->success('You have a new command! Now make it your own! Pass --help to see your options.');

        return Command::SUCCESS;
    }
}
