<?php

namespace App\Command;

use App\Service\ProfileInfo\Alert;
use App\Service\SalesDocument\MissingSalesDocumentFinder;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

use function Sentry\captureMessage;

#[AsCommand(
    name: 'bp:missing-sales-docs',
    description: 'Add a short description for your command',
)]
class BpMissingSalesDocsCommand extends Command
{
    public function __construct(
        private MissingSalesDocumentFinder $missingSalesDocumentFinder,
        private LoggerInterface $logger,
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->addOption(
                'generate',
                'g',
                InputOption::VALUE_OPTIONAL,
                'generate missing documents',
                false
            );
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $generateMissingInvoices = $input->getOption('generate');

        $alerts = $this->missingSalesDocumentFinder->generate($generateMissingInvoices);
        $count = count($alerts);

        foreach ($alerts as $alert) {
            /** @var Alert $alert */
            $this->notifySentry($alert->getTitle(), $alert->getText());
        }

        $count ? $io->caution("Znaleziono $count dokumentów") :
                $io->success("brak niewystawionych dokumentów")
            ;

        return Command::SUCCESS;
    }

    private function notifySentry($title, $message)
    {
        $this->logger->error(
            "$title: \n\t$message"
        );
        captureMessage("$title\n
        $message");
    }
}
