<?php

namespace App\Exception;

use Exception;
use Throwable;

/**
 * Class ClientSideException
 */
class ClientSideException extends Exception
{
    protected $clientCode;

    /**
     * ClientSideException constructor.
     *
     * @param string $message
     * @param int $code
     * @param \Throwable|null $previous
     * @param string $clientCode
     */
    public function __construct($message = "", $code = 0, Throwable $previous = null, $clientCode = "")
    {
        parent::__construct($message, $code, $previous);
        $this->clientCode = $clientCode;
    }

    /**
     * @return mixed
     */
    public function getClientCode()
    {
        return $this->clientCode;
    }
}
