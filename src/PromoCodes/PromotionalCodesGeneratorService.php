<?php

namespace App\PromoCodes;

use App\Entity\PromotionalCodesGroup;
use App\Entity\PromotionalTopupCode;
use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\OptimisticLockException;

/**
 * Class PromotionalCodesGeneratorService
 *
 * @package CarwashManagerBundle\Service
 */
class PromotionalCodesGeneratorService
{
    protected EntityManagerInterface $em;

    /**
     * @var PromotionalCodesValidatorService $validator
     */
    protected $validator;

    public function __construct(
        EntityManagerInterface $em,
        PromotionalCodesValidatorService $validator
    ) {
        $this->em = $em;
        $this->validator = $validator;
    }

    /**
     * @param DateTime|null $startDate
     * @param DateTime|null $expirationDate
     * @throws OptimisticLockException
     */
    public function generate(
        int $quantity,
        float $value,
        string $name,
        string $type,
        DateTime $startDate = null,
        DateTime $expirationDate = null
    ): array {
        $codes = [];

        for ($a = 0; $a < $quantity; $a++) {
            do {
                $code = $this->makeCode();
            } while (array_key_exists($code, $codes));

            $codes[$code] = 1;
        }

        $codes = array_keys($codes);

        $group = new PromotionalCodesGroup();
        $group->setName($name);
        $group->setCtime(new DateTime());
        $group->setCount($quantity);
        $group->setType($type);
        $group->setStartDate($startDate);
        $group->setExpirationDate($expirationDate);

        foreach ($codes as $code) {
            $topup = new PromotionalTopupCode();
            $topup->setCode($code);
            $topup->setValue($value);
            $topup->setGroup($group);
            $this->em->persist($topup);
        }

        $this->em->persist($group);
        $this->em->flush();

        return $codes;
    }

    /**
     * @return string
     */
    public function makeCode()
    {
        $rand = strtoupper(str_pad(dechex(mt_rand(0, 0xFFFF)), 4, '0', STR_PAD_LEFT));
        $code = $rand;
        $code .= $this->validator->genValidNumber($rand);
        $code .= '-';
        $rand = strtoupper(str_pad(dechex(mt_rand(0, 0xFFF)), 3, '0', STR_PAD_LEFT));
        $code .= $rand;
        $code .= $this->validator->genValidNumber($rand);

        return $code;
    }
}
