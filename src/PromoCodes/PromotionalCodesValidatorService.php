<?php

namespace App\PromoCodes;

/**
 * Class class PromotionalCodesValidatorService
 *
 * @package CarwashManagerBundle\Service
 */
class PromotionalCodesValidatorService
{
    public function genValidNumber(string $numbers): string
    {
        $code = hexdec($numbers);

        if (strlen((string)$code) < 5) {
            for ($a = 0; $a < (5 - strlen($code)); $a++) {
                $code = $code . '0';
            }
        }

        $sumEvenIndexes = 0;
        $sumOddIndexes = 0;

        $eanAsArray = array_map('intval', str_split($numbers));

        for ($i = 0; $i < count($eanAsArray) - 1; $i++) {
            if ($i % 2 === 0) {
                $sumOddIndexes += $eanAsArray[$i];
            } else {
                $sumEvenIndexes += $eanAsArray[$i];
            }
        }

        $rest = ($sumOddIndexes + (3 * $sumEvenIndexes)) % 10;

        if ($rest !== 0) {
            $rest = 10 - $rest;
        }

        return dechex($rest);
    }

    public function validateKey(string $code): bool
    {
        $blocks = explode('-', $code);

        $block2 = substr($blocks[1], 0, 4);
        $valid2 = substr($blocks[1], 4, 1);
        $block3 = substr($blocks[2], 0, 4);
        $valid3 = substr($blocks[2], 4, 1);

        $check2 = $this->genValidNumber($block2);
        $check3 = $this->genValidNumber($block3);

        if (
            $valid2 != $check2 || $valid3 != $check3
            || !ctype_xdigit($blocks[1]) || !ctype_xdigit($blocks[2])
            || strlen($blocks[0]) != 7 || strlen($blocks[1]) != 5
            || strlen($blocks[2]) != 5
        ) {
            return false;
        } else {
            return true;
        }
    }
}
