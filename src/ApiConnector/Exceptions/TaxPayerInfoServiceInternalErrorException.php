<?php

namespace App\ApiConnector\Exceptions;

use Throwable;
use App\Exception\ClientSideException;

/**
 * Class NoTaxNumberGivenForTaxPayerInfoException
 *
 * @package AppBundle\Service\Exception
 */
class TaxPayerInfoServiceInternalErrorException extends ClientSideException
{
    /**
     * TaxPayerInfoServiceInternalErrorException constructor.
     *
     * @param string $message
     * @param int $code
     * @param Throwable|null $previous
     */
    public function __construct($message = "", $code = 0, Throwable $previous = null)
    {
        parent::__construct($message, $code, $previous, 'tax_payer_info_service_internal_error');
    }
}
