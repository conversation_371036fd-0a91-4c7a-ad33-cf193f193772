<?php

namespace App\ApiConnector;

use PhpAmqpLib\Channel\AMQPChannel;
use PhpAmqpLib\Connection\AMQPStreamConnection;
use PhpAmqpLib\Message\AMQPMessage;
use PhpAmqpLib\Exchange\AMQPExchangeType;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;

class RabbitMQConnector
{
    private ?AMQPChannel $channel = null;
    private ?AMQPStreamConnection $connection = null;
    private ?string $exchanger = null;

    private const QUEUE_PORT = 5672;
    private const QUEUE_USER = "beloyal-api";
    private const QUEUE_PASS = "E7j7NWRYRmxQP4h8";
    private const QUEUE_VHOST = "/bl";
    private const QUEUE_IP = "***********";

    public function __construct(
        ParameterBagInterface $parameterBag
    ) {
        $this->exchanger = "receipt-" . $parameterBag->get('receipt_company');
    }

    public function __destruct()
    {
        if (!is_null($this->channel)) {
            $this->channel->close();
            $this->connection->close();
        }
    }


    private function init()
    {
        if (is_null($this->channel)) {
            $connection = new AMQPStreamConnection(
                self::QUEUE_IP,
                self::QUEUE_PORT,
                self::QUEUE_USER,
                self::QUEUE_PASS,
                self::QUEUE_VHOST,
                false,
                'AMQPLAIN',
                null,
                'en_US',
                3.0,
                3.0,
                null,
                true
            );

            $channel = $connection->channel();
            $this->connection = $connection;

            $channel->exchange_declare(
                $this->exchanger,
                AMQPExchangeType::TOPIC,
                false,
                true,
                false
            );
            $this->channel = $channel;

            register_shutdown_function(
                [$this, 'shutdown'],
                $this->channel,
                $this->connection
            );
        }

        if (!$this->connection->isConnected()) {
            throw new \Exception("brak połączenia z Rabbit");
        }
    }

    private function shutdown($channel, $connection)
    {
        $channel->close();
        $connection->close();
    }

    public function publish($messageBody, $topic, $content_type = 'application/json')
    {
        $this->init();

        $properties = array(
            'content_type' => $content_type,
            'delivery_mode' => AMQPMessage::DELIVERY_MODE_PERSISTENT,
            'timestamp' => time()
        );

        $message = new AMQPMessage($messageBody, $properties);

        $this->channel->basic_publish($message, $this->exchanger, $topic);

        return null;
    }
}
