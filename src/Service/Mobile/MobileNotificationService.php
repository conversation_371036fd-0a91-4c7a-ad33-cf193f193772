<?php

namespace App\Service\Mobile;

use App\Entity\MobileToken;
use App\Entity\User;
use App\Repository\LoggerRepository;
use Doctrine\ORM\EntityManagerInterface;
use Kreait\Firebase\Contract\Messaging;
use Kreait\Firebase\Messaging\AndroidConfig;
use Kreait\Firebase\Messaging\CloudMessage;
use Kreait\Firebase\Messaging\Notification;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class MobileNotificationService
{
    public function __construct(
        private readonly Messaging $messaging,
        private readonly EntityManagerInterface $entityManager,
        private readonly LoggerRepository $loggerRepository,
    ) {
    }

    /**
     * Wysyła powiadomienie push do wszystkich urządzeń zasubskrybowanych do danego topicu.
     * Powiadomienia na topic są zoptymalizowane pod kątem przepustowości, a nie opóźnień.
     * <PERSON><PERSON> szybkiego dostarczania do pojedync<PERSON>ch urządzeń, lepiej uży<PERSON>ć sendNotification.
     */
    public function sendTopicNotification(string $topic, string $title, string $body, array $data = []): void
    {
        try {
            $this->loggerRepository->notice(
                user: null,
                type: "TOPIC_NOTIFICATION",
                identifier: null,
                comment: sprintf(
                    "sending topic notification - topic: %s, title: %s, body: %s, data: %s",
                    $topic,
                    $title,
                    $body,
                    json_encode($data)
                )
            );

            // Konfiguracja dla Androida (z ikoną)
            $androidConfig = AndroidConfig::fromArray([
                'notification' => [
                    'icon' => 'ic_stat_notification', // Twoja ikona umieszczona w res/drawable/
                ],
            ]);

            $message = CloudMessage::new()
                ->toTopic($topic)
                ->withNotification(Notification::create($title, $body))
                ->withData($data)
                ->withAndroidConfig($androidConfig);

            $this->messaging->send($message);
        } catch (\Exception $e) {
            throw $e;
        }
    }


    /**
     * Wysyła powiadomienie push do wszystkich urządzeń użytkownika.
     * Jeśli token urządzenia jest nieważny, zostanie automatycznie usunięty z bazy.
     * W przypadku innych błędów podczas wysyłania do konkretnego urządzenia, błąd jest ignorowany.
     */
    public function sendNotification(int $userId, string $title, string $body, array $data = []): void
    {
        try {
            $user = $this->getUser($userId);
            $tokens = $this->getUserTokens($user);

            $this->loggerRepository->notice(
                user: $user,
                type:"NOTIFICATION",
                identifier: null,
                comment: sprintf(
                    "sending mobile notification - title: %s, body: %s, data: %s",
                    $title,
                    $body,
                    json_encode($data)
                )
            );

            if (empty($tokens)) {
                return;
            }

            $invalidTokens = [];
            foreach ($tokens as $token) {
                try {
                    // Konfiguracja dla Androida (z ikoną)
                    $androidConfig = AndroidConfig::fromArray([
                        'notification' => [
                            'icon' => 'ic_stat_notification', // Twoja ikona umieszczona w res/drawable/
                        ],
                    ]);

                    $message = CloudMessage::new()
                        ->toToken($token)
                        ->withNotification(Notification::create($title, $body))
                        ->withData($data)
                        ->withAndroidConfig($androidConfig);

                    $this->messaging->send($message);
                } catch (\Kreait\Firebase\Exception\Messaging\NotFound $e) {
                    $invalidTokens[] = $token;
                } catch (\Exception $e) {
                    // Ciche ignorowanie pozostałych błędów
                }
            }

            if (!empty($invalidTokens)) {
                $this->removeInvalidTokens($user, $invalidTokens);
            }
        } catch (\Exception $e) {
            throw $e;
        }
    }

    /**
     * Zapisuje lub aktualizuje token urządzenia mobilnego dla użytkownika.
     * Jeśli token już istnieje, zostanie zaktualizowana data jego modyfikacji.
     * Jeśli token nie istnieje, zostanie utworzony nowy wpis w bazie danych.
     */
    public function storeUserMobileToken(User $user, string $tokenString, string $deviceInfo = ''): void
    {
        $token = $this->findUserToken($user, $tokenString);

        if ($token === null) {
            $token = $this->createNewMobileToken($user, $tokenString, $deviceInfo);
        }

        $token->setUpdatedAt(new \DateTimeImmutable());
        $user->addMobileToken($token);
        $this->entityManager->persist($token);
        $this->entityManager->flush();
    }

    /**
     * Usuwa token urządzenia mobilnego użytkownika z bazy danych.
     * Jeśli token nie istnieje, metoda zakończy działanie bez błędu.
     */
    public function removeUserMobileToken(User $user, string $tokenString): void
    {
        $token = $this->findUserToken($user, $tokenString);

        if ($token === null) {
            return;
        }

        $user->removeMobileToken($token);
        $this->entityManager->remove($token);
        $this->entityManager->flush();
    }

    private function getUser(int $userId): User
    {
        $user = $this->entityManager->getRepository(User::class)->find($userId);
        if (!$user) {
            throw new NotFoundHttpException('User not found');
        }
        return $user;
    }

    private function getUserTokens(User $user): array
    {
        return $user->getMobileTokens()
            ->map(fn(MobileToken $token) => $token->getToken())
            ->toArray();
    }

    private function removeInvalidTokens(User $user, array $invalidTokens): void
    {
        $tokens = $this->entityManager->getRepository(MobileToken::class)
            ->findBy(['user' => $user, 'token' => $invalidTokens]);

        foreach ($tokens as $token) {
            $this->entityManager->remove($token);
        }

        $this->entityManager->flush();
    }

    private function findUserToken(User $user, string $tokenString): ?MobileToken
    {
        return $this->entityManager
            ->getRepository(MobileToken::class)
            ->findOneBy(['user' => $user, 'token' => $tokenString]);
    }

    private function createNewMobileToken(User $user, string $tokenString, string $deviceInfo = ''): MobileToken
    {
        return (new MobileToken())
            ->setUser($user)
            ->setToken($tokenString)
            ->setMobileDevice($deviceInfo);
    }
}
