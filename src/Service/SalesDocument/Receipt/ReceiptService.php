<?php

namespace App\Service\SalesDocument\Receipt;

use App\ApiConnector\RabbitMQConnector;
use App\Entity\Enum\Product;
use App\Entity\MobilePayment;
use App\Service\AppConfig\AppConfigService;
use Exception;
use Symfony\Contracts\Translation\TranslatorInterface;

class ReceiptService
{
    public function __construct(
        private RabbitMQConnector $rabbitMQ,
        private AppConfigService $appConfigService,
        private TranslatorInterface $translator
    ) {
    }

    public function newReceipt(MobilePayment $mobilePayment)
    {

        $url = $this->appConfigService->getBackendUrl() . '/api/v3/receipt/receive';

        $receipt = [
            'data' => [
                'url' => $url,
                'client' => [
                    'nip' => $mobilePayment->getBkfpayCompany()?->getClient()?->getTaxNumber(),
                    'email' => $mobilePayment->getBkfpayCompany()?->getEmail(),
                    'paymentId' => (string) $mobilePayment->getId(),
                ],
                'item' => [
                    [
                        'name' => $this->translator->trans($mobilePayment->getProductName()),
                        'qtty' => (string) $mobilePayment->getExternalPayment()->getValue(),
                        'price' => 100
                    ]
                ]
            ],
            'type' => 'receipt',
            'date' => '2020-01-01T21:04:12.207299'
        ];

        $this->rabbitMQ->publish(
            json_encode($receipt),
            'receipt'
        );
    }
}
