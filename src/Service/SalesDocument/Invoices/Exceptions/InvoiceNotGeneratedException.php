<?php

namespace App\Service\SalesDocument\Invoices\Exceptions;

use App\Exception\ClientSideException;
use Throwable;

/**
 * Class InvoiceNotGeneratedException
 * @package AppBundle\Service\Invoices\Exceptions
 */
class InvoiceNotGeneratedException extends ClientSideException
{
    /**
     * InvoiceNotGeneratedException constructor.
     * @param string $message
     * @param int $code
     * @param Throwable|null $previous
     */
    public function __construct($message = "", $code = 0, Throwable $previous = null)
    {
        parent::__construct($message, $code, $previous, 'invoice_not_generated');
    }
}
