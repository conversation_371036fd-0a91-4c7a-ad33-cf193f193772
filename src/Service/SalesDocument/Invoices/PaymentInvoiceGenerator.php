<?php

namespace App\Service\SalesDocument\Invoices;

use App\Entity\Invoices;
use App\Entity\MobilePayment;
use App\Repository\InvoicesRepository;
use App\Service\AppConfig\AppConfigService;
use App\Service\SalesDocument\Invoices\Exceptions\InvoiceNotGeneratedException;
use I2m\Invoices\Enum\InvoiceGeneratorType;
use I2m\Invoices\Enum\PaymentStatus;
use I2m\Invoices\Service\InvoiceGenerator\InvoiceGeneratorAbstract;
use I2m\Invoices\Service\InvoiceGenerator\InvoiceGeneratorFactory;
use I2m\Invoices\Service\InvoiceService;

/**
 * Class PaymentInvoiceGenerator
 *
 * @package AppBundle\Service\Invoices\InvoiceGenerator
 */
class PaymentInvoiceGenerator
{
    public function __construct(
        private ExternalPaymentInvoiceDataProvider $externalPaymentInvoiceDataProvider,
        private InvoiceService $invoiceService,
        private InvoicesRepository $invoicesRepository,
        private AppConfigService $appConfigService,
        private InvoiceGeneratorFactory $generatorFactory,
        private InvoiceSender $invoiceSender,
    ) {
    }


    public function generateInvoice(MobilePayment $mobilePayment): ?Invoices
    {
        if ($mobilePayment->getInvoice()) {
            throw new InvoiceNotGeneratedException('Invoice instance already generated for this payment');
        }

        $issuer = $this->appConfigService->getAppIssuer();
        if (
            $issuer->getInvoiceType() == InvoiceGeneratorType::Disabled
        ) {
            return null;
        }

        $invoice = $this->externalPaymentInvoiceDataProvider->getInvoiceInstance($mobilePayment);

        $this->invoiceService->generate($issuer->getInvoiceType(), $issuer->getInvoiceConfig(), $invoice);
        $this->invoicesRepository->save($invoice);

        $this->send($invoice);

        return null;
    }
    public function send(Invoices $invoices)
    {
        $status = $this->getGenerator()->send($invoices);
        if (is_null($status)) {
            $status = $this->invoiceSender->sendInvoiceWithEmail($invoices);
        }

        if ($status) {
            $invoices->setPublished(true);
            $invoices->setSendDate(new \DateTimeImmutable('now'));
            $this->invoicesRepository->save($invoices);
        }
    }

    public function status(Invoices $invoices): PaymentStatus
    {
        return $this->getGenerator()->getStatus($invoices);
    }

    private function getGenerator(): InvoiceGeneratorAbstract
    {
        $issuer = $this->appConfigService->getAppIssuer();
        return $this->generatorFactory->create($issuer->getInvoiceType(), $issuer->getInvoiceConfig());
    }
}
