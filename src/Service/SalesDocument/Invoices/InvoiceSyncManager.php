<?php

namespace App\Service\SalesDocument\Invoices;

use App\Entity\Invoices;
use App\Repository\InvoicesRepository;
use Doctrine\ORM\EntityManagerInterface;
use I2m\Invoices\Enum\PaymentStatus;
use I2m\Payment\Enum\Status;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Command\Command;

use function Sentry\captureMessage;

class InvoiceSyncManager
{
    private string $commandName = 'Invoice Sync Command:';

    public function __construct(
        private LoggerInterface $logger,
        private EntityManagerInterface $entityManager,
        private InvoicesRepository $invoicesRepository,
        private PaymentInvoiceGenerator $paymentInvoiceGenerator
    ) {
    }

    public function syncInvoicesStatus(): int
    {
        $invoicesToSync = $this->invoicesRepository->getList(statuses: [Status::WAITING->value]);

        $this->logger->notice($this->commandName . " - pobranych faktur: " . $invoicesToSync['totalItems']);

        foreach ($invoicesToSync['items'] as $invoice) {
            /** @var Invoices $invoice */
            if ($invoice->getExternalId() === null) {
                $errorMessage = "{$this->commandName}"
                    . " Brak external id dla faktury: {$invoice->getNumber()}";
                $this->logMessage(
                    $errorMessage
                );
                continue;
            }


            $status = $this->paymentInvoiceGenerator->status($invoice);

            if ($status == PaymentStatus::Paid) {
                $externalPayment = $invoice->getExternalPayment();
                $externalPayment->setStatus(Status::CONFIRMED);

                $this->entityManager->persist($externalPayment);
                $this->entityManager->flush();
            }
        }

        return Command::SUCCESS;
    }

    private function logMessage(string $errorMessage)
    {
        captureMessage(
            $errorMessage
        );
        $this->logger->error(
            $errorMessage
        );
    }
}
