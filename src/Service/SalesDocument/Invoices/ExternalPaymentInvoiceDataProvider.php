<?php

namespace App\Service\SalesDocument\Invoices;

use App\Entity\Enum\InvoiceSource;
use App\Entity\ExternalPayment;
use App\Entity\Invoices;
use App\Entity\MobilePayment;
use App\Repository\InvoicesRepository;
use App\Service\AppConfig\AppConfigService;
use I2m\Invoices\Enum\InvoiceKindType;
use I2m\StandardTypes\Enum\Country;
use Symfony\Contracts\Translation\TranslatorInterface;

class ExternalPaymentInvoiceDataProvider
{
    public function __construct(
        private AppConfigService $appConfigService,
        private InvoicesRepository $invoicesRepository,
        private TranslatorInterface $translator
    ) {
    }

    public function getInvoiceInstance(
        MobilePayment $mp
    ): ?Invoices {
        $issuenceDate = new \DateTime();
        $invoice = (new Invoices())
            ->setCtime($issuenceDate)
            ->setInvoiceDate($issuenceDate)
            ->setMobilePayment($mp)
            ->setPaymentDate(self::getPaymentDate($mp))
            ->setPaymentMethod(self::getPaymentMethod($mp))
            ->setPeriod($this->getCurrentPeriod($issuenceDate))
            ->setUser($mp->getBkfpayCompany())
            ->setClient($mp->getBkfpayCompany()->getClient())
            ->setCurrency($this->appConfigService->getCurrency())
            ->setIssuer($this->appConfigService->getAppIssuer())
            ->setKind($this->getKindType($mp))
            ->setSource(InvoiceSource::CLIENT)
            ->setDescription(self::getDescription($mp))
            ->setExternalPayment($mp->getExternalPayment())
        ;




        //$this->setInvoiceNumber($invoice);
        $invoice->addPosition($this->translator->trans($mp->getProductName()), 1, $mp->getExternalPayment()->getValue(), $this->appConfigService->getVatTax()->getTaxValue());
        $this->invoicesRepository->save($invoice);
        $mp->setInvoice($invoice);
        $this->invoicesRepository->save($invoice);
        return $invoice;
    }

    /**
     * Return current period using server php timezone
     *
     * @return string
     */
    public function getCurrentPeriod(\DateTimeInterface $date = null)
    {
        return \DateTime::createFromInterface($date)->format("Y-m");
    }
    public static function getPaymentMethod(MobilePayment $mp): string
    {
        $issuer = $mp->getExternalPayment()->getIssuer();
        switch ($issuer) {
            case ExternalPayment::ISSUER_CREDIT_CARD:
                return Invoices::PAYMENT_METHOD_CREDIT_CARD;
            case ExternalPayment::ISSUER_TRANSFER:
                return Invoices::PAYMENT_METHOD_POST_PAID;
        }

        if ($mp->getExternalPayment()->getGate()) {
            return $mp->getExternalPayment()->getGate()->getType()->value;
        }

        throw new \Exception("Nieznany dostawca płatności $issuer dla fakturowania");
    }

    public static function getPaymentDate(MobilePayment $mp): ?\DateTimeImmutable
    {
        $interval = new \DateInterval($mp->getExternalPayment()->getPaymentTerm());
        return (new \DateTimeImmutable())->add($interval);
    }

    public function getKindType(MobilePayment $mp): InvoiceKindType
    {
        // specjalny przypadek dla Szwecji Washstop,
        // gdzie po otrzymaniu płatności wysyłamy kvitto (paragon),
        // a przy płatnosci odroczonej przelewowej wystawiamy vat (wezwanie do zapłaty)
        if (
            $this->appConfigService->getAppIssuer()->getCountry()
            == Country::SE
        ) {
            switch ($mp->getExternalPayment()->getIssuer()) {
                case ExternalPayment::ISSUER_TRANSFER:
                    return InvoiceKindType::vat;
                default:
                    return InvoiceKindType::receipt;
            }
        }

        return InvoiceKindType::vat;
    }

    public static function getDescription(MobilePayment $mp): ?string
    {
        $description = null;
        if ($mp->getLicensePlate()) {
            $description .= $mp->getLicensePlate() . '\n';
        }
        return $description;
    }
}
