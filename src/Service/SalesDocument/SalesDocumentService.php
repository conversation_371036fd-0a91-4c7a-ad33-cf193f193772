<?php

namespace App\Service\SalesDocument;

use App\Entity\Enum\MobilePaymentStatus;
use App\Entity\MobilePayment;
use App\Entity\User;
use App\Repository\MobilePaymentRepository;
use App\Service\SalesDocument\Invoices\PaymentInvoiceGenerator;
use App\Service\SalesDocument\Receipt\ReceiptService;
use Doctrine\Persistence\ManagerRegistry;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;
use Symfony\Component\Messenger\MessageBusInterface;

use function Sentry\captureMessage;

#[AsMessageHandler]
class SalesDocumentService
{
    public function __construct(
        protected ?MessageBusInterface $messenger,
        private MobilePaymentRepository $mobilePaymentRepository,
        private ReceiptService $receiptService,
        private LoggerInterface $logger,
        private PaymentInvoiceGenerator $paymentInvoiceGenerator,
        private ManagerRegistry $managerRegistry
    ) {
    }
    public function isTestAccount(User $user): bool
    {
//        // konta służbowe do użytku prywantnego
//        if (
//            in_array($user->getEmail(), [
//                '<EMAIL>',
//                '<EMAIL>',
//                '<EMAIL>',
//                '<EMAIL>',
//                '<EMAIL>'
//            ])
//        ) {
//            return false;
//        }

        // czy email jest w domenie BKF lub podobnej
        if (preg_match('/@(i2m\.pl|ebkf\.eu)$/', $user->getEmail())) {
            return true;
        }

        // dodatkowe konta wyłaczone z fakturowania
        if (
            in_array($user->getEmail(), [
                '<EMAIL>',
            //'<EMAIL>',
            //'<EMAIL>'
            ])
        ) {
            return true;
        }

        // nip i2m
        if ($user->getClient()?->getTaxNumber() == "851-316-52-36") {
            return true;
        }
        return false;
    }
    public function generateSync(MobilePayment $mp)
    {
        if (is_null($mp->getExternalPayment())) {
            $this->notice("Dziwne że tutaj trafiłem, bo nie ma płątności {$mp->getId()}");
            return ;
        }

        // docuemnt already created
        if ($mp->getInvoice() || $mp->getReceipt()) {
            $this->notice("Faktura lub paragon juz wygenerowany, a jeszcze nie powinien, {$mp->getId()}");
            return;
        }

        //
        if ($mp->getStatus() !== MobilePaymentStatus::CONFIRMED) {
            $this->notice("Transakcja niepotwierdzona, a próbuje wystawić dokument {$mp->getId()}");
            return;
        }

        //
        if (is_null($mp->getConfirmedTimestamp())) {
            $this->notice("brakuje daty potwierdzenia transakcji {$mp->getId()}");
            return;
        }

        if (!$mp->getExternalPayment()->isReadyToSalesDocument()) {
            $this->notice("płatność niepotwierdzona, a próbuje wystawić dokument mp: {$mp->getId()}");
            return;
        }

        if (is_null($mp->getProduct())) {
            $this->notice(
                "brakuje produktu dla którego generujemy dokument
                dla użytkownika {$mp->getBkfpayUser()->getEmail()} i transakcji {$mp->getId()}"
            );
            return;
        }

        if ($this->isTestAccount($mp->getBkfpayCompany())) {
            $this->notice(
                "nie wystawiam dokumentu księgowego na {$mp->getBkfpayCompany()->getClient()?->getName()}
                dla użytkownika {$mp->getBkfpayUser()->getEmail()} i transakcji {$mp->getId()}"
            );
            return;
        }

        if ($mp->getBkfpayCompany()->getClient()?->isInvoicedAfterTransaction()) {
            $this->generateInvoice($mp);
        } else {
            $this->generateReceipt($mp);
        }
    }


    private function generateInvoice(MobilePayment $mp)
    {
        if (!$mp->getBkfpayCompany()->isInvoiceDataFilled()) {
            $this->notice(
                "nie wystawiam faktury na {$mp->getBkfpayCompany()->getClient()?->getName()}" .
                "dla użytkownika {$mp->getBkfpayUser()->getEmail()} i transakcji {$mp->getId()}" .
                "ponieważ dane firmy nie są poprawnie wypełnione"
            );
            return;
        }

        $this->logger->notice("generuje fakturę dla transakcji {$mp->getId()}" .
            " na firme {$mp->getBkfpayCompany()->getClient()->getName()}, " .
            " za płatność: {$mp->getExternalPayment()->getIssuer()}");
        $this->paymentInvoiceGenerator->generateInvoice($mp);
    }

    private function generateReceipt(MobilePayment $mp)
    {
        $this->logger->notice("generuje paragon dla transakcji {$mp->getId()}" .
            " dla uzytkownika {$mp->getBkfpayCompany()->getEmail()}, " .
            " za płatność: {$mp->getExternalPayment()->getIssuer()}");
        $this->receiptService->newReceipt($mp);
    }

    // funkcje asynchronczne
    public function generateAsync(MobilePayment $mp)
    {
        $this->messenger->dispatch(new SalesDocumentMessage($mp->getId()));
    }

    public function __invoke(SalesDocumentMessage $doc)
    {

        $this->managerRegistry->resetManager();

        $mp = $this->mobilePaymentRepository->find($doc->getMp());
        $this->generateSync($mp);

        return null;
    }

    public function notice(string $message): void
    {
        $this->logger->notice($message);
        captureMessage($message);
    }
}
