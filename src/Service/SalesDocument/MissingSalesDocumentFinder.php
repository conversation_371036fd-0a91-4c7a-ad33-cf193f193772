<?php

namespace App\Service\SalesDocument;

use App\Entity\Client;
use App\Entity\Enum\AlertLevel;
use App\Entity\Enum\MobilePaymentStatus;
use App\Entity\ExternalPayment;
use App\Entity\User;
use App\Repository\ExternalPaymentRepository;
use App\Service\ProfileInfo\Alert;
use I2m\Payment\Enum\Status;

class MissingSalesDocumentFinder
{
    public array $alerts = [];

    public function __construct(
        private ExternalPaymentRepository $externalPaymentRepository,
        private SalesDocumentService $salesDocumentService,
    ) {
    }

    public function generate(bool $generateMissingInvoices = false): array
    {
        $from = new \DateTime("-2 months");
        $to = new \DateTime("-1 hours");
        $this->alerts = [];
        $this->checkMissingDocuments($from, $to, $generateMissingInvoices);

        return $this->alerts;
    }

    private function createAlert(string $title, string $message): void
    {
        $this->alerts[] = (new Alert())
            ->setLevel(AlertLevel::WARNING)
            ->setTitle($title)
            ->setText($message);
    }

    public function checkMissingDocuments(
        \DateTime $from,
        \DateTime $to,
        bool $generateMissingInvoices = false
    ): void {
        $eps = $this->externalPaymentRepository->findMissingSalesDocs($from, $to);
        foreach ($eps as $ep) {
            /** @var ExternalPayment $ep */

            /** @var User $user */
            $user = $ep->getUser();
            /** @var ?Client $client */
            $client = $user->getClient();
            $docType = $client?->isInvoicedAfterTransaction() ? "Faktura" : "Paragon";

            if ($ep->getStatus() != Status::CONFIRMED) {
                throw new \Exception("Nieprawidłowy status płatności");
            }

            if ($this->salesDocumentService->isTestAccount($user)) {
                $this->createAlert(
                    "Brak dokumentu sprzedaży, użytkownika i2m/bkf",
                    "{$ep->getConfirmedTimestamp()->format("Y-m-d H:i")}: " .
                    "Dla  płatności {$ep->getIssuer()} ({$ep->getExternalId()}) " .
                    "nie wygenerowano dokumentu sprzedaży zw. na  {$ep->getMobilePayment()->getProduct()->value} (mp. {$ep->getMobilePayment()->getId()}) " .
                    "\n\tdokonaj zwrotu klientowi {$ep->getUser()->getEmail()} na kwotę {$ep->getValue()} lub wystaw $docType ręcznie",
                );
                continue;
            }

            if ($ep->getMobilePayment()->getStatus() != MobilePaymentStatus::CONFIRMED) {
                $this->createAlert(
                    "Nie udało się wydać usługi po pobraniu płatności",
                    "{$ep->getConfirmedTimestamp()->format("Y-m-d H:i")}: " .
                    "Dla płatności  płatności {$ep->getIssuer()} ({$ep->getExternalId()}) " .
                    "nie udało się wykonać usługi {$ep->getMobilePayment()->getProduct()->value} (transakcja {$ep->getMobilePayment()->getId()}) " .
                    "\n\tdokonaj zwrotu klientowi {$ep->getUser()->getEmail()}, na kwotę {$ep->getValue()}",
                );
                continue;
            }

            $this->createAlert(
                "Brak dokumentu sprzedaży",
                "{$ep->getConfirmedTimestamp()->format("Y-m-d H:i")}: " .
                "Brakuje $docType dla: {$ep->getUser()->getEmail()}, nip: {$client?->getTaxNumber()} " .
                "do płatności {$ep->getIssuer()} ({$ep->getExternalId()}) " .
                "na kwotę {$ep->getValue()} " .
                "dla transakcji {$ep->getMobilePayment()->getId()}",
            );

            if ($generateMissingInvoices) {
                $this->salesDocumentService->generateAsync($ep->getMobilePayment());
            }
        }
    }
}
