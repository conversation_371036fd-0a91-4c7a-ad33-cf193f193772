<?php

namespace App\Service\BkfPay;

use App\Entity\MpApiConfig;
use App\Repository\LoggerRepository;
use App\Repository\MpApiConfigRepository;
use App\Repository\UserRepository;
use Symfony\Component\Config\Definition\Exception\Exception;
use Symfony\Component\HttpFoundation\Request;

use function Sentry\captureMessage;

/**
 * Class VerifyMobilePaymentRequest
 *
 * @package App\MobilePayment\MobilePaymentSecurity
 */
class VerifyMobilePaymentRequest
{
    /**
     * VerifyMobilePaymentRequest constructor.
     *
     */
    public function __construct(private MpApiConfigRepository $apiConfigRepository, private LoggerRepository $loggerRepository)
    {
    }

    public function verifyRequest(Request $request): array
    {
        $recivedDigest = (string)$request->headers->get('X-HMAC-DIGEST');

        $inputData = json_decode($request->getContent(), true);

        if (!(isset($inputData) && isset($inputData['methodId']) && isset($inputData['timestamp']))) {
            throw new Exception('invalidFormat;Invalid JSON');
        }

        $apiConfig = $this->apiConfigRepository->findOneBy(['paySystem' => $inputData['pay_system']]);

        if (is_null($apiConfig)) {
            throw new Exception('invalidPaysystem;Invalid pay system');
        }

        if (empty($apiConfig->getApiKey())) {
            captureMessage("Brak hasła dla użytkownika {$inputData['pay_system']}");
            throw new Exception('invalidDigest;Invalid password');
        }

        $calculatedDigest = $this->getDigest($request->getContent(), $apiConfig->getApiKey(), $inputData['timestamp']);
        if ($recivedDigest !== $calculatedDigest) {
            $this->loggerRepository->log($apiConfig->getUser(), "MOBILE_PAYMENT", $inputData['methodId'], "MP digest error: $recivedDigest !== $calculatedDigest");
            throw new Exception('invalidDigest;Invalid password');
        }

        $inputData['user'] = $apiConfig->getUser();

        return $inputData;
    }

    public function getDigest(string $message, string $secret, string $timestamp): string
    {
        return hash_hmac(
            'md5',
            $message,
            $secret . '?' . $timestamp
        );
    }
}
