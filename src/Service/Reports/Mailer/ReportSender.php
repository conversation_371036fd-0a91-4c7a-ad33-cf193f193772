<?php

namespace App\Service\Reports\Mailer;

use App\Entity\ReportFile;
use App\Entity\ReportsConfig;
use App\Service\AppConfig\AppConfigService;
use I2m\Reports\Service\ReportService;
use Symfony\Bridge\Twig\Mime\TemplatedEmail;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\Mailer\MailerInterface;
use Symfony\Component\Mime\Address;
use Symfony\Contracts\Translation\TranslatorInterface;

/**
 * Class InvoiceSender
 * @package App\Invoices\InvoiceSender
 */
class ReportSender
{
    private string $noReplyEmailAddress;
    /**
     * InvoiceSender constructor.
     *
     */
    public function __construct(
        private TranslatorInterface $translator,
        private MailerInterface $mailer,
        ParameterBagInterface $parameterBag,
        private AppConfigService $appConfig,
        private ReportService $reportService
    ) {
        $this->noReplyEmailAddress = $parameterBag->get('mailer_address_no_replay');
    }

    public function send(
        ReportFile $reportFile,
        ReportsConfig $reportConfig,
        array $hiddenCopyAdreses,
        string $sendTo,
    ) {
        $subject = $this->translator->trans('report_email_title', [], null, $this->appConfig->getLanguage()->value)
            . ' ' . $reportConfig->getName();

        $email = (new TemplatedEmail())
            ->from(new Address($this->noReplyEmailAddress, $this->appConfig->getAppName()))
            ->to(new Address($sendTo))
            ->subject($subject)
            ->htmlTemplate('reports/mail_content.html.twig')
            ->context([
                'userEmail' => $reportConfig->getUser()->getEmail(),
                'reportName' => $reportConfig->getName(),
                'language' => $this->appConfig->getLanguage()->value,
            ])
            ->attach(
                $this->reportService->getStream($reportFile),
                $reportConfig->getName() . '.' . $reportConfig->getExt()->value
            );

        foreach ($hiddenCopyAdreses as $emailCopy) {
            $email->addBcc(new Address($emailCopy));
        }

        $this->mailer->send($email);

        return true;
    }
}
