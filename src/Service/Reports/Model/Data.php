<?php

namespace App\Service\Reports\Model;

use Symfony\Component\Serializer\Annotation\Groups;

class Data
{
    public function __construct(
        private array $data = [], // lista elementow w tablicy (tylko z zakresu paginacji)
        private ?array $pageSum = null, // podsumowanie strony na koncu tabeli
        private ?array $totalSum = null, // podsumowanie wszystkich wyników bez paginacji
        private ?int $total = null, // liczba wszytkich elementów, przy paginacji
        private ?array $filters = null, // ustawienia filtrów (np. lista myjni, statusów)
        private ?array $criteria = null,
    ) {
    }

    #[Groups(["report:data", "report:config:list"])]
    public function getData(): array
    {
        return $this->data;
    }

    #[Groups(["report:data", "report:config:list"])]
    public function getTotal(): ?int
    {
        return $this->total;
    }
    #[Groups(["report:data"])]
    public function getFilters(): array
    {
        return $this->filters;
    }
    #[Groups(["report:data"])]
    public function getPageSum(): ?array
    {
        return $this->pageSum;
    }
    #[Groups(["report:data"])]
    public function getTotalSum(): ?array
    {
        return $this->totalSum;
    }

    public function getCriteria(): ?array
    {
        return $this->criteria;
    }

    public function isGenerated()
    {
        return !empty($this->data);
    }
}
