<?php

namespace App\Service\Reports;

use App\Entity\Enum\Languages;
use I2m\Reports\Enum\ReportInterval;
use App\Entity\Enum\ReportType;
use App\Entity\ReportFile;
use App\Entity\ReportsConfig;
use App\Entity\User;
use App\Repository\ReportFileRepository;
use App\Service\AppConfig\AppConfigService;
use App\Service\Reports\Data\ReportDataAbstract;
use App\Service\Reports\Data\ReportDataFactory;
use I2m\Reports\Enum\FileExtention;
use I2m\Reports\Enum\ReportStatus;
use I2m\Reports\Model\ReportDocument;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Translation\Translator;
use Symfony\Contracts\Translation\TranslatorInterface;
use I2m\Reports\Service\ReportService;

class ReportGenerator
{
    public function __construct(
        private TranslatorInterface $translator,
        private ReportDataFactory $dataFactory,
        private ReportFileRepository $reportFileRepository,
        private AppConfigService $appConfigService,
        private ReportService $reportService,
    ) {
    }

    public function generateFile(ReportsConfig $reportConfig): ?ReportFile
    {
        $title = $reportConfig->getName();
        $user = $reportConfig->getUser();

        $lang = $user?->getLocale() ?? $this->appConfigService->getLanguage()->value;

        /** @var Translator $translator */
        $translator = $this->translator;
        $translator->setLocale($lang);

        $reportFile = new ReportFile();
        $reportFile
            ->setType($reportConfig->getReportType())
            ->setUser($reportConfig->getUser())
            ->setExt($reportConfig->getExt())
            ->setCriterias($reportConfig->getConfig())
            ->setStatus(ReportStatus::NEW)
            ->setEmail($reportConfig->getEmail())
            ->setTitle($title ?? $this->getTranslatedReportName($reportConfig, $lang))
            ->setEmailText($this->getTranslatedEmailText($reportFile))
            ->setReportInterval($reportConfig->getReportInterval())
        ;
        $this->reportFileRepository->save($reportFile);


        $reportDocument = $this->getFile($reportFile);

        return $reportFile;
    }
    public function getTranslatedReportName(ReportsConfig $reportConfig, string $langShort): string
    {
        /** @var Translator $translator */
        $translator = $this->translator;
        $translator->setLocale($langShort);

        $orgName = $this
            ->dataFactory
            ->getReport(
                reportName: $reportConfig->getReportType(),
                criteria: $reportConfig->getConfig(),
                user: $reportConfig->getUser(),
            )->getTitle();
        return $translator->trans($orgName);
    }

    private function getTranslatedEmailText(ReportFile $reportFile): string
    {
        $lang = $reportFile->getUser()?->getLocale() ?? $this->appConfigService->getLanguage()->value;
        /** @var Translator $translator */
        $translator = $this->translator;
        $translator->setLocale($lang);

        $orgName = $this
            ->dataFactory
            ->getReport(
                reportName: $reportFile->getType(),
                criteria: $reportFile->getCriterias(),
                user: $reportFile->getUser()
            )->getEmailText();
        return $translator->trans($orgName);
    }

    public function getConfig(Request $request, User $user): ReportsConfig
    {
        $email = $request->get("email");
        $ext = $request->get("ext", 'pdf');
        $groups = $request->get("groups");
        $items = $request->get("items");
        $interval = $request->get("interval", "custom");
        $type = $request->get("type");
        $users = $request->get("users");
        $name = $request->get("name", $type);

        $queryParams = $request->query->all();

        unset($queryParams['type']);
        unset($queryParams['interval']);
        unset($queryParams['groups']);
        unset($queryParams['email']);
        unset($queryParams['users']);
        unset($queryParams['name']);
        unset($queryParams['ext']);

        $reportConfig = (new ReportsConfig())
            ->setConfig($queryParams) // musi być pierwsze, poniewaz zaraz będzie nadpisywane
            ->setEmail($email)
            ->setExt(FileExtention::tryFrom($ext))
            ->setGroups($groups ? explode(",", $groups) : null)
            ->setItems($items ? explode(",", $items) : null)
            ->setName($name)
            ->setUser($user)
            ->setUsers($users)
            ->setReportType(ReportType::from($type))
            ->setReportInterval(ReportInterval::from($interval))
        ;
        return $reportConfig;
    }

    private function getFile(ReportFile $reportFile): ReportDocument
    {
        $data = $this
            ->dataFactory
            ->getReport(
                reportName: $reportFile->getType(),
                criteria: $reportFile->getCriterias(),
                user: $reportFile->getUser(),
                interval: $reportFile->getReportInterval()
            );

        $tz = $this->appConfigService->getTimezone();
        $this->reportFileRepository->process($reportFile);
        $reportDocument = $this->reportService->generate($data, $reportFile, new \DateTime("now", $tz));
        $this->reportFileRepository->done($reportFile);

        return $reportDocument;
    }
}
