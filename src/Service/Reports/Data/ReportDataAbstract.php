<?php

namespace App\Service\Reports\Data;

use DateTime;
use I2m\Reports\Enum\ReportInterval;
use App\Entity\User;
use App\Service\AppConfig\AppConfigService;
use App\Service\Reports\Model\Data;
use DateTimeZone;
use I2m\Reports\Enum\FileExtention;
use I2m\Reports\Interface\ReportDataInterface;
use Symfony\Bundle\FrameworkBundle\Translation\Translator;
use Symfony\Contracts\Translation\TranslatorInterface;

abstract class ReportDataAbstract implements ReportDataInterface
{
    private array $criteria;
    private ?User $user;
    private ?ReportInterval $interval = null;

    public function __construct(
        private TranslatorInterface $translator,
        private AppConfigService $appConfigService
    ) {
    }

    abstract public function getEmailText(): ?string;
    abstract public function getData(?int $page = null, ?int $perPage = null): Data;

    public function setConfig(
        array $criteria,
        ?User $user = null,
        ?ReportInterval $interval = null,
    ): ReportDataAbstract {
        $this->user = $user;
        $this->criteria = $criteria;
        $this->interval = $interval;
        return $this;
    }

    public function getTwigTemplate(): string
    {
        return
            "reports" . DIRECTORY_SEPARATOR .
            "TableReport.html.twig";
    }

    private function getBaseTemplate(): string
    {
        return
            "reports" . DIRECTORY_SEPARATOR .
            "report.base.html.twig";
    }

    public function getCriteria(): array
    {
        return $this->criteria;
    }

    public function getInfo(): array
    {
        return
        [
            'baseTemplate' => $this->getBaseTemplate(),
            'user' => $this->getUser()
        ];
    }

    public function getHeader(): array
    {
        $header = [];

        if ($this->getInterval()) {
            $header[] =
                [
                    $this->translator->trans("table.date") . ":",
                    $this->getDateFrom()->format('Y-m-d'),
                    ' - ',
                    $this->getDateTo()->format('Y-m-d'),
                ];
        }

        return $header;
    }

    public function getDateFrom(): ?DateTime
    {
        return $this->getInterval()?->getDateRange($this->getTimezone())?->getDateFrom();
    }

    public function getDateTo(): ?DateTime
    {
        return $this->getInterval()?->getDateRange($this->getTimezone())?->getDateTo();
    }

    public function getInterval(): ?ReportInterval
    {
        return $this->interval;
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    protected function trans($nameOrg)
    {
        $lang = $this->getUser()?->getLocale() ?? 'pl';
        /** @var Translator $translator */
        $translator = $this->translator;
        $translator->setLocale($lang);
        return $translator->trans($nameOrg);
    }

    protected function getTimezone(): DateTimeZone
    {
        return $this->appConfigService->getTimezone();
    }

    public function download(FileExtention $fileExtention): ?string
    {
        return null;
    }


    protected static function saveStreamToFile(FileExtention $fileType, $inputStream): string
    {
        $filePath = self::getFilePath($fileType->value);
        // Otwórz strumień do zapisu do pliku
        $outputStream = fopen($filePath, 'wb');
        if ($outputStream === false) {
            throw new \RuntimeException("Nie można otworzyć pliku do zapisu: {$filePath}");
        }

        try {
            // Skopiuj dane ze strumienia wejściowego do pliku
            stream_copy_to_stream($inputStream, $outputStream);
        } finally {
            // Zamknij oba strumienie
            fclose($outputStream);
        }

        return $filePath;
    }
    private static function getFilePath(string $fileType): string
    {
        $dir = sys_get_temp_dir() . DIRECTORY_SEPARATOR . 'bl-reports';
        if (!file_exists($dir)) {
            mkdir($dir);
            chmod($dir, 0777);
        }
        return $dir . DIRECTORY_SEPARATOR . uniqid() . ".$fileType";
    }

    public function getPdfOptions(): array
    {
        return [];
        //return [
        //    'orientation' => 'Landscape',
        //    'page-size' => 'A4',
        //    'margin-top' => '10mm',
        //    'margin-bottom' => '10mm',
        //    'margin-left' => '10mm',
        //    'margin-right' => '10mm'
        //];
    }
}
