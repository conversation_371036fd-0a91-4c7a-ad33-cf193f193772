<?php

namespace App\Service\Reports\Data;

use App\Entity\Invoices;
use App\Repository\InvoicesRepository;
use App\Service\AppConfig\AppConfigService;
use App\Service\Reports\Model\Data;
use DateTime;
use I2m\Reports\Model\Column;
use I2m\Reports\Model\Table;
use Symfony\Contracts\Translation\TranslatorInterface;

class InvoicesListReport extends ReportDataAbstract
{
    public function __construct(
        TranslatorInterface $translator,
        private InvoicesRepository $invoicesRepository,
        AppConfigService $appConfigService,
    ) {
        parent::__construct($translator, $appConfigService);
    }

    public function getTitle(): string
    {
        return $this->trans('menu.invoices');
    }

    public function getEmailText(): ?string
    {
        return $this->getTitle();
    }

    public function getData(?int $page = null, ?int $perPage = null): Data
    {
        $params = $this->getCriteria();
        $page2 = $page ?? 1;
        $params['perPage'] = $perPage ?? 20;

        $tz = $this->getTimezone();

        $data = [];
        do {
            $params['page'] = $page2;

            $invoices = $this->invoicesRepository->getList(
                user: $this->getUser(),
                from: $this->getDateFrom(),
                to: $this->getDateTo(),
                page: $params['page'],
                perPage: $params['perPage'],
            );

            foreach ($invoices['items'] as $invoice) {
                /** @var Invoices $invoice */
                $data [] = [
                    'id' => $invoice->getId(),
                    'uri' => $invoice->getDocument() ? $invoice->getDocument()['uri'] : null,
                    'number' => $invoice->getNumber(),
                    'issuance_date' => (new DateTime($invoice->getInvoiceDate()->format(DateTime::ATOM)))
                        ->setTimezone($tz)->format('Y-m-d H:i:s'),
                    'payment_date' => (new DateTime($invoice->getPaymentDate()->format(DateTime::ATOM)))
                        ->setTimezone($tz)->format('Y-m-d H:i:s'),
                    'price_excluded_tax' => $invoice->getTotalNet(),
                    'price' => $invoice->getTotalGross(),
                    'tax_amount' => $invoice->getTotalTax(),
                    'currency_symbol' => $invoice->getCurrency()->symbol(),
                    'currency_code' => $invoice->getCurrency()->value,
                ];
            }
            $page2++;
        } while (!empty($invoices['items']) && is_null($page)); // jesli nie podajemy strony wtedy pobieramy wszystkie

        return new Data(
            data: $data,
            total: $invoices['totalItems'],
            filters: [
                'statues' => $invoices['statuses'],
            ]
        );
    }

    public function getTables(): array
    {
        $data = $this->getData();

        $columns = [
            new Column(
                key: 'number',
                name: $this->trans("invoice.number"),
                unit: null,
                class: "align-left"
            ),
            new Column(
                key: 'issuance_date',
                name: $this->trans("invoices.issuance-date"),
                unit: null,
                class: "align-left"
            ),
            new Column(
                key: 'payment_date',
                name: $this->trans("invoices.payment-date"),
                unit: null,
                class: "align-left"
            ),
            new Column(
                key: 'price_excluded_tax',
                name: $this->trans("invoice.nett"),
                unit: null,
                class: "align-right"
            ),
            new Column(
                key: 'tax_amount',
                name: $this->trans("invoices.vat"),
                unit: null,
                class: "align-right",
                numberFormat: 2,
            ),
            new Column(
                key: 'price',
                name: $this->trans("payment_value"),
                unit: null,
                class: "align-right",
                numberFormat: 2,
            ),
            new Column(
                key: 'currency_symbol',
                name: $this->trans("invoices.currency"),
                unit: null,
                class: "align-right"
            ),
        ];

        $tables = [new Table(
            name: '',
            columns: $columns,
            items: $data->getData(),
        )];

        return $tables;
    }
}
