<?php

namespace App\Service\Reports\Data;

use App\Entity\Enum\MobilePaymentStatus;
use App\Entity\MobilePayment;
use App\Repository\MobilePaymentRepository;
use App\Service\AppConfig\AppConfigService;
use App\Service\Reports\Model\Data;
use DateTime;
use I2m\Reports\Model\Column;
use I2m\Reports\Model\Table;
use Symfony\Contracts\Translation\TranslatorInterface;

class TransactionsListReport extends ReportDataAbstract
{
    public function __construct(
        TranslatorInterface $translator,
        private MobilePaymentRepository $paymentRepository,
        AppConfigService $appConfigService,
    ) {
        parent::__construct($translator, $appConfigService);
    }

    public function getTitle(): string
    {
        return $this->trans('menu.transactions');
    }

    public function getEmailText(): ?string
    {
        return $this->getTitle();
    }

    public function getData(?int $page = null, ?int $perPage = null): Data
    {
        $params = $this->getCriteria();
        $page2 = $page ?? 1;
        $params['perPage'] = $perPage ?? 20;

        $issuer = array_key_exists('issuer', $params) ? explode(',', $params['issuer']) : null;
        $tz = $this->getTimezone();

        $user = $this->getUser();
        $data = [];
        $total = 0;
        $issuers = null;
        $params['showTotal'] = true;
        do {
            $params['page'] = $page2;

            $mobilePayments = $this->paymentRepository->findPaymentsByDateRangeAndUser(
                user: $user,
                startDate: $this->getDateFrom(),
                endDate: $this->getDateTo(),
                statusArray: [MobilePaymentStatus::CONFIRMED],
                issuer: $issuer,
                page: $params['page'],
                perPage: $params['perPage'],
                showTotal: $params['showTotal'],
            );

            if ($mobilePayments['totalItems'] !== null) {
                $total = $mobilePayments['totalItems'];
                $issuers = $mobilePayments['issuers'];
                $params['showTotal'] = false;
            }

            foreach ($mobilePayments['items'] as $mp) {
                /** @var MobilePayment $mp */
                $data [] = [
                    'id' => $mp->getId(),
                    'value' => $mp->getValue(),
                    'company_email' => $mp->getBkfpayCompany()?->getEmail(),
                    'user_email' => $mp->getBkfpayUser()?->getEmail(),
                    'carwash_long_name' => $mp->getCarwash()?->getLongName(),
                    'stand_code' => $mp->getStandCode(),
                    'document_id' => $mp->getDocument() ? $mp->getDocument()['id'] : null,
                    'document_type' => $mp->getDocument() ? $mp->getDocument()['type'] : null,
                    'document_uri' => $mp->getDocument() ? $mp->getDocument()['uri'] : null,
                    'document_number' => $mp->getDocument() ? $mp->getDocument()['number'] : null,
                    'external_payment_id' => $mp->getExternalPayment()?->getId(),
                    'external_payment_issuer' => $mp->getExternalPayment()?->getIssuer(),
                    'external_payment_value' => $mp->getExternalPayment()?->getValue(),
                    'external_payment_currency_code' => $mp->getExternalPayment()?->getCurrency()?->getCode(),
                    'external_payment_currency_symbol' => $mp->getExternalPayment()?->getCurrency()?->getSymbol(),
                    'subscription_id' => $mp->getSubscription()?->getId(),
                    'license_plate' => $mp->getLicensePlate(),
                    'confirmed_timestamp' => (new DateTime($mp->getConfirmedTimestamp()->format(DateTime::ATOM)))
                        ->setTimezone($tz)->format('Y-m-d H:i:s'),
                    'transaction_type' => $mp->getTransactionType()->value,
                ];
            }
            $page2++;
        } while (!empty($mobilePayments['items']) && is_null($page)); // jesli nie podajemy strony wtedy pobieramy wszystkie

        return new Data(
            data: $data,
            total: $total,
            filters: [
                'issuers' => $issuers,
            ]
        );
    }

    public function getTables(): array
    {
        $data = $this->getData();

        $columns = [
            new Column(
                key: 'confirmed_timestamp',
                name: $this->trans("table.date"),
                unit: null,
                class: "align-left"
            ),
            new Column(
                key: 'transaction_type',
                name: $this->trans("table.transaction_type"),
                unit: null,
                class: "align-left"
            ),
            new Column(
                key: 'user_email',
                name: $this->trans("table.user"),
                unit: null,
                class: "align-left"
            ),
            new Column(
                key: 'company_email',
                name: $this->trans("table.account"),
                unit: null,
                class: "align-left"
            ),
            new Column(
                key: 'value',
                name: $this->trans("table.transaction_value"),
                unit: null,
                class: "align-right",
                numberFormat: 2
            ),
        ];

        $tables = [new Table(
            name: '',
            columns: $columns,
            items: $data->getData(),
        )];

        return $tables;
    }
}
