<?php

namespace App\Service\Reports\Data;

use App\Entity\Enum\MobilePaymentStatus;
use App\Entity\Enum\TransactionType;
use App\Entity\Invoices;
use App\Entity\MobilePayment;
use App\Entity\User;
use App\Repository\InvoicesRepository;
use App\Repository\MobilePaymentRepository;
use App\Repository\UserRepository;
use App\Service\AppConfig\AppConfigService;
use App\Service\Reports\Model\Data;
use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use I2m\Invoices\Enum\PaymentStatus;
use I2m\Reports\Model\Column;
use I2m\Reports\Model\Table;
use Symfony\Contracts\Translation\TranslatorInterface;

class TransactionsSummaryReport extends ReportDataAbstract
{
    public function __construct(
        TranslatorInterface $translator,
        private MobilePaymentRepository $paymentRepository,
        private InvoicesRepository $invoicesRepository,
        private UserRepository $userRepository,
        private EntityManagerInterface $em,
        private AppConfigService $appConfigService,
    ) {
        parent::__construct($translator, $appConfigService);
    }

    public function getTitle(): string
    {
        return $this->trans('report_fleet_title');
    }

    public function getEmailText(): ?string
    {
        return $this->getTitle();
    }

    public function getData(?int $page = null, ?int $perPage = null): Data
    {
        return new Data(
            data: [],
            total: null,
        );
    }

    public function getTransactions(?int $page = null, ?int $perPage = null): Data
    {
        $params = $this->getCriteria();
        $page2 = $page ?? 1;
        $params['perPage'] = $perPage ?? 20;

        $issuer = array_key_exists('issuer', $params) ? explode(',', $params['issuer']) : null;
        $tz = $this->getTimezone();

        $user = $this->getUser();
        $data = [];
        $valueSum = 0;

        do {
            $params['page'] = $page2;

            $mobilePayments = $this->paymentRepository->findPaymentsByDateRangeAndUser(
                user: $user,
                startDate: $this->getDateFrom(),
                endDate: $this->getDateTo(),
                statusArray: [MobilePaymentStatus::CONFIRMED],
                issuer: $issuer,
                type: [TransactionType::PAYMENT],
                page: $params['page'],
                perPage: $params['perPage'],
                showTotal: false,
            );

            foreach ($mobilePayments['items'] as $mp) {
                /** @var MobilePayment $mp */
                $data [] = [
                    'value' => $mp->getValue(),
                    'user_email' => $mp->getBkfpayUser()?->getEmail(),
                    'carwash_long_name' => $mp->getCarwash()?->getLongName(),
                    'stand_code' => $mp->getStandCode(),
                    'license_plate' => $mp->getLicensePlate(),
                    'confirmed_timestamp' => (new DateTime($mp->getConfirmedTimestamp()->format(DateTime::ATOM)))
                        ->setTimezone($tz)->format('Y-m-d H:i:s'),
                    'currency_symbol' => $this->appConfigService->getCurrency()->symbol(),
                ];
                $valueSum += $mp->getValue();
            }
            $page2++;
        } while (!empty($mobilePayments['items']) && is_null($page));
        // jesli nie podajemy strony wtedy pobieramy wszystkie

        return new Data(
            data: $data,
            totalSum: [
                'confirmed_timestamp' => $this->trans("report_fleet_total"),
                'user_email' => null,
                'carwash_long_name' => null,
                'stand_code' => null,
                'license_plate' => null,
                'currency_symbol' => $this->appConfigService->getCurrency()->symbol(),
                'value' => $valueSum,
            ]
        );
    }

    public function getTransactionsColumns(): array
    {
        return [
            new Column(
                key: 'confirmed_timestamp',
                name: $this->trans("table.date"),
                unit: null,
                class: "align-left"
            ),
            new Column(
                key: 'user_email',
                name: $this->trans("table.user"),
                unit: null,
                class: "align-left"
            ),
            new Column(
                key: 'license_plate',
                name: $this->trans("license_plate"),
                unit: null,
                class: "align-left"
            ),
            new Column(
                key: 'value',
                name: $this->trans("table.transaction_value"),
                unit: null,
                class: "align-right",
                numberFormat: 2
            ),
            new Column(
                key: 'currency_symbol',
                name: $this->trans("invoices.currency"),
                unit: null,
                class: "align-right"
            ),
        ];
    }

    public function getInvoices(?int $page = null, ?int $perPage = null): Data
    {
        $params = $this->getCriteria();
        $page2 = $page ?? 1;
        $params['perPage'] = $perPage ?? 20;

        $tz = $this->getTimezone();

        $data = [];
        do {
            $params['page'] = $page2;

            $invoices = $this->invoicesRepository->getList(
                user: $this->getUser(),
                from: $this->getDateFrom(),
                to: $this->getDateTo(),
                page: $params['page'],
                perPage: $params['perPage'],
                statuses: [PaymentStatus::Pending]
            );

            foreach ($invoices['items'] as $invoice) {
                /** @var Invoices $invoice */
                $data [] = [
                    'id' => $invoice->getId(),
                    'number' => $invoice->getNumber(),
                    'issuance_date' => (new DateTime($invoice->getInvoiceDate()->format(DateTime::ATOM)))
                        ->setTimezone($tz)->format('Y-m-d H:i:s'),
                    'payment_date' => (new DateTime($invoice->getPaymentDate()->format(DateTime::ATOM)))
                        ->setTimezone($tz)->format('Y-m-d H:i:s'),
                    'price_excluded_tax' => $invoice->getTotalNet(),
                    'price' => $invoice->getTotalGross(),
                    'tax_amount' => $invoice->getTotalTax(),
                    'currency_symbol' => $invoice->getCurrency()->symbol(),
                    'currency_code' => $invoice->getCurrency()->value,
                ];
            }
            $page2++;
        } while (!empty($invoices['items']) && is_null($page));
        // jesli nie podajemy strony wtedy pobieramy wszystkie

        return new Data(
            data: $data,
            total: $invoices['totalItems'],
            filters: [
                'statues' => $invoices['statuses'],
            ],
        );
    }

    public function parseUserStats(array $stat, User $user): array
    {
        return [
            'email' => $user->getEmail(),
            'value' => $stat['value'],
            'count' => $stat['count'],
            'avg' => $stat['avg'],
            'issuer' => $this->trans('report_fleet_issuer_' . $stat['issuer']->value),
            'currency_symbol' => $this->appConfigService->getCurrency()->symbol(),
        ];
    }

    public function getUserStats(?int $page = null, ?int $perPage = null): Data
    {
        $params = $this->getCriteria();
        $page2 = $page ?? 1;
        $params['perPage'] = $perPage ?? 20;
        $valueSum = 0;
        $tz = $this->getTimezone();
        $data = [];
        // add manager stats
        $managerStats = $this->paymentRepository->getUserStats(
            $this->getUser(),
            $this->getDateFrom(),
            $this->getDateTo(),
            transactionType: TransactionType::PAYMENT,
        );

        foreach ($managerStats as $stat) {
            $data[] = $this->parseUserStats($stat, $this->getUser());
            $valueSum += $stat['value'];
        }

        do {
            $params['page'] = $page2;

            $users = $this->userRepository->getList(
                search: null,
                manager: $this->getUser(),
                page: $params['page'],
                perPage: $params['perPage'],
            );

            foreach ($users['items'] as $user) {
                /** @var User $user */
                $stats = $this->paymentRepository->getUserStats(
                    $user,
                    $this->getDateFrom(),
                    $this->getDateTo(),
                    transactionType: TransactionType::PAYMENT,
                );

                foreach ($stats as $stat) {
                    $data[] = $this->parseUserStats(
                        $stat,
                        $user,
                    );
                    $valueSum += $stat['value'];
                }
            }
            $page2++;
        } while (!empty($users['items']) && is_null($page));
        // jesli nie podajemy strony wtedy pobieramy wszystkie

        return new Data(
            data: $data,
            totalSum: [
                'email' => null,
                'count' => null,
                'avg' => null,
                'issuer' => $this->trans("report_fleet_total"),
                'value' => $valueSum,
                'currency_symbol' => $this->appConfigService->getCurrency()->symbol(),
            ],
        );
    }

    public function getUserStatsColumns(): array
    {
        return [
            new Column(
                key: 'issuer',
                name: $this->trans("table.account_type"),
                unit: null,
                class: "align-left"
            ),
            new Column(
                key: 'email',
                name: $this->trans("table.user"),
                unit: null,
                class: "align-left"
            ),
            new Column(
                key: 'value',
                name: $this->trans("report_fleet_value"),
                unit: null,
                class: "align-right",
            ),
            new Column(
                key: 'currency_symbol',
                name: $this->trans("invoices.currency"),
                unit: null,
                class: "align-right"
            ),
        ];
    }

    public function getInvoicesColumns(): array
    {
        return [
            new Column(
                key: 'number',
                name: $this->trans("invoice.number"),
                unit: null,
                class: "align-left"
            ),
            new Column(
                key: 'issuance_date',
                name: $this->trans("invoices.issuance-date"),
                unit: null,
                class: "align-left"
            ),
            new Column(
                key: 'payment_date',
                name: $this->trans("invoices.payment-date"),
                unit: null,
                class: "align-left"
            ),
            new Column(
                key: 'price_excluded_tax',
                name: $this->trans("invoice.nett"),
                unit: null,
                class: "align-right"
            ),
            new Column(
                key: 'tax_amount',
                name: $this->trans("invoices.vat"),
                unit: null,
                class: "align-right",
                numberFormat: 2,
            ),
            new Column(
                key: 'price',
                name: $this->trans("payment_value"),
                unit: null,
                class: "align-right",
                numberFormat: 2,
            ),
            new Column(
                key: 'currency_symbol',
                name: $this->trans("invoices.currency"),
                unit: null,
                class: "align-right"
            ),
        ];
    }

    public function getLicensePlatesStats(?int $page = null, ?int $perPage = null): Data
    {
        $data = [];
        $valueSum = 0;

        $stats = $this->paymentRepository->getLicensePlateStats(
            $this->getUser(),
            $this->getDateFrom(),
            $this->getDateTo(),
        );

        foreach ($stats as $stat) {
            $data [] = [
                'license_plate' => $stat['license_plate'],
                'value' => $stat['value'],
                'currency_symbol' => $this->appConfigService->getCurrency()->symbol(),
            ];
            $valueSum += $stat['value'];
        }

        return new Data(
            data: $data,
            totalSum: [
                'license_plate' => $this->trans("report_fleet_total"),
                'value' => $valueSum,
                'currency_symbol' => $this->appConfigService->getCurrency()->symbol(),
            ],
        );
    }

    public function getLicensePlatesColumns(): array
    {
        return [
            new Column(
                key: 'license_plate',
                name: $this->trans("license_plate"),
                unit: null,
                class: "align-left"
            ),
            new Column(
                key: 'value',
                name: $this->trans("report_fleet_value"),
                unit: null,
                class: "align-right",
            ),
            new Column(
                key: 'currency_symbol',
                name: $this->trans("invoices.currency"),
                unit: null,
                class: "align-right"
            ),
        ];
    }

    public function getTopups(?int $page = null, ?int $perPage = null): Data
    {
        $params = $this->getCriteria();
        $page2 = $page ?? 1;
        $params['perPage'] = $perPage ?? 20;

        $issuer = array_key_exists('issuer', $params) ? explode(',', $params['issuer']) : null;
        $tz = $this->getTimezone();

        $user = $this->getUser();
        $data = [];
        $valueSum = 0;

        do {
            $params['page'] = $page2;

            $mobilePayments = $this->paymentRepository->findPaymentsByDateRangeAndUser(
                user: $user,
                startDate: $this->getDateFrom(),
                endDate: $this->getDateTo(),
                statusArray: [MobilePaymentStatus::CONFIRMED],
                issuer: $issuer,
                type: [TransactionType::TOP_UP, TransactionType::TOP_UP_BONUS],
                page: $params['page'],
                perPage: $params['perPage'],
                showTotal: false,
            );

            foreach ($mobilePayments['items'] as $mp) {
                /** @var MobilePayment $mp */
                $data [] = [
                    'value' => $mp->getValue(),
                    'user_email' => $mp->getBkfpayUser()?->getEmail(),
                    'confirmed_timestamp' => (new DateTime($mp->getConfirmedTimestamp()->format(DateTime::ATOM)))
                        ->setTimezone($tz)->format('Y-m-d H:i:s'),
                    'transaction_type' =>  $this->trans(
                        'report_fleet_transaction_type_' . $mp->getTransactionType()->value
                    ),
                    'document_type' => $mp->getDocument() ?  $this->trans($mp->getDocument()['type']) : null,
                    'document_number' => $mp->getDocument() ? $mp->getDocument()['number'] : null,
                    'currency_symbol' => $this->appConfigService->getCurrency()->symbol(),
                ];
                $valueSum += $mp->getValue();
            }
            $page2++;
        } while (!empty($mobilePayments['items']) && is_null($page));
        // jesli nie podajemy strony wtedy pobieramy wszystkie

        return new Data(
            data: $data,
            totalSum: [
                'confirmed_timestamp' => $this->trans("report_fleet_total"),
                'value' => $valueSum,
                'user_email' => null,
                'transaction_type' => null,
                'document_type' => null,
                'document_number' => null,
                'currency_symbol' => $this->appConfigService->getCurrency()->symbol(),
            ]
        );
    }

    public function getTopupsColumns(): array
    {
        return [
            new Column(
                key: 'confirmed_timestamp',
                name: $this->trans("table.date"),
                unit: null,
                class: "align-left"
            ),
            new Column(
                key: 'user_email',
                name: $this->trans("table.user"),
                unit: null,
                class: "align-left"
            ),
            new Column(
                key: 'transaction_type',
                name: $this->trans("table.transaction_type"),
                unit: null,
                class: "align-left"
            ),
            new Column(
                key: 'document_type',
                name: $this->trans("report_fleet_document_type"),
                unit: null,
                class: "align-left",
            ),
            new Column(
                key: 'document_number',
                name: $this->trans("report_fleet_document_number"),
                unit: null,
                class: "align-left",
            ),
            new Column(
                key: 'value',
                name: $this->trans("table.transaction_value"),
                unit: null,
                class: "align-right",
            ),
            new Column(
                key: 'currency_symbol',
                name: $this->trans("invoices.currency"),
                unit: null,
                class: "align-right",
            ),
        ];
    }

    public function getTables(): array
    {
        // show deleted users
        if ($this->em->getFilters()->isEnabled('status_filter')) {
            $this->em->getFilters()->disable('status_filter');
        }

        $userStats = $this->getUserStats();
        $tables = [
            new Table(
                name: $this->trans('reportFleet_userStats'),
                columns: $this->getUserStatsColumns(),
                items: $userStats->getData(),
                summary: $userStats->getTotalSum(),
            ),
        ];

        $licensePlate = $this->getLicensePlatesStats();
        if (
            count($licensePlate->getData()) > 1
            || (count($licensePlate->getData()) === 1 && $licensePlate->getData()[0]['license_plate'] !== null)
        ) {
            $tables[] = new Table(
                name: $this->trans('report_fleet_licensePlates_list'),
                columns: $this->getLicensePlatesColumns(),
                items: $licensePlate->getData(),
                summary: $licensePlate->getTotalSum(),
            );
        }

        $topups = $this->getTopups();
        if (!empty($topups->getData())) {
            $tables[] = new Table(
                name: $this->trans('report_fleet_topups'),
                columns: $this->getTopupsColumns(),
                items: $topups->getData(),
                summary: $this->getTopups()->getTotalSum(),
            );
        }

        $transactions = $this->getTransactions();
        $tables[] = new Table(
            name: $this->trans('menu.transactions'),
            columns: $this->getTransactionsColumns(),
            items: $transactions->getData(),
            summary: $transactions->getTotalSum(),
        );

        $unpaidInvoicesData = $this->getInvoices();
        if (!empty($unpaidInvoicesData->getData())) {
            $tables[] = new Table(
                name: $this->trans('report_fleet_invoices_not_paid'),
                columns: $this->getInvoicesColumns(),
                items: $unpaidInvoicesData->getData(),
            );
        }

        return $tables;
    }
}
