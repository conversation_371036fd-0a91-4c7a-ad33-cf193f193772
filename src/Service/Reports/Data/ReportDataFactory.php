<?php

namespace App\Service\Reports\Data;

use I2m\Reports\Enum\ReportInterval;
use App\Entity\Enum\ReportType;
use App\Entity\User;

class ReportDataFactory
{
    public function __construct(
        private InvoicesListReport $invoicesReport,
        private TransactionsListReport $transactionsReport,
        private TransactionsSummaryReport $transactionsSummaryReport,
        private FranchiseTransactionsReport $franchiseTransactionsReport,
        private SelfInvoiceListReport $selfInvoiceListReport,
    ) {
    }

    public function getReport(
        ReportType $reportName,
        array $criteria,
        ?User $user = null,
        ?ReportInterval $interval = null,
    ): ?ReportDataAbstract {
        return $this
            ->getReportByName($reportName)
                ?->setConfig(
                    $criteria,
                    $user,
                    $interval
                );
    }
    public function getReportByName(ReportType $type): ?ReportDataAbstract
    {
        return match ($type) {
            ReportType::INVOICES_REPORT => $this->invoicesReport,
            ReportType::TRANSACTIONS_REPORT => $this->transactionsReport,
            ReportType::TRANSACTIONS_SUMMARY_REPORT => $this->transactionsSummaryReport,
            ReportType::FRANCHISE_TRANSACTIONS_REPORT => $this->franchiseTransactionsReport,
            ReportType::SELF_INVOICE_REPORT => $this->selfInvoiceListReport,
        };
    }
}
