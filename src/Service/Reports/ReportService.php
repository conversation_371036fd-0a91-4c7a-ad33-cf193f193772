<?php

namespace App\Service\Reports;

use I2m\Reports\Enum\ReportInterval;
use App\Entity\Enum\ReportType;
use App\Entity\User;
use App\Service\Reports\Model\Data;
use App\Service\Reports\Data\ReportDataFactory;

class ReportService
{
    public function __construct(
        private ReportDataFactory $reportDataFactory,
    ) {
    }

    public function getData(
        ReportType $reportType,
        array $criteria,
        ?User $user,
        ?int $page = null,
        ?int $perPage = null,
        ?ReportInterval $interval = null,
    ): Data {
        return $this
            ->reportDataFactory
            ->getReport($reportType, $criteria, $user, $interval)
            ->getData($page, $perPage)
            ;
    }
}
