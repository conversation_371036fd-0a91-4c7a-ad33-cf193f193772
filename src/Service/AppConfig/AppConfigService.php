<?php

namespace App\Service\AppConfig;

use App\Entity\Client;
use App\Entity\Enum\Languages;
use App\Entity\Owners;
use App\Entity\VatTax;
use App\Repository\ClientRepository;
use App\Repository\CurrencyRepository;
use App\Repository\OwnerRepository;
use I2m\Invoices\Enum\InvoiceGeneratorType;
use I2m\StandardTypes\Enum\Currency;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;

class AppConfigService
{
    public function __construct(
        private ParameterBagInterface $parameterBag,
        private CurrencyRepository $currencyRepository,
        private OwnerRepository $ownerRepository,
        private ClientRepository $clientRepository
    ) {
    }

    public function getCurrency(): Currency
    {
        return Currency::from($this->parameterBag->get('branding_currency_code'));
    }

    /** @deprecated  */
    public function getCurrency2(): \App\Entity\Currency
    {
        return $this->currencyRepository
            ->findOneBy(['code' => $this->parameterBag->get('branding_currency_code')]);
    }

    public function getVatTax(): VatTax
    {
        return (new VatTax())
            ->setTaxValue((float) $this->parameterBag->get('invoice_issuer_vat'))
            ->setTaxKey($this->parameterBag->get('invoice_issuer_vat'))
            ;
    }

    public function getTimezone(): \DateTimeZone
    {
        return new \DateTimeZone($this->parameterBag->get('branding_timezone'));
    }

    public function getAppName(): string
    {
        return $this->parameterBag->get('app_name');
    }

    public function getBackendUrl(): string
    {
        return $this->parameterBag->get('web_url');
    }

    public function getAppIssuer(): Owners
    {
        return $this->ownerRepository->find($this->parameterBag->get('invoice_issuer_owner_id'));
    }

    public function getFranchiseClient(): Client
    {
        return $this->clientRepository->find(1);
    }

    public function getLanguage(): Languages
    {
        return Languages::from($this->parameterBag->get('default_internalization.locale'));
    }
}
