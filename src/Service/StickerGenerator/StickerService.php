<?php

namespace App\Service\StickerGenerator;

use App\Service\AppConfig\AppConfigService;
use I2m\IIot\Tools\Ean8Helper;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\Filesystem\Filesystem;

class StickerService
{
    public function __construct(
        private StickerGenerator $stickerGenerator,
        private ParameterBagInterface $parameterBag,
        private AppConfigService $appConfigService
    ) {
    }
    public function genrateSeries(
        int $start,
        int $length
    ) {
        $end = $start + $length - 1;

        $filesystem = new Filesystem();
        $exportPath = "strickers/{$start}_{$end}";
        $filesystem->mkdir($exportPath);


        for ($i = $start; $i <= $end; $i++) {
            $code = $i . Ean8Helper::checkSumEAN8((string)$i);
            $file = $this->generateSingle($code);
            $filesystem->copy($file, $exportPath . DIRECTORY_SEPARATOR . $code . ".pdf");
        }
    }


    public function generateSingle(string $code)
    {
        $pdfPrintBorderFilePath = $this->getTemplatePath();
        $host = $this->appConfigService->getBackendUrl();
        $locale = $this->appConfigService->getLanguage()->value;
        $qrCodeUrl = "$host/stand/";

        return $this->stickerGenerator->generatePdf($code, $pdfPrintBorderFilePath, $qrCodeUrl, $locale);
    }
    private function getTemplatePath()
    {
        return
            $this->parameterBag->get('kernel.project_dir') .
            DIRECTORY_SEPARATOR . "public" .
            DIRECTORY_SEPARATOR . $this->parameterBag->get('brandingAssets') .
            DIRECTORY_SEPARATOR . "sticker" .
            DIRECTORY_SEPARATOR . "padding_no_cutting_edge.pdf"

            ;
    }
}
