<?php

namespace App\Service\StickerGenerator;

use chillerlan\QRCode\QRCode;
use chillerlan\QRCode\QROptions;
use I2m\IIot\Tools\Ean8Helper;
use Knp\Snappy\Pdf;
use Symfony\Component\Filesystem\Filesystem;
use Symfony\Contracts\Translation\TranslatorInterface;
use Twig\Environment;

class StickerGenerator
{
    public function __construct(
        private readonly Environment $templating,
        private readonly TranslatorInterface $translator,
        private readonly Pdf $pdfPrinter,
    ) {
    }

    /**
     * raturns qr image in base64
     */
    private function generateBase64Image(string $qrCodeUrl, string $standCode): string
    {

        $options = new QROptions([
            'version'    => 4,
            'outputType' => QRCode::OUTPUT_MARKUP_SVG,
            'eccLevel'   => QRCode::ECC_M,
            'imageBase64' => false,
        ]);

        $qrcode = new QRCode($options);

        return $qrcode->render($qrCodeUrl . $standCode);
    }

    /**
     * @param string $ena8StandCode
     * throws \Exception
     */
    private function validateStandCode(string $ena8StandCode)
    {
        $ean8withoutValidationCode = $this->getStandCode($ena8StandCode);

        $calculatedEan = Ean8Helper::checkSumEAN8($ean8withoutValidationCode);

        if ($ena8StandCode != ($ean8withoutValidationCode . $calculatedEan)) {
            throw new \Exception('Invalid stand code. Check sum not correct');
        }
    }

    private function getStandCode(string $ena8StandCode): string
    {
        return substr($ena8StandCode, 0, 7);
    }

    public function generatePdf(
        string $standCode,
        string $pdfPrintBorderFilePath,
        string $qrCodeUrl,
        string $locale,
    ) {
        $this->validateStandCode($standCode);

        $qrImage = $this->generateBase64Image($qrCodeUrl, $standCode);

        $html = $this->templating->render(
            'sticker/sticker.pdf.html.twig',
            [
                'carwash_stand_code' => $standCode,
                'label' => $this->translator->trans('stand-code', locale: $locale),
                'qr_code_image' => $qrImage,
            ]
        );
        $tmpPdfPath = $this->getTmpPdfPath();


        $this->pdfPrinter->generateFromHtml(
            $html,
            $tmpPdfPath,
            [
                'page-width' => '8.4cm',
                'page-height' => '11.4cm',
                'margin-bottom' => 0,
                'margin-left' => '1.8cm',
                'margin-right' => 0,
                'margin-top' => '2cm',
            ]
        );

        $newPdfFile = $this->getTmpPdfPath();
        $newPdfFileVectorized = $this->getTempDir() . DIRECTORY_SEPARATOR . $standCode . ".pdf";



        if (file_exists($tmpPdfPath)) {
            $output = [];
            $return_var = null;


            // merge pdf's
            $command = 'pdftk ' . $tmpPdfPath . ' background ' . $pdfPrintBorderFilePath . ' output ' . $newPdfFile;
            exec(
                $command,
                $output,
                $return_var
            );

            if ($return_var > 0) {
                throw new \Exception(
                    "Pdftk command error $command. Incorect return status: $return_var\n" .
                    "output: " . json_encode($output),
                    $return_var
                );
            }

            // font to vector
            $command = 'gs -dNOPAUSE -q -sDEVICE=pdfwrite -sColorConversionStrategy=CMYK '
                . '-ddProcessColorModel=/DeviceCMYK -dNoOutputFonts -sOutputFile='
                . $newPdfFileVectorized . ' -dBATCH ' . $newPdfFile;
            exec(
                $command,
                $output,
                $return_var
            );

            if ($return_var > 0) {
                throw new \Exception("Ghostscript command error $command. Incorect return status: " . $return_var, $return_var);
            }
        }

        return file_exists($newPdfFileVectorized) ? $newPdfFileVectorized : null;
    }


    private function getTmpPdfPath()
    {
        $dir = $this->getTempDir();
        return $dir . DIRECTORY_SEPARATOR .  uniqid() . '.pdf';
    }


    public function getTempDir(): string
    {
        $dir = sys_get_temp_dir() . DIRECTORY_SEPARATOR . "stickers";
        if (!file_exists($dir)) {
            mkdir(directory: $dir, recursive: true);
        }
        return $dir;
    }
}
