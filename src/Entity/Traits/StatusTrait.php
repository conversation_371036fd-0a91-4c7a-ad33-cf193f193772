<?php

namespace App\Entity\Traits;

use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Annotation as Serializer;

trait StatusTrait
{
    #[ORM\Column(type: 'string', length: 1, nullable: true, options: ['default' => 'a'])]
    #[Serializer\Groups(['user_fleet_data'])]
    private string $status = 'a';

    public function getStatus(): string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }
}
