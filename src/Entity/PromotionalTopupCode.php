<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;
use App\Repository\PromotionalTopupCodeRepository;
use Symfony\Component\Serializer\Annotation as Serializer;

/**
 * MobilePayment
 *
 *
 */
#[ORM\Table(name: 'bkfpay_promotional_topups_codes')]
#[ORM\Entity(repositoryClass: PromotionalTopupCodeRepository::class)]
class PromotionalTopupCode
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    #[Serializer\Groups(['payments:list'])]
    private int $id;

    #[ORM\Column(type: 'string', length: 19, nullable: false)]
    #[Serializer\Groups(['payments:list'])]
    private string $code;

    #[ORM\JoinColumn(name: 'bkfpay_user_id', referencedColumnName: 'id', nullable: true)]
    #[ORM\ManyToOne(targetEntity: User::class)]
    private ?User $bkfpayUser = null;

    #[ORM\Column(type: 'float')]
    #[Serializer\Groups(['payments:list'])]
    private float $value;

    #[ORM\JoinColumn(name: 'group_id', referencedColumnName: 'id', nullable: true)]
    #[ORM\ManyToOne(targetEntity: PromotionalCodesGroup::class)]
    #[Serializer\Groups(['payments:list'])]
    private ?PromotionalCodesGroup $group;

    #[ORM\OneToOne(targetEntity: \App\Entity\MobilePayment::class, inversedBy: 'topupCode')]
    private ?MobilePayment $mobilePayment;

    public function getId(): int
    {
        return $this->id;
    }

    public function getCode(): string
    {
        return $this->code;
    }

    public function setCode(string $code): PromotionalTopupCode
    {
        $this->code = $code;
        return $this;
    }

    public function getBkfpayUser(): ?User
    {
        return $this->bkfpayUser;
    }

    public function setBkfpayUser(?User $bkfpayUser): PromotionalTopupCode
    {
        $this->bkfpayUser = $bkfpayUser;
        return $this;
    }

    public function getValue(): float
    {
        return $this->value;
    }

    public function setValue(float $value): PromotionalTopupCode
    {
        $this->value = $value;
        return $this;
    }

    public function getMobilePayment(): ?MobilePayment
    {
        return $this->mobilePayment;
    }

    public function setMobilePayment(?MobilePayment $mobilePayment): PromotionalTopupCode
    {
        $this->mobilePayment = $mobilePayment;
        return $this;
    }

    /**
     * @return PromotionalCodesGroup
     */
    public function getGroup(): ?PromotionalCodesGroup
    {
        return $this->group;
    }


    public function setGroup(?PromotionalCodesGroup $group): PromotionalTopupCode
    {
        $this->group = $group;
        return $this;
    }

    public function getNumber(): string
    {
        return $this->getGroup()->getName() . ' / ' . $this->getCode();
    }
    public function getDocument(): array
    {
        return [
            'number' => $this->getNumber(),
            'type' => "code",
            'id' => $this->getId(),
            'uri' => null,
        ];
    }
}
