<?php

namespace App\Entity;

use App\Entity\Enum\InvoiceSource;
use App\Repository\InvoicesRepository;
use DateTime;
use DateTimeImmutable;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use I2m\Invoices\Enum\InvoiceKindType;
use I2m\Invoices\Enum\PaymentStatus;
use I2m\Invoices\Enum\PaymentType;
use I2m\Invoices\Interface\IInvoice;
use I2m\Invoices\Interface\IIssuer;
use I2m\Payment\Enum\Status;
use Symfony\Component\Serializer\Annotation as Serializer;
use I2m\StandardTypes\Enum\Currency;
use Symfony\Component\Serializer\Annotation\Groups;

/**
 * Invoices
 */
#[ORM\Table(name: 'invoices')]
#[ORM\Entity(repositoryClass: InvoicesRepository::class)]
#[ORM\HasLifecycleCallbacks]
class Invoices implements IInvoice
{
    public const PAYMENT_METHOD_TRANSFER = 'transfer';
    public const PAYMENT_METHOD_CREDIT_CARD = 'credit-card';
    public const PAYMENT_METHOD_P24 = 'p24';
    public const PAYMENT_METHOD_POST_PAID = 'post-paid';

    #[ORM\Column(name: 'id', type: 'integer', nullable: false)]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    #[ORM\SequenceGenerator(sequenceName: 'invoices_id_seq', allocationSize: 1, initialValue: 1)]
    #[Serializer\Groups(['invoice:list'])]
    private $id;

    #[ORM\Column(type: 'datetime')]
    private $ctime;

    #[ORM\Column(type: 'datetime', nullable: true)]
    private $mtime;

    #[ORM\Column(type: 'string', length: 255, nullable: true)]
    #[Serializer\Groups(['invoice:list'])]
    private ?string $number = null;

    #[ORM\Column(type: 'integer', nullable: true)]
    private ?int $sequentialNumber = null;


    #[ORM\Column(type: 'datetime')]
    #[Serializer\Groups(['invoice:list'])]
    private \DateTimeInterface $invoiceDate;

    #[ORM\Column(type: Types::DATETIME_IMMUTABLE, nullable: true)]
    #[Serializer\Groups(['invoice:list'])]
    private ?\DateTimeImmutable $paymentDate = null;


    #[ORM\Column(type: 'string', length: 16, nullable: true)]
    private ?string $paymentMethod = null;

    #[ORM\Column(type: 'decimal', precision: 10, scale: 2, nullable: true)]
    #[Serializer\Groups(['invoice:list'])]
    private $totalNet;

    #[ORM\Column(type: 'decimal', precision: 10, scale: 2, nullable: true)]
    #[Serializer\Groups(['invoice:list'])]
    private $totalGross;

    #[ORM\Column(type: 'decimal', precision: 10, scale: 2, nullable: true)]
    #[Serializer\Groups(['invoice:list'])]
    private $totalTax;

    #[ORM\Column(type: 'string', length: 10, nullable: true)]
    private $period;


    #[ORM\OneToMany(targetEntity: InvoicePosition::class, mappedBy: 'invoice', cascade: ['all'], orphanRemoval: true)]
    private $positions;


    #[ORM\Column(type: 'boolean', length: 1, nullable: false)]
    private $published = false;

    #[ORM\ManyToOne(targetEntity: User::class)]
    #[Serializer\Groups(['invoice:list'])]
    private ?User $user = null;

    #[ORM\ManyToOne(targetEntity: Client::class)]
    #[Serializer\Groups(['invoice:list'])]
    private ?Client $client;

    #[ORM\ManyToOne(targetEntity: Owners::class)]
    #[Serializer\Groups(['invoice:list'])]
    private ?Owners $issuer = null;

    #[ORM\Column(type: 'string', nullable: true)]
    private ?string $numberFormatted = null;
    #[Serializer\Groups(['invoice:list'])]
    #[ORM\Column(length: 8, nullable: true, enumType: Currency::class)]
    private ?Currency $currency = null;

    #[ORM\OneToOne(targetEntity: MobilePayment::class, mappedBy: 'invoice')]
    private ?MobilePayment $mobilePayment = null;

    #[ORM\OneToOne(targetEntity: ExternalPayment::class, inversedBy: 'invoice')]
    private ?ExternalPayment $externalPayment = null;

    #[ORM\Column(type: 'string', length: 20, nullable: true)]
    private ?string $externalId = null;

    #[ORM\Column(enumType: InvoiceKindType::class, length: 20, nullable: true)]
    private ?InvoiceKindType $kind = null;

    #[ORM\Column(enumType: InvoiceSource::class, length: 20, nullable: true)]
    private ?InvoiceSource $source = null;

    #[ORM\Column(type: 'string', nullable: true)]
    private ?string $cloudPath = null;

    #[ORM\Column(nullable: true)]
    private ?\DateTimeImmutable $sendDate = null;

    #[ORM\OneToOne(mappedBy:"invoice", cascade: ['persist', 'remove'])]
    private ?InvoiceFranchise $franchise = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $description = null;

    #[ORM\OneToOne(targetEntity: ReportFile::class, cascade: ['persist', 'remove'])]
    private ReportFile $attachment;


    public function getCurrency(): ?Currency
    {
        return $this->currency;
    }

    public function setCurrency(?Currency $currency): Invoices
    {
        $this->currency = $currency;
        return $this;
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function setUser(?User $user): Invoices
    {
        $this->user = $user;
        return $this;
    }

    /**
     * Invoices constructor.
     */
    public function __construct()
    {
        $this->positions = new ArrayCollection();
    }

    /**
     * Get id
     *
     * @return integer|null
     */
    public function getId(): ?int
    {
        return $this->id;
    }

    /**
     * set id
     */
    public function setId(?int $id): self
    {
        $this->id = $id;

        return $this;
    }

    /**
     * Get ctime
     *
     * @return DateTime
     */
    public function getCtime()
    {
        return $this->ctime;
    }

    /**
     * Set ctime
     *
     * @param Datetime $ctime
     * @return $this
     */
    public function setCtime($ctime)
    {
        $this->ctime = $ctime;

        return $this;
    }

    /**
     * Get mtime
     *
     * @return DateTime
     */
    public function getMtime()
    {
        return $this->mtime;
    }

    public function setMtime($mtime)
    {
        $this->mtime = $mtime;

        return $this;
    }

    public function setNumber(string $number): static
    {
        $this->number = $number;

        return $this;
    }


    /**
     * Get number
     */
    public function getNumber(): ?string
    {
        return $this->number;
    }

    /**
     * Set invoice number numerical value
     * e.g. 10 from string FS-CM/10/2017/04
     *
     * @return $this
     */
    public function setSequentialNumber(?int $number): static
    {
        $this->sequentialNumber = $number;

        return $this;
    }

    /**
     * Get invoice number numerical value
     */
    public function getSequentialNumber(): int
    {
        return $this->sequentialNumber;
    }

    public function setInvoiceDate(\DateTimeInterface $invoiceDate)
    {
        $this->invoiceDate = $invoiceDate;

        return $this;
    }

    public function getInvoiceDate(): \DateTimeInterface
    {
        return $this->invoiceDate;
    }

    public function setPaymentDate(\DateTimeImmutable $paymentDate): self
    {
        $this->paymentDate = $paymentDate;

        return $this;
    }

    public function getPaymentDate(): ?\DateTimeImmutable
    {
        return $this->paymentDate;
    }

    public function getPaymentMethod(): string
    {
        return $this->paymentMethod ?? static::PAYMENT_METHOD_TRANSFER;
    }

    public function getPaymentMethodName(): string
    {
        switch ($this->paymentMethod) {
            case static::PAYMENT_METHOD_CREDIT_CARD:
                return "ESPAGO";
            case static::PAYMENT_METHOD_P24:
                return "Przelewy24";
            default:
                return static::PAYMENT_METHOD_TRANSFER;
        }
    }

    public function setPaymentMethod(string $paymentMethod): Invoices
    {
        $this->paymentMethod = $paymentMethod;
        return $this;
    }

    public function setTotalGross($value): static
    {
        $this->totalGross = $value;

        return $this;
    }

    /**
     * Get Price
     *
     * @return float
     */
    public function getTotalGross()
    {
        return round($this->totalGross, 2);
    }

    public function getTotalNet(): float
    {
        return round($this->totalNet, 2);
    }

    public function setTotalNet(float $value): static
    {
        $this->totalNet = $value;

        return $this;
    }

    public function getTotalTax(): float
    {
        return $this->totalTax;
    }

    public function setTotalTax(float $value): static
    {
        $this->totalTax = round($value, 2);

        return $this;
    }

    /**
     * @return string
     */
    public function getPeriod()
    {
        return $this->period;
    }

    /**
     * @param string $period
     * @return $this
     */
    public function setPeriod($period)
    {
        $this->period = $period;

        return $this;
    }

    public function getPositions(): Collection
    {
        return $this->positions;
    }

    /**
     * Add InvoicePositions or
     * increment quantity for existing InvoicePositions if product name and description are the same.
     *
     * @return $this
     */
    public function addInvoicePositions(InvoicePosition $invoicePosition)
    {

            $this->positions[] = $invoicePosition;
            $invoicePosition->setInvoice($this);

        $this->calculatePrice();

        return $this;
    }

    public function calculatePrice()
    {
        $priceExcludedTax = 0;
        $taxAmount = 0;
        $priceIncludedTax = 0;
        foreach ($this->getPositions() as $invoicePosition) {
            /** @var InvoicePosition $invoicePosition */
            $priceExcludedTax += $invoicePosition->getTotalNet();
            $taxAmount += $invoicePosition->getTotalTax();
            $priceIncludedTax += $invoicePosition->getGrossPrice();
        }
        $this->setTotalNet($priceExcludedTax);
        $this->setTotalGross($priceIncludedTax);
        $this->setTotalTax($taxAmount);
    }

    public function addPosition(string $name, float $quantity, float $grossPrice, ?int $tax): static
    {

        $position = (new InvoicePosition())
            ->setName($name)
            ->setQuantity($quantity)
            ->setGrossPrice($grossPrice)
            ->setTax($tax);
        ;

        if (!$this->positions->contains($position)) {
            $this->positions->add($position);
            $position->setInvoice($this);
        }

        return $this;
    }

    public function isPublished(): bool
    {
        return $this->published;
    }

    /**
     * @return Invoices
     */
    public function setPublished(bool $published)
    {
        $this->published = $published;
        return $this;
    }

    #[ORM\PrePersist]
    #[ORM\PreUpdate]
    public function updatedTimestamps(): void
    {
        if ($this->getCtime() == null) {
            $this->setCtime(new \DateTime('now'));
        }

        $this->setMtime(new \DateTime('now'));
    }

    public function getClient(): ?Client
    {
        return $this->client;
    }

    public function setClient(?Client $client): Invoices
    {
        $this->client = $client;
        return $this;
    }

    public function getNumberFormatted(): ?string
    {
        return $this->numberFormatted;
    }

    public function setNumberFormatted(?string $numberFormatted): static
    {
        $this->numberFormatted = $numberFormatted;
        return $this;
    }

    public function getDocument(): array
    {
        return [
            'number' => $this->getNumber(),
            'type' => "invoice",
            'id' => $this->getId(),
            'uri' => "/invoice/{$this->getId()}/download"
        ];
    }

    public function getMobilePayment(): ?MobilePayment
    {
        return $this->mobilePayment;
    }

    public function setMobilePayment(?MobilePayment $mp): self
    {
        $this->mobilePayment = $mp;
        return $this;
    }

    #[Serializer\Groups(['invoice:list'])]
    public function getExternalPayment(): ?ExternalPayment
    {
        return $this->externalPayment ?? $this->getMobilePayment()?->getExternalPayment();
    }

    public function setExternalPayment(?ExternalPayment $externalPayment): Invoices
    {
        $this->externalPayment = $externalPayment;
        return $this;
    }

    public function getExternalId(): ?string
    {
        return $this->externalId;
    }

    public function setExternalId(?string $id): Invoices
    {
        $this->externalId = $id;
        return $this;
    }

    public function getCloudPath(): ?string
    {
        return $this->cloudPath;
    }
    public function setCloudPath(?string $cloudPath): static
    {
        $this->cloudPath = $cloudPath;
        return $this;
    }
    public function getIssuer(): ?Owners
    {
        return $this->issuer;
    }

    public function setIssuer(?Owners $issuer): Invoices
    {
        $this->issuer = $issuer;
        return $this;
    }

    public function getKind(): InvoiceKindType
    {
        return $this->kind ?? InvoiceKindType::vat;
    }

    public function setKind(?InvoiceKindType $kind): Invoices
    {
        $this->kind = $kind;
        return $this;
    }
    public function getLanguage(): string
    {
        return $this->getUser()?->getLanguage()->value ?? $this->getIssuer()->getLanguage()->value;
    }

    #[Serializer\Groups(['invoice:list'])]
    public function getStatus(): PaymentStatus
    {
        return match ($this->getExternalPayment()->getStatus()) {
            Status::PENDING => PaymentStatus::Pending,
            Status::CONFIRMED => PaymentStatus::Paid,
            Status::CANCELED => PaymentStatus::Cancelled,
            Status::REFUNDING => PaymentStatus::Cancelled,
            Status::REFUNDED => PaymentStatus::Cancelled,
            Status::REJECTED => PaymentStatus::Pending,
            Status::WAITING => PaymentStatus::Pending,
            Status::INITIATED => PaymentStatus::Pending,
        };
    }

    public function getPaid(): ?float
    {
        return ($this->getStatus() == PaymentStatus::Paid) ?  $this->getExternalPayment()->getValue() : null;
    }


    public function getAdditionalInfo(): ?string
    {
        return null;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): Invoices
    {
        $this->description = $description;
        return $this;
    }



    public function getPaymentTerm(): ?\DateInterval
    {
        return $this->getExternalPayment()?->getPaymentTerm() ? new \DateInterval($this->getExternalPayment()->getPaymentTerm()) : null;
    }

    public function getPaymentType(): ?PaymentType
    {
        return match ($this->getExternalPayment()?->getIssuer()) {
            ExternalPayment::ISSUER_TRANSFER => PaymentType::Transfer,
            ExternalPayment::ISSUER_CREDIT_CARD => PaymentType::CreditCard,
            default => PaymentType::Online,
        };
    }

    public function getServiceDate(): ?DateTimeImmutable
    {
        return \DateTimeImmutable::createFromInterface($this->getInvoiceDate());
        // return \DateTimeImmutable::createFromInterface($this->getMobilePayment()->getConfirmedTimestamp());
    }

    public function getSendDate(): ?DateTimeImmutable
    {
        return $this->sendDate;
    }

    public function setSendDate(?DateTimeImmutable $sendDate): Invoices
    {
        $this->sendDate = $sendDate;
        return $this;
    }

    public function getSource(): ?InvoiceSource
    {
        return $this->source;
    }

    public function setSource(?InvoiceSource $source): Invoices
    {
        $this->source = $source;
        return $this;
    }

    public function getFranchise(): ?InvoiceFranchise
    {
        return $this->franchise;
    }

    public function setFranchise(?InvoiceFranchise $franchise): Invoices
    {
        $this->franchise = $franchise;
        return $this;
    }

    public function getAttachment(): ReportFile
    {
        return $this->attachment;
    }

    public function setAttachment(ReportFile $attachment): Invoices
    {
        $this->attachment = $attachment;
        return $this;
    }

    public function setPaid(?float $paid): static
    {
        return $this;
    }

    public function getDiscount(): ?int
    {
        return null;
    }
}
