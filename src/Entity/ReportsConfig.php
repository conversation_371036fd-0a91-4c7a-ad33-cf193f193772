<?php

namespace App\Entity;

use I2m\Reports\Enum\ReportInterval;
use App\Entity\Enum\ReportType;
use App\Entity\User;
use App\Repository\ReportsConfigRepository;
use DateTime;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\Mapping as ORM;
use I2m\Reports\Enum\FileExtention;
use Symfony\Component\Serializer\Annotation\Groups;

#[ORM\Table(name: 'reports_config')]
#[ORM\Entity(repositoryClass: ReportsConfigRepository::class)]
#[ORM\HasLifecycleCallbacks]
class ReportsConfig
{
    #[ORM\Column(name: 'id', type: 'integer', nullable: false)]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    #[Groups(['report:list'])]
    protected $id;

    /**
     * @var string
     */
    #[ORM\Column(name: 'status', type: 'string', length: 1)]
    private $status = 'a';

    #[ORM\Column(name: 'type', nullable: true, enumType: ReportType::class)]
    #[Groups(['report:list'])]
    private ?ReportType $reportType = null;

    #[ORM\Column(name: 'ctime', type: 'datetime', nullable: true)]
    private \DateTimeInterface $ctime;

    #[ORM\Column(name: 'mtime', type: 'datetime', nullable: true)]
    private \DateTimeInterface $mtime;

    #[ORM\ManyToOne(targetEntity: User::class)]
    private ?User $user = null;

    #[ORM\Column(name: 'is_enabled', type: 'boolean')]
    #[Groups(['report:list'])]
    protected $isEnabled = true;

    /** @var array
     */
    #[ORM\Column(type: 'json', nullable: true)]
    protected $config = [];

    #[ORM\Column(enumType: ReportInterval::class, nullable: true, name: 'reportinterval')]
    #[Groups(['report:list'])]
    protected ?ReportInterval $reportInterval = null;

    #[ORM\Column(type: 'string', nullable: true)]
    #[Groups(['report:list'])]
    protected ?string $name;

    #[ORM\Column(nullable: true)]
    #[Groups(['report:list'])]
    private ?array $email = null;

    #[ORM\Column(name: 'ext', nullable: true, enumType: FileExtention::class)]
    #[Groups(['report:list'])]
    private ?FileExtention $ext = null;

    public function isEnabled(): bool
    {
        return $this->isEnabled;
    }

    public function setEnabled(bool $isEnabled): ReportsConfig
    {
        $this->isEnabled = $isEnabled;
        return $this;
    }

    public function getReportInterval(): ?ReportInterval
    {
        return $this->reportInterval;
    }

    public function setReportInterval(ReportInterval $reportInterval): ReportsConfig
    {
        $this->reportInterval = $reportInterval;
        return $this;
    }

    public function getStatus(): string
    {
        return $this->status;
    }

    public function setStatus(string $status): ReportsConfig
    {
        $this->status = $status;
        return $this;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getCtime(): \DateTimeInterface
    {
        return $this->ctime;
    }

    public function setCtime(DateTime $ctime): ReportsConfig
    {
        $this->ctime = $ctime;
        return $this;
    }

    public function getMtime(): \DateTimeInterface
    {
        return $this->mtime;
    }

    public function setMtime(DateTime $mtime): ReportsConfig
    {
        $this->mtime = $mtime;
        return $this;
    }

    /**
     * Delete Reports ConfigReportsGenerator2Command
     */
    public function delete(): ReportsConfig
    {
        $this->setStatus('d');

        return $this;
    }

    public function getReportType(): ?ReportType
    {
        return $this->reportType;
    }

    public function setReportType(ReportType $type)
    {
        $this->reportType = $type;
        return $this;
    }

    /**
     * @return string
     */
    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(?string $name): ReportsConfig
    {
        $this->name = $name;
        return $this;
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function setUser(?User $user): ReportsConfig
    {
        $this->user = $user;
        return $this;
    }

    public function setGroups(?array $groups): ReportsConfig
    {
        $this->config['groups'] =  $groups;
        return $this;
    }

    public function getGroups(): ?array
    {
        return $this->config['groups'] ?? null;
    }

    public function setItems(?array $items): ReportsConfig
    {
        $this->config['items'] =  $items;

        return $this;
    }
    public function getItems(): array
    {
        return
            $this->config['items'] ??
            $this->config['devices'] ?? // deprecated
            [];
    }

    public function setParameter(string $key, $value): ReportsConfig
    {
        $this->config[$key] =  $value;

        return $this;
    }
    public function getParameter(string $key)
    {
        return $this->config[$key] ?? null;
    }

    public function getConfig(): array
    {
        return $this->config;
    }



    public function setUsers(?array $users): ReportsConfig
    {
        $this->config['users'] =  $users;

        return $this;
    }

    public function getUsers(): array
    {
        return $this->config['users'] ?? [];
    }

    public function setConfig(array $config): ReportsConfig
    {
        $this->config = $config;
        return $this;
    }

    #[ORM\PrePersist]
    #[ORM\PreUpdate]
    public function updatedTimestamps()
    {
        if (!isset($this->ctime)) {
            $this->ctime = new DateTime('now');
        }
        $this->mtime = new DateTime('now');
    }

    public function getEmail(): ?array
    {
        return $this->email;
    }

    public function setEmail(?array $email): ReportsConfig
    {
        $this->email = $email;
        return $this;
    }

    public function getExt(): ?FileExtention
    {
        return $this->ext;
    }

    public function setExt(?FileExtention $ext): ReportsConfig
    {
        $this->ext = $ext;
        return $this;
    }
}
