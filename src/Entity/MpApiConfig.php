<?php

namespace App\Entity;

use App\Repository\MpApiConfigRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: MpApiConfigRepository::class)]
class MpApiConfig
{
    #[ORM\Id]
    #[ORM\OneToOne()]
    #[ORM\JoinColumn(nullable: false)]
    private User $user;

    #[ORM\Column(length: 64)]
    private ?string $apiKey = null;

    #[ORM\Column(length: 32)]
    private ?string $paySystem = null;

    public function getApiKey(): ?string
    {
        return $this->apiKey;
    }

    public function setApiKey(string $apiKey): static
    {
        $this->apiKey = $apiKey;

        return $this;
    }

    public function getPaySystem(): ?string
    {
        return $this->paySystem;
    }

    public function setPaySystem(string $paySystem): static
    {
        $this->paySystem = $paySystem;

        return $this;
    }

    public function getUser(): User
    {
        return $this->user;
    }

    public function setUser(User $user): MpApiConfig
    {
        $this->user = $user;
        return $this;
    }
}
