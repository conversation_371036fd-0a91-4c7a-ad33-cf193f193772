<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;
use I2m\StandardTypes\Enum\Country;
use I2m\StandardTypes\Interface\ICompany;
use Symfony\Component\Serializer\Annotation\Groups;
use Symfony\Component\Serializer\Attribute\SerializedName;

#[ORM\MappedSuperclass]
#[ORM\HasLifecycleCallbacks]
class Company implements ICompany
{
    #[ORM\Column(type: 'string', length: 255, nullable: true)]
    #[Groups(['company:data'])]
    private ?string $name;

    #[ORM\Column(type: 'string', length: 64, nullable: true)]
    #[Groups(['company:data'])]
    private ?string $taxNumber;

    #[Groups(['company:data'])]
    #[ORM\Column(type: 'string', length: 255, nullable: true)]
    private ?string $address;

    #[Groups(['company:data'])]
    #[ORM\Column(type: 'string', length: 12, nullable: true)]
    private ?string $postCode;


    #[Groups(['company:data'])]
    #[ORM\Column(type: 'string', length: 128, nullable: true)]
    private ?string $city;

    #[ORM\Column(enumType: Country::class, length: 8, nullable: true, name: 'country')]
    #[Groups(['company:data'])]
    #[SerializedName('country')]
    private ?Country $country;

    #[ORM\Column(type: 'string', length: 255, nullable: true)]
    #[Groups(['company:data'])]
    private ?string $email;

    public function getRegon(): ?string
    {
        return null;
    }

    public function setRegon(?string $regon): static
    {
        return $this;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(?string $name): static
    {
        $this->name = $name;
        return $this;
    }

    public function getTaxNumber(): ?string
    {
        return $this->taxNumber;
    }

    public function setTaxNumber(?string $taxNumber): static
    {
        $this->taxNumber = $taxNumber;
        return $this;
    }

    public function getAddress(): ?string
    {
        return $this->address;
    }

    public function setAddress(?string $address): static
    {
        $this->address = $address;
        return $this;
    }

    public function getPostCode(): ?string
    {
        return $this->postCode;
    }

    public function setPostCode(?string $postCode): static
    {
        $this->postCode = $postCode;
        return $this;
    }

    public function getCity(): ?string
    {
        return $this->city;
    }

    public function setCity(?string $city): static
    {
        $this->city = $city;
        return $this;
    }

    public function getCountry(): ?Country
    {
        return $this->country;
    }

    public function setCountry(?Country $country): static
    {
        $this->country = $country;
        return $this;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function setEmail(?string $email): static
    {
        $this->email = $email;
        return $this;
    }
}
