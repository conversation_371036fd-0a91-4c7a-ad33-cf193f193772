<?php

namespace App\Entity\Enum;

enum Languages : string
{
    case EN = 'en';
    case PL = 'pl';
    case CS = 'cs';
    case RU = 'ru';
    case LV = 'lv';
    case LT = 'lt';
    case SV = 'sv';

    public static function toArray(): array
    {
        return [
            [
                'locale' => self::PL->value,
                'name' => 'Polski',
                'code' => 'pl_PL',
                'country_code' => 'PL',
            ],
            [
                'locale' => self::EN->value,
                'name' => 'English',
                'code' => 'en_US',
                'country_code' => 'US',
            ],
            [
                'locale' => self::RU->value,
                'name' => 'русский',
                'code' => 'ru_RU',
                'country_code' => 'RU',
            ],
            [
                'locale' => self::LT->value,
                'name' => 'Lietuvos',
                'code' => 'lt_LT',
                'country_code' => 'LT',
            ],
            [
                'locale' => self::SV->value,
                'name' => 'svenska',
                'code' => 'sv_SE',
                'country_code' => 'SE',
            ],
            [
                'locale' => self::LV->value,
                'name' => 'Latvia',
                'code' => 'lv_LV',
                'country_code' => 'LV',
            ],
            [
                'locale' => self::CS->value,
                'name' => 'Czech',
                'code' => 'cs_CZ',
                'country_code' => 'CZ',
            ],
        ];
    }
}
