<?php

namespace App\Entity;

use App\Repository\ReceiptRepository;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Annotation\Groups;

#[ORM\Entity(repositoryClass: ReceiptRepository::class)]
class Receipt extends Locale
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    #[Groups(['payments:list', 'receipt:list', 'receipt:basic'])]
    private int $id;

    #[ORM\Column(type: 'text', nullable: true)]
    #[Groups(['receipt:read'])]
    private ?string $image = '';

    #[ORM\ManyToOne(targetEntity: User::class, inversedBy: 'receipts')]
    #[Groups(['receipt:list', 'receipt:read'])]
    private ?User $user = null;

    #[ORM\OneToOne(targetEntity: MobilePayment::class, mappedBy: 'receipt')]
    #[Groups(['receipt:list', 'receipt:read'])]
    private ?MobilePayment $mobilePayment = null;

    #[ORM\Column(type: 'text', nullable: true)]
    private ?string $response = null;

    #[ORM\Column(type: 'json', nullable: true)]
    #[Groups(['receipt:read'])]
    private array $jws = [];

    #[ORM\Column(type: 'integer', nullable: true)]
    #[Groups(['receipt:list', 'receipt:read', 'receipt:basic'])]
    private ?int $number = null;

    #[ORM\Column(type: 'integer', nullable: true)]
    #[Groups(['receipt:list', 'receipt:read', 'receipt:basic'])]
    private ?int $jpkId = null;

    #[ORM\Column(type: 'string', length: 16, nullable: true)]
    private ?string $uid = null;


    #[ORM\Column(type: 'datetime', nullable: true)]
    #[Groups(['receipt:list', 'receipt:read', 'receipt:basic'])]
    private ?\DateTimeInterface $time = null;

    #[ORM\Column(type: 'float', nullable: true)]
    #[Groups(['receipt:list', 'receipt:read', 'receipt:basic'])]
    private ?float $gross = null;

    #[ORM\Column(type: 'float', nullable: true)]
    #[Groups(['receipt:list', 'receipt:read', 'receipt:basic'])]
    private ?float $vat = null;

    #[ORM\Column(name: 'currency', type: 'string', length: 10, nullable: true)]
    #[Groups(['receipt:list', 'receipt:read', 'receipt:basic'])]
    private ?string $currencyCode = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getImage(): string
    {
        return $this->image;
    }

    public function setImage(?string $image): Receipt
    {
        $this->image = $image;
        return $this;
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function setUser(?User $user): Receipt
    {
        $this->user = $user;
        return $this;
    }

    public function getMobilePayment(): ?MobilePayment
    {
        return $this->mobilePayment;
    }

    public function setMobilePayment(?MobilePayment $mobilePayment): Receipt
    {
        $this->mobilePayment = $mobilePayment;
        return $this;
    }

    public function getResponse(): ?string
    {
        return $this->response;
    }

    public function setResponse(array $response): Receipt
    {
        $this->response = json_encode($response);

        $this->parse($response);

        return $this;
    }

    #[Groups(['payments:list', 'receipt:list'])]
    public function getJpkId(): ?int
    {
        return $this->jpkId;
    }

    public function getUid(): ?string
    {
        return $this->uid;
    }

    public static function base64urldecode($arg)
    {
        $s = $arg;
        $s = str_replace('-', '+', $s); // 62nd char of encoding
        $s = str_replace('_', '/', $s); // 63rd char of encoding
        switch (strlen($s) % 4) { // Pad with trailing '='s
            case 0:
                break; // No pad chars in this case
            case 2:
                $s .= "==";
                break; // Two pad chars
            case 3:
                $s .= "=";
                break; // One pad char
            default:
                throw new \Exception("Illegal base64url string!");
        }
        return base64_decode($s); // Standard base64 decoder
    }

    public static function decodeJws($jwsEncoded): array
    {
        $data = explode('.', $jwsEncoded);
        $unzip = gzinflate(self::base64urldecode($data[1]));
        return json_decode($unzip, true);
    }

    public function getJws(): array
    {
        return $this->jws;
    }

    public function getNumber(): ?int
    {
        return $this->number;
    }

    /**
     * @return \DateTimeInterface
     */
    public function getTime(): ?\DateTimeInterface
    {
        return $this->getLocalTime($this->time);
    }

    public function getGross(): ?float
    {
        return $this->gross;
    }

    public function getVat(): ?float
    {
        return $this->vat;
    }

    public function parse(array $eParagon): void
    {
        $this->image = $eParagon['WizualizacjaBase64'];
        $this->jws = self::decodeJws($eParagon['JWS']);

        $dokument = $this->jws["dokument"];
        $paragon = $dokument["paragon"];

        $this->number = $paragon["nrParag"];
        $this->time = new \DateTime($paragon["zakSprzed"]);
        $this->gross = $paragon["podsum"]["sumaBrutto"] / 100;
        $this->vat = $paragon["podsum"]["sumaPod"] / 100;
        $this->currencyCode = $paragon["podsum"]["waluta"];
        $this->jpkId = $paragon["JPKID"];
        $this->uid = $dokument["podmiot1"]["nrUnik"];
    }

    public function getCurrencyCode(): ?string
    {
        return $this->currencyCode;
    }

    public function getDocument(): array
    {
        return [
            'number' => (string) $this->getNumber(),
            'type' => "receipt",
            'id' => $this->getId(),
            'uri' => "/receipt/{$this->getId()}/download"
        ];
    }
}
