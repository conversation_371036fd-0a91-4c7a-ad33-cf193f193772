<?php

namespace App\Entity;

use DateTime;
use <PERSON>trine\ORM\Mapping as ORM;
use App\Repository\PromotionalCodesGroupsRepository;
use Symfony\Component\Serializer\Annotation as Serializer;

/**
 * PromotionalCodesGroup
 *
 *
 */
#[ORM\Table(name: 'bkfpay_promotional_codes_groups')]
#[ORM\Entity(repositoryClass: PromotionalCodesGroupsRepository::class)]
#[ORM\HasLifecycleCallbacks]
class PromotionalCodesGroup
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private int $id;

    #[ORM\Column(type: 'string', length: 16, nullable: false)]
    private string $type;

    #[ORM\Column(type: 'string', length: 19, nullable: false)]
    #[Serializer\Groups(['payments:list'])]
    private string $name;

    #[ORM\Column(type: 'datetime')]
    private DateTime $ctime;

    #[ORM\Column(type: 'datetime', nullable: true)]
    private ?DateTime $startDate;

    #[ORM\Column(type: 'datetime', nullable: true)]
    private ?DateTime $expirationDate;

    #[ORM\Column(type: 'integer')]
    private int $count;

    #[ORM\Column(type: 'string', length: 255, nullable: true)]
    private string $source;

    public function getId(): int
    {
        return $this->id;
    }

    public function setId(int $id): void
    {
        $this->id = $id;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): void
    {
        $this->name = $name;
    }

    public function getCtime(): DateTime
    {
        return $this->ctime;
    }

    public function setCtime(DateTime $ctime): void
    {
        $this->ctime = $ctime;
    }

    /**
     * @return DateTime
     */
    public function getStartDate(): ?DateTime
    {
        return $this->startDate;
    }

    /**
     * @param DateTime $startDate
     */
    public function setStartDate(?DateTime $startDate): void
    {
        $this->startDate = $startDate;
    }

    public function getCount(): int
    {
        return $this->count;
    }

    public function setCount(int $count): void
    {
        $this->count = $count;
    }

    public function getSource(): string
    {
        return $this->source;
    }

    public function setSource(string $source): void
    {
        $this->source = $source;
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function setType(string $type): void
    {
        $this->type = $type;
    }

    /**
     * @return DateTime
     */
    public function getExpirationDate(): ?DateTime
    {
        return $this->expirationDate;
    }

    /**
     * @param DateTime $expirationDate
     */
    public function setExpirationDate(?DateTime $expirationDate): void
    {
        $this->expirationDate = $expirationDate;
    }

    #[ORM\PrePersist]
    public function updatedTimestamps(): void
    {
        $this->setCtime(new \DateTime('now'));
    }
}
