<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;
use I2m\Invoices\Interface\IPosition;

#[ORM\Entity]
class InvoicePosition implements IPosition
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id;

    #[ORM\ManyToOne(inversedBy: 'positions')]
    #[ORM\JoinColumn(nullable: false)]
    private ?Invoices $invoice = null;

    #[ORM\Column(length: 255)]
    private ?string $name = null;

    #[ORM\Column]
    private ?float $quantity = null;

    #[ORM\Column]
    private ?float $grossPrice = null;

    #[ORM\Column]
    private ?float $totalGross = null;

    #[ORM\Column(nullable: true)]
    private ?int $tax = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getInvoice(): ?Invoices
    {
        return $this->invoice;
    }

    public function setInvoice(?Invoices $invoice): static
    {
        $this->invoice = $invoice;

        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): static
    {
        $this->name = $name;

        return $this;
    }

    public function getQuantity(): ?float
    {
        return $this->quantity;
    }

    public function setQuantity(float $quantity): static
    {
        $this->quantity = $quantity;
        $this->totalGross = $this->grossPrice * $this->quantity;
        return $this;
    }

    public function getGrossPrice(): ?float
    {
        return $this->grossPrice;
    }

    public function setGrossPrice(float $grossPrice): static
    {
        $this->grossPrice = $grossPrice;
        $this->totalGross = $this->grossPrice * $this->quantity;
        return $this;
    }

    public function getTotalGross(): ?float
    {
        return $this->totalGross;
    }

    public function setTotalGross(float $totalGross): static
    {
        $this->totalGross = $totalGross;

        return $this;
    }

    public function getTax(): ?int
    {
        return $this->tax;
    }

    public function setTax(?int $tax): static
    {
        $this->tax = $tax;

        return $this;
    }

    public function getTotalNet(): float
    {
        return $this->getTotalGross() - $this->getTotalTax();
    }

    public function getTotalTax(): float
    {
        return $this->getTax() ?
            round($this->getTotalGross() * ($this->getTax() / ($this->getTax() + 100)), 2) :
            0;
    }
}
