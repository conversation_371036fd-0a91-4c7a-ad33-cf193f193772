<?php

namespace App\Entity;

use App\Repository\CountryRepository;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Annotation\Groups;

#[ORM\Entity(repositoryClass: CountryRepository::class)]
class Country
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\Column(type: 'string', length: 50)]
    #[Groups(['default:basic'])]
    private string $name;

    #[ORM\Column(type: 'boolean')]
    private bool $inEuropeanUnion;


    #[Groups(['default:basic'])]
    #[ORM\Column(type: 'string', length: 2)]
    private string $shortName;

    public function getId(): int
    {
        return $this->id;
    }
    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function isInEuropeanUnion(): bool
    {
        return $this->inEuropeanUnion;
    }

    public function setInEuropeanUnion(bool $inEuropeanUnion): self
    {
        $this->inEuropeanUnion = $inEuropeanUnion;

        return $this;
    }

    public function getShortName(): string
    {
        return $this->shortName;
    }

    public function setShortName(string $shortName): self
    {
        $this->shortName = $shortName;

        return $this;
    }

    public function __toString()
    {
        return $this->getName();
    }
}
