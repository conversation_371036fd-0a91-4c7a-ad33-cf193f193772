<?php

namespace App\Entity;

use App\Entity\Enum\Languages;
use App\Repository\ClientRepository;
use DateTimeInterface;
use Doctrine\ORM\Mapping as ORM;
use I2m\Invoices\Interface\IClient;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;
use Symfony\Component\Serializer\Annotation\Groups;
use Symfony\Component\Serializer\Attribute\SerializedName;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Table]
#[ORM\Entity(repositoryClass: ClientRepository::class)]
#[ORM\HasLifecycleCallbacks]
class Client extends Company implements IClient
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    #[Groups(['default:basic'])]
    private int $id;

    #[ORM\Column(type: 'datetime', nullable: true)]
    private ?DateTimeInterface $ctime = null;

    #[ORM\Column(type: 'datetime', nullable: true)]
    private ?DateTimeInterface $mtime;


    #[Groups([ 'client:data'])]
    #[ORM\Column(type: 'boolean', nullable: true)]
    private ?bool $invoicedAfterTransaction;

    #[ORM\OneToOne(targetEntity: User::class, mappedBy: 'client')]
    private ?User $user;

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getCtime(): ?DateTimeInterface
    {
        return $this->ctime;
    }

    public function setCtime(?DateTimeInterface $ctime): self
    {
        $this->ctime = $ctime;

        return $this;
    }

    public function getMtime(): ?DateTimeInterface
    {
        return $this->mtime;
    }

    public function setMtime(?DateTimeInterface $mtime): self
    {
        $this->mtime = $mtime;

        return $this;
    }

    public function isInvoicedAfterTransaction(): ?bool
    {
        return $this->invoicedAfterTransaction;
    }

    public function setInvoicedAfterTransaction(?bool $invoicedAfterTransaction): self
    {
        $this->invoicedAfterTransaction = $invoicedAfterTransaction;

        return $this;
    }

    /**
     * Are invoice data filled
     */
    public function areInvoiceDataFilled(): bool
    {
        return $this->getName() &&
            $this->getAddress() &&
            $this->getCity() &&
            // można dodać z powrotem po naprawie
            // https://gitlab.bkf.pl/bkf/ebkf/beloyal-2.0/beloyal/-/issues/150#note_806572
            //$this->getPostCode() &&
            $this->getCountry() &&
            $this->getTaxNumber();
    }

    #[ORM\PrePersist]
    #[ORM\PreUpdate]
    public function updatedTimestamps(): void
    {
        if ($this->getCtime() == null) {
            $this->setCtime(new \DateTime('now'));
        }

        $this->setMtime(new \DateTime('now'));
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function setUser(?User $user): Client
    {
        $this->user = $user;
        return $this;
    }

    /**
     * @deprecated use getTaxNumber()
     */
    #[Groups([ 'client:data'])]
    public function getNip(): ?string
    {
        return $this->getTaxNumber();
    }

    /**
     * @deprecated use setTaxNumber()
     */
    #[Groups([ 'client:data'])]
    public function setNip(?string $nip): static
    {
        $this->setTaxNumber($nip);
        return $this;
    }
}
