<?php

namespace App\Entity;

use App\Repository\UserSettingsRepository;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Annotation\Groups;

#[ORM\Entity(repositoryClass: UserSettingsRepository::class)]
class UserSettings
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    #[Groups(['user:settings:basic'])]
    private int $id;

    #[ORM\OneToOne(targetEntity: User::class, inversedBy: "settings")]
    private ?User $user = null;

    #[ORM\Column(type: 'float', nullable: true)]
    #[Groups(['user:settings:basic'])]
    private $limitDaily;

    #[ORM\Column(type: 'float', nullable: true)]
    #[Groups(['user:settings:basic'])]
    private $limitHourly;

    #[ORM\Column(type: 'float', nullable: true)]
    #[Groups(['user:settings:basic'])]
    private $limitMonthly;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getLimitDaily(): ?float
    {
        return $this->limitDaily;
    }

    public function setLimitDaily(?float $limitDaily): self
    {
        $this->limitDaily = $limitDaily;

        return $this;
    }

    public function getLimitHourly(): ?float
    {
        return $this->limitHourly;
    }

    public function setLimitHourly(?float $limitHourly): self
    {
        $this->limitHourly = $limitHourly;

        return $this;
    }

    public function getLimitMonthly(): ?float
    {
        return $this->limitMonthly;
    }

    public function setLimitMonthly(?float $limitMonthly): self
    {
        $this->limitMonthly = $limitMonthly;

        return $this;
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function setUser(?User $user): UserSettings
    {
        $this->user = $user;
        return $this;
    }
}
