<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;
use I2m\Payment\Enum\Status;
use I2m\Payment\Interface\IConfig;
use I2m\Payment\Interface\IPayment;
use Symfony\Component\Serializer\Annotation\Groups;

#[ORM\Table(name: 'external_payment')]
#[ORM\Entity(repositoryClass: \App\Repository\ExternalPaymentRepository::class)]
class ExternalPayment extends Locale implements IPayment
{
    public const ISSUER_GATE = 'GATE';
    public const ISSUER_TRANSFER = 'TRANSFER';
    public const ISSUER_CREDIT_CARD = 'CREDIT_CARD';

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    #[Groups(['external:list', 'default:basic'])]
    private $id;

    #[ORM\ManyToOne(targetEntity: User::class)]
    #[Groups(['external:list'])]
    private ?User $user = null;

    #[ORM\OneToOne(targetEntity: MobilePayment::class, inversedBy: "externalPayment")]
    #[Groups(['external:list'])]
    private ?MobilePayment $mobilePayment = null;

    #[ORM\OneToOne(targetEntity: Invoices::class, mappedBy: "externalPayment")]
    private ?Invoices $invoice = null;

    #[ORM\Column(name: 'issuer', type: 'string', length: 16)]
    #[Groups(['external:list', 'default:basic'])]
    private string $issuer;

    #[ORM\Column(name: 'initiation_timestamp', type: 'datetime')]
    #[Groups(['external:list'])]
    private ?\DateTimeInterface $initiatedTimestamp = null;

    #[ORM\Column(name: 'confirmation_timestamp', type: 'datetime', nullable: true)]
    #[Groups(['external:list'])]
    private ?\DateTimeInterface $confirmedTimestamp = null;


    #[ORM\Column(type: 'float', nullable: true)]
    #[Groups(['external:list', 'default:basic'])]
    private ?float $value;

    #[ORM\Column(type: 'float', nullable: true)]
    #[Groups(['external:list'])]
    private ?float $refunded;

    #[ORM\Column(name: 'status', type: 'string', length: 16, enumType: Status::class)]
    #[Groups(['external:list', 'default:basic'])]
    private Status $status;


    #[ORM\Column(name: 'additional_data', type: 'text', nullable: true)]
    private string $additionalData;

    #[ORM\Column(name: 'external_id', type: 'text', nullable: true)]
    #[Groups(['external:list'])]
    private ?string $externalId = null;

    #[ORM\Column(type: 'string', length: 6, nullable: true)]
    private ?string $paymentTerm;
    private bool $isSuccess = false;

    #[ORM\Column(type: 'text', nullable: true)]
    private ?string $description = null;

    #[ORM\Column(type: 'string', length: 32, nullable: true)]
    private ?string $digest = null;

    #[ORM\ManyToOne(targetEntity: PaymentGate::class)]
    private ?PaymentGate $gate = null;

    #[ORM\Column(type: 'string', length: 64, nullable: true)]
    private ?string $onSuccess = null;

    public function getPaymentTerm(): ?string
    {
        return $this->paymentTerm;
    }

    public function setPaymentTerm(?string $paymentTerm): ExternalPayment
    {
        $this->paymentTerm = $paymentTerm;
        return $this;
    }

    private ?string $redirectUrl = null;

    #[ORM\ManyToOne]
    private ?ExternalCreditCardToken $ecct = null;

    public function getId(): int
    {
        return $this->id;
    }
    public function getStatus(): Status
    {
        return $this->status;
    }
    public function setStatus(Status $status): ExternalPayment
    {
        $this->status = $status;
        return $this;
    }

    public function getInitiatedTimestamp(): \DateTimeInterface
    {
        return $this->getLocalTime($this->initiatedTimestamp);
    }
    public function setInitiatedTimestamp(?\DateTimeInterface $initiatedTimestamp): ExternalPayment
    {
        $this->initiatedTimestamp = $initiatedTimestamp;
        return $this;
    }
    public function getConfirmedTimestamp(): ?\DateTimeInterface
    {
        return $this->getLocalTime($this->confirmedTimestamp);
    }
    public function setConfirmedTimestamp(?\DateTimeInterface $confirmedTimestamp): ExternalPayment
    {
        $this->confirmedTimestamp = $confirmedTimestamp;
        return $this;
    }

    #[Groups(['external:list', 'default:basic'])]
    public function getTime(): ?\DateTimeInterface
    {
        return
            $this->getLocalTime($this->confirmedTimestamp) ??
            $this->getLocalTime($this->initiatedTimestamp);
    }
    public function getAdditionalData(): ?string
    {
        return $this->additionalData;
    }
    public function setAdditionalData(?array $additionalData): ExternalPayment
    {
        $this->additionalData = json_encode($additionalData);
        return $this;
    }

    public function getIssuer(): string
    {
        return $this->issuer;
    }

    public function setIssuer(string $issuer): ExternalPayment
    {
        $this->issuer = $issuer;
        return $this;
    }

    public function getExternalId(): ?string
    {
        return $this->externalId;
    }

    public function setExternalId(?string $externalId): ExternalPayment
    {
        $this->externalId = $externalId;
        return $this;
    }

    public function getValue(): float
    {
        return $this->value;
    }

    public function setValue(?float $value): ExternalPayment
    {
        $this->value = $value;
        return $this;
    }

    public function getUser(): User
    {
        return $this->user;
    }

    public function setUser(?User $user): self
    {
        $this->user = $user;
        return $this;
    }

    public function getMobilePayment(): ?MobilePayment
    {
        return $this->mobilePayment;
    }

    public function setMobilePayment(?MobilePayment $mobilePayment): self
    {
        $this->mobilePayment = $mobilePayment;
        return $this;
    }

    #[Groups(['external:list'])]
    public function getDocument(): ?array
    {
        return $this->mobilePayment?->getDocument();
    }

    public function getRedirectUrl(): ?string
    {
        return $this->redirectUrl;
    }

    public function setRedirectUrl(?string $redirectUrl): ExternalPayment
    {
        $this->redirectUrl = $redirectUrl;
        return $this;
    }

    public function isSuccess()
    {
        return $this->isSuccess;
    }

    public function setSuccess(bool $isSuccess)
    {
        return $this->isSuccess = $isSuccess;
    }

    public function isReadyToSalesDocument(): bool
    {
        return
            ($this->status == Status::CONFIRMED) ||
            (
                ($this->issuer == ExternalPayment::ISSUER_TRANSFER) &&
                ($this->status == Status::WAITING)
            );
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): ExternalPayment
    {
        $this->description = $description;
        return $this;
    }

    public function getDigest(): ?string
    {
        return $this->digest;
    }

    public function setDigest(?string $digest): ExternalPayment
    {
        $this->digest = $digest;
        return $this;
    }

    public function getGate(): ?PaymentGate
    {
        return $this->gate;
    }

    public function setGate(?PaymentGate $gate): ExternalPayment
    {
        $this->gate = $gate;
        return $this;
    }

    public function setOnSuccess(?string $onSuccess): ExternalPayment
    {
        $this->onSuccess = $onSuccess;
        return $this;
    }

    public function getOnSuccess(): ?string
    {
        return $this->onSuccess;
    }

    #[Groups(['external:list'])]
    public function getClient(): ?Client
    {
        return $this->getUser()->getClient();
    }

    public function getRefunded(): ?float
    {
        return $this->refunded ?? 0;
    }

    public function setRefunded(?float $refunded): ExternalPayment
    {
        $this->refunded = $refunded;
        return $this;
    }

    public function addRefunded(?float $refunded)
    {
        $this->refunded = ($this->refunded ?? 0) + $refunded;
    }

    public function getEcct(): ?ExternalCreditCardToken
    {
        return $this->ecct;
    }

    public function setEcct(?ExternalCreditCardToken $ecct): static
    {
        $this->ecct = $ecct;

        return $this;
    }

    public function getInvoice(): ?Invoices
    {
        return $this->invoice;
    }

    public function setInvoice(?Invoices $invoice): ExternalPayment
    {
        $this->invoice = $invoice;
        return $this;
    }
}
