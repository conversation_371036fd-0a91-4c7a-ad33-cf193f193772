<?php

namespace App\Entity;

use DateTime;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;

/**
 * VatTax.
 */
#[ORM\Table(name: 'vat_tax')]
#[ORM\Entity]
#[UniqueEntity('taxKey')]
class VatTax
{
    /**
     * @var int
     */
    #[ORM\Column(name: 'id', type: 'integer', nullable: false)]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    #[ORM\SequenceGenerator(sequenceName: 'vat_tax_id_seq', allocationSize: 1, initialValue: 1)]
    private $id;

    /**
     * @var DateTime
     */
    #[ORM\Column(name: 'ctime', type: 'datetime', nullable: true)]
    private $ctime;

    /**
     * @var DateTime
     */
    #[ORM\Column(name: 'mtime', type: 'datetime', nullable: true)]
    private $mtime;

    /**
     * @var string
     */
    #[ORM\Column(name: 'status', type: 'string', length: 1, nullable: true)]
    private $status = 'a';

    /**
     * @var string
     */
    #[ORM\Column(name: 'tax_key', type: 'string', length: 10, nullable: true, unique: true)]
    private $taxKey;

    /**
     * @var float
     */
    #[ORM\Column(name: 'tax_value', type: 'integer', nullable: false)]
    private $taxValue;

    /**
     * Get id.
     *
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set ctime.
     *
     * @param DateTime $ctime
     *
     * @return VatTax
     */
    public function setCtime($ctime)
    {
        $this->ctime = $ctime;

        return $this;
    }

    /**
     * Get ctime.
     *
     * @return DateTime
     */
    public function getCtime()
    {
        return $this->ctime;
    }

    /**
     * Set mtime.
     *
     * @param DateTime $mtime
     *
     * @return $this
     */
    public function setMtime($mtime)
    {
        $this->mtime = $mtime;

        return $this;
    }

    /**
     * Get mtime.
     *
     * @return DateTime
     */
    public function getMtime()
    {
        return $this->mtime;
    }

    /**
     * Set status.
     *
     * @param string $status
     *
     * @return $this
     */
    public function setStatus($status)
    {
        $this->status = $status;

        return $this;
    }

    /**
     * Get status.
     *
     * @return string
     */
    public function getStatus()
    {
        return $this->status;
    }

    /**
     * @return string
     */
    public function getTaxKey()
    {
        return $this->taxKey;
    }

    /**
     * @return VatTax
     */
    public function setTaxKey(string $taxKey)
    {
        $this->taxKey = $taxKey;

        return $this;
    }

    /**
     * Set taxValue.
     *
     * @param float $taxValue
     *
     * @return $this
     */
    public function setTaxValue($taxValue)
    {
        $this->taxValue = $taxValue;

        return $this;
    }

    /**
     * Get taxValue.
     *
     * @return float
     */
    public function getTaxValue()
    {
        return $this->taxValue;
    }

    /**
     * Get tax decimal value
     *
     * @return float
     */
    public function getTaxDecimalValue()
    {
        return $this->getTaxValue() !== 0.0 ? $this->getTaxValue() / 100 : $this->getTaxValue();
    }

    /**
     * To string method
     *
     * @return string
     */
    public function __toString()
    {
        return (string) $this->getTaxValue();
    }
}
