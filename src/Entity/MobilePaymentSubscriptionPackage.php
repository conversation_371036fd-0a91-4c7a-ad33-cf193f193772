<?php

namespace App\Entity;

use App\Entity\Enum\AggrementStatus;
use App\Entity\Enum\MobilePaymentStatus;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Annotation\Groups;

/**
 * MobilePaymentPackage
 */
#[ORM\Entity(repositoryClass: \App\Repository\MobilePaymentSubscriptionPackageRepository::class)]
class MobilePaymentSubscriptionPackage
{
    #[ORM\Column(name: 'id', type: 'integer')]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    #[Groups(['default:basic'])]
    private int $id;

    #[ORM\Column()]
    #[Groups(['default:basic'])]
    private float $value;

    #[ORM\ManyToOne(targetEntity: User::class)]
    #[Groups(['subscription:list'])]
    private User $user;

    #[ORM\ManyToOne(targetEntity: User::class)]
    #[Groups(['subscription:list'])]
    private User $fleet;

    #[ORM\Column(type: 'datetime', nullable: true)]
    #[Groups(['default:basic'])]
    private ?\DateTimeInterface $startTime = null;
    #[ORM\Column(type: 'datetime', nullable: true)]
    #[Groups(['default:basic'])]
    private ?\DateTimeInterface $endTime = null;

    #[ORM\Column(type: 'string', length: 16, enumType: MobilePaymentStatus::class)]
    #[Groups(['default:basic'])]
    private MobilePaymentStatus $status = MobilePaymentStatus::INITIATED;

    #[ORM\ManyToOne(targetEntity: MobilePaymentPackage::class)]
    private MobilePaymentPackage $package;

    #[Groups(['subscription:left'])]
    private ?float $left;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getValue(): ?float
    {
        return $this->value;
    }

    public function setValue(float $value): static
    {
        $this->value = $value;

        return $this;
    }

    public function getStartTime(): ?\DateTimeInterface
    {
        return $this->startTime;
    }

    public function setStartTime(\DateTimeInterface $startTime): static
    {
        $this->startTime = $startTime;

        return $this;
    }

    public function getEndTime(): ?\DateTimeInterface
    {
        return $this->endTime;
    }

    public function setEndTime(\DateTimeInterface $endTime): static
    {
        $this->endTime = $endTime;

        return $this;
    }

    public function getStatus(): ?MobilePaymentStatus
    {
        return $this->status;
    }

    public function setStatus(MobilePaymentStatus $status): static
    {
        $this->status = $status;

        return $this;
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function setUser(?User $user): static
    {
        $this->user = $user;

        return $this;
    }

    public function getFleet(): ?User
    {
        return $this->fleet;
    }

    public function setFleet(?User $fleet): static
    {
        $this->fleet = $fleet;

        return $this;
    }

    public function getPackage(): ?MobilePaymentPackage
    {
        return $this->package;
    }

    public function setPackage(?MobilePaymentPackage $package): static
    {
        $this->package = $package;

        return $this;
    }

    public function getLeft(): ?float
    {
        return $this->left;
    }

    public function setLeft(?float $left): MobilePaymentSubscriptionPackage
    {
        $this->left = $left;
        return $this;
    }
}
