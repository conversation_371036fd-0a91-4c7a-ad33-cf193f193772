<?php

namespace App\Entity;

use DateTimeImmutable;
use <PERSON>trine\ORM\Mapping as ORM;

#[ORM\Table(name: 'mobile_token')]
#[ORM\Index(name: 'mobile_token_token_idx', columns: ['token'])]
#[ORM\Entity]
class MobileToken
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private int $id;
    #[ORM\Column(type: 'string', length: 255)]
    private string $token;
    #[ORM\ManyToOne(targetEntity: User::class, inversedBy: 'mobileTokens')]
    #[ORM\JoinColumn(name: 'user_id', referencedColumnName: 'id')]
    private ?User $user;
    #[ORM\Column(type: 'string', length: 255, name: 'mobile_device')]
    private ?string $mobileDevice;
    #[ORM\Column(type: 'datetime_immutable', name: 'updated_at')]
    private DateTimeImmutable $updatedAt;
    public function getId(): int
    {
        return $this->id;
    }
    public function getToken(): string
    {
        return $this->token;
    }
    public function setToken(string $token): MobileToken
    {
        $this->token = $token;
        return $this;
    }
    public function getUser(): ?User
    {
        return $this->user;
    }
    public function setUser(?User $user): MobileToken
    {
        $this->user = $user;
        return $this;
    }
    public function getUpdatedAt(): DateTimeImmutable
    {
        return $this->updatedAt;
    }
    public function setUpdatedAt(DateTimeImmutable $updatedAt): MobileToken
    {
        $this->updatedAt = $updatedAt;
        return $this;
    }
    public function getMobileDevice(): ?string
    {
        return $this->mobileDevice;
    }
    public function setMobileDevice(?string $mobileDevice): MobileToken
    {
        $this->mobileDevice = $mobileDevice;
        return $this;
    }
}
