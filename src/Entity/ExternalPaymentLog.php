<?php

namespace App\Entity;

use App\Repository\ExternalPaymentLogRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity]
class ExternalPaymentLog
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\Column(type: 'datetime', options: ['default' => 'CURRENT_TIMESTAMP'])]
    private \DateTime $ctime;

    #[ORM\Column(type: 'json', nullable: true)]
    private ?array $addInfo = null;

    #[ORM\Column(type: 'string', length: 512, nullable: false)]
    private string $comment;

    #[ORM\ManyToOne(targetEntity: ExternalPayment::class)]
    private ?ExternalPayment $externalPayment = null;

    public function getId()
    {
        return $this->id;
    }


    public function getCtime(): \DateTime
    {
        return $this->ctime;
    }


    public function setCtime(\DateTime $ctime): self
    {
        $this->ctime = $ctime;
        return $this;
    }

    public function getAddInfo(): ?array
    {
        return $this->addInfo;
    }

    public function setAddInfo(?array $addInfo): self
    {
        $this->addInfo = $addInfo;
        return $this;
    }

    public function getComment(): string
    {
        return $this->comment;
    }

    public function setComment(string $comment): self
    {
        $this->comment = $comment;
        return $this;
    }

    public function getExternalPayment(): ?ExternalPayment
    {
        return $this->externalPayment;
    }


    public function setExternalPayment(?ExternalPayment $externalPayment): self
    {
        $this->externalPayment = $externalPayment;
        return $this;
    }
}
