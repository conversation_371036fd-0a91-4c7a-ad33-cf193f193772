<?php

namespace App\Entity;

use App\Entity\Enum\ReportType;
use App\Repository\ReportFileRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use I2m\Reports\Enum\FileExtention;
use I2m\Reports\Enum\ReportInterval;
use I2m\Reports\Enum\ReportStatus;
use I2m\Reports\Interface\ReportFileInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Serializer\Annotation\Groups;

#[ORM\Entity(repositoryClass: ReportFileRepository::class)]
#[ORM\HasLifecycleCallbacks]
class ReportFile implements ReportFileInterface
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    #[Groups(['report:file:list', 'default:basic'])]
    private int $id;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(nullable: true)]
    #[Groups(['report:file:list'])]
    private ?User $user = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $cloudPath = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    #[Groups(['report:file:list'])]
    private ?\DateTimeInterface $ctime = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    #[Groups(['report:file:list'])]
    private ?\DateTimeInterface $etime = null;

    #[ORM\Column(length: 255, enumType: ReportType::class)]
    #[Groups(['report:file:list'])]
    private ?ReportType $type = null;

    #[ORM\Column(enumType: ReportInterval::class, nullable: true, length: 16)]
    protected ?ReportInterval $reportInterval = null;

    #[ORM\Column(length: 16)]
    #[Groups(['report:file:list'])]
    private FileExtention $ext;

    #[ORM\Column(nullable: true)]
    private ?array $criterias = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $mtime = null;

    #[ORM\Column(length: 16)]
    #[Groups(['report:file:list'])]
    private ReportStatus $status = ReportStatus::NEW;

    #[ORM\Column(nullable: true)]
    #[Groups(['report:file:list'])]
    private ?array $email = null;

    #[ORM\Column(length: 255, nullable: true)]
    #[Groups(['report:file:list'])]
    private ?string $title = null;

    #[ORM\Column(length: 255, nullable: true)]
    #[Groups(['report:file:list'])]
    private ?string $emailText = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $info = null;

    #[ORM\Column(nullable: true)]
    private ?int $durration = null;

    public function getId(): int
    {
        return $this->id;
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function setUser(?User $user): static
    {
        $this->user = $user;

        return $this;
    }

    public function getCloudPath(): ?string
    {
        return $this->cloudPath;
    }

    public function setCloudPath(?string $cloudPath): static
    {
        $this->cloudPath = $cloudPath;

        return $this;
    }

    public function getCtime(): ?\DateTimeInterface
    {
        return $this->ctime;
    }

    public function getEtime(): ?\DateTimeInterface
    {
        return $this->etime;
    }

    public function setEtime(?\DateTimeInterface $etime): static
    {
        $this->etime = $etime;

        return $this;
    }

    public function getCriterias(): ?array
    {
        return $this->criterias;
    }

    public function setCriterias(?array $criterias): static
    {
        $this->criterias = $criterias;

        return $this;
    }

    public function getType(): ?ReportType
    {
        return $this->type;
    }

    public function setType(ReportType $type): static
    {
        $this->type = $type;

        return $this;
    }

    #[ORM\PrePersist]
    #[ORM\PreUpdate]
    public function updatedTimestamps()
    {
        $this->mtime = new \DateTimeImmutable('now');
        if (empty($this->ctime)) {
            $this->ctime = new \DateTimeImmutable('now');
        }
    }

    public function getMtime(): ?\DateTimeInterface
    {
        return $this->mtime;
    }

    public function getStatus(): ?ReportStatus
    {
        return $this->status;
    }

    public function setStatus(ReportStatus $status): static
    {
        $this->status = $status;

        return $this;
    }

    public function getEmail(): ?array
    {
        return $this->email;
    }

    public function setEmail(?array $email): static
    {
        $this->email = $email;

        return $this;
    }

    public function getTitle(): ?string
    {
        return $this->title;
    }

    public function setTitle(?string $title): static
    {
        $this->title = $title;

        return $this;
    }

    public function getExt(): ?FileExtention
    {
        return $this->ext;
    }

    public function setExt(FileExtention $ext): ReportFile
    {
        $this->ext = $ext;
        return $this;
    }

    public function getInfo(): ?string
    {
        return $this->info;
    }

    public function addInfo(?string $info): ReportFile
    {
        if (is_null($this->info)) {
            $this->info = $info;
            return $this;
        }

        $this->info  = '; ' . $info;


        return $this;
    }

    #[Groups(['report:file:list'])]
    public function getFileName(): ?string
    {
        return $this->cloudPath ? basename($this->cloudPath) : null;
    }

    public function getEmailText(): ?string
    {
        return $this->emailText;
    }

    public function setEmailText(?string $emailText): ReportFile
    {
        $this->emailText = $emailText;
        return $this;
    }

    public static function getReportStatus(ReportStatus $status)
    {
        switch ($status) {
            case ReportStatus::NEW:
            case ReportStatus::PROCESS:
            case ReportStatus::QUEUE:
                return JsonResponse::HTTP_ACCEPTED;
            case ReportStatus::DONE:
                return JsonResponse::HTTP_OK;
            case ReportStatus::ERROR:
                return JsonResponse::HTTP_UNPROCESSABLE_ENTITY;
        }
    }

    public function getDurration(): ?int
    {
        return $this->durration;
    }

    public function setDurration(?int $durration): static
    {
        $this->durration = $durration;
        return $this;
    }

    public function getReportInterval(): ?ReportInterval
    {
        return $this->reportInterval;
    }

    public function setReportInterval(?ReportInterval $reportInterval): ReportFile
    {
        $this->reportInterval = $reportInterval;
        return $this;
    }
}
