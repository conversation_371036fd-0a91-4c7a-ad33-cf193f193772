<?php

namespace App\Entity;

use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity()]
#[ORM\HasLifecycleCallbacks]
class InvoiceFranchise
{
    #[ORM\Id]
    #[ORM\OneToOne(inversedBy: 'franchise', cascade: ['persist', 'remove'])]
    private Invoices $invoice;

    #[ORM\Column(type: 'string', nullable: true)]
    private ?string $confirmEmail = null;

    #[ORM\Column(type: 'string', nullable: true)]
    private ?string $confirmIp = null;

    #[ORM\Column(type: 'string', nullable: true)]
    private ?string $billingPath = null;

    #[ORM\Column(type: Types::DATETIME_IMMUTABLE, nullable: true)]
    private ?\DateTimeImmutable $confirmDate = null;

    public function __construct(Invoices $invoice)
    {
        $this->invoice = $invoice;
    }

    public function getInvoice(): ?Invoices
    {
        return $this->invoice;
    }

    public function setInvoice(?Invoices $invoice): InvoiceFranchise
    {
        $this->invoice = $invoice;
        return $this;
    }

    public function getConfirmEmail(): ?string
    {
        return $this->confirmEmail;
    }

    public function setConfirmEmail(?string $confirmEmail): InvoiceFranchise
    {
        $this->confirmEmail = $confirmEmail;
        return $this;
    }

    public function getConfirmIp(): ?string
    {
        return $this->confirmIp;
    }

    public function setConfirmIp(?string $confirmIp): InvoiceFranchise
    {
        $this->confirmIp = $confirmIp;
        return $this;
    }

    public function getBillingPath(): ?string
    {
        return $this->billingPath;
    }

    public function setBillingPath(?string $billingPath): InvoiceFranchise
    {
        $this->billingPath = $billingPath;
        return $this;
    }

    public function getConfirmDate(): ?\DateTimeImmutable
    {
        return $this->confirmDate;
    }

    public function setConfirmDate(?\DateTimeImmutable $confirmDate): InvoiceFranchise
    {
        $this->confirmDate = $confirmDate;
        return $this;
    }
}
