<?php

namespace App\Entity;

use App\Repository\PaymentGateRepository;
use Doctrine\DBAL\Types\Types;
use I2m\Payment\Enum\GateList;
use I2m\Payment\Interface\IConfig;
use I2m\Payment\Service\PaymentGateException;
use Doctrine\ORM\Mapping as ORM;
use I2m\StandardTypes\Enum\Currency;

#[ORM\Entity(repositoryClass: PaymentGateRepository::class)]
class PaymentGate implements IConfig
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: Types::INTEGER)]
    private int $id;

    #[ORM\Column(length: 16, enumType: GateList::class)]
    private GateList $type;

    #[ORM\Column(type: Types::JSON)]
    private array $config = [];

    #[ORM\Column(length: 3, nullable: true, enumType: Currency::class)]
    private Currency $currency;

    #[ORM\Column(type: Types::STRING, nullable: true)]
    private ?string $comment = null;

    #[ORM\Column(type: Types::BOOLEAN, nullable: true)]
    private ?bool $enable = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $logo = null;

    public function getId(): int
    {
        return $this->id;
    }

    public function getType(): GateList
    {
        return $this->type;
    }

    public function setType(GateList $type): self
    {
        $this->type = $type;

        return $this;
    }

    public function getConfig(): array
    {
        return $this->config;
    }

    public function setConfig(array $config): self
    {
        $this->config = $config;

        return $this;
    }

    public function getCurrency(): Currency
    {
        return $this->currency;
    }

    public function setCurrency(Currency $currency): self
    {
        $this->currency = $currency;

        return $this;
    }

    public function getProperty(string $property): string
    {
        $val = $this->config[$property];

        if (empty($val)) {
            throw new PaymentGateException("Brak ustawienia $property dla płatnoci {$this->getType()->value} ($this->id)");
        }

        return $val;
    }

    public function getComment(): ?string
    {
        return $this->comment;
    }

    public function setComment(?string $comment): PaymentGate
    {
        $this->comment = $comment;
        return $this;
    }

    public function getLogo(): ?string
    {
        return $this->logo;
    }

    public function setLogo(?string $logo): PaymentGate
    {
        $this->logo = $logo;
        return $this;
    }

    public function getEnable(): ?bool
    {
        return $this->enable;
    }

    public function setEnable(?bool $enable): PaymentGate
    {
        $this->enable = $enable;
        return $this;
    }
}
