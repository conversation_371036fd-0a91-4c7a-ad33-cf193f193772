<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity()]
class UserStatus
{
    #[ORM\Id]
    #[ORM\OneToOne(inversedBy: 'liveStatus', cascade: ['persist', 'remove'])]
    #[ORM\JoinColumn(nullable: false)]
    private User $user;

    #[ORM\Column(length: 8, nullable: true)]
    private ?string $lastStandCodeSuccess = null;

    #[ORM\Column(nullable: true)]
    private ?\DateTime $lastStandCodeSuccessTime = null;

    #[ORM\Column(length: 8, nullable: true)]
    private ?string $lastStandCodeFail = null;


    #[ORM\Column(nullable: true)]
    private ?string $lastStandCodeFailMessage = null;

    #[ORM\Column(nullable: true)]
    private ?\DateTime $lastStandCodeFailTime = null;

    #[ORM\ManyToOne(targetEntity: MobilePayment::class)]
    private ?MobilePayment $lastMobilePayment;

    #[ORM\Column(nullable: true)]
    private ?string $appVersion = null;


    public function __construct(User $user)
    {
        $this->user = $user;
    }

    public function getUser(): User
    {
        return $this->user;
    }

    public function getLastStandCodeSuccess(): ?string
    {
        return $this->lastStandCodeSuccess;
    }

    public function setLastStandCodeSuccess(?string $lastStandCodeSuccess): static
    {
        $this->lastStandCodeSuccess = $lastStandCodeSuccess;

        return $this;
    }

    public function getLastStandCodeFail(): ?string
    {
        return $this->lastStandCodeFail;
    }


    public function setStandCodeFail($standId, string $message)
    {
        $this->lastStandCodeFail = $standId;
        $this->lastStandCodeFailTime = new \DateTime();
        $this->lastStandCodeFailMessage = $message;
    }

    public function setStandCodeSuccess($standId)
    {
        $this->lastStandCodeSuccess = $standId;
        $this->lastStandCodeSuccessTime = new \DateTime();
    }
    public function setLastStandCodeFail(?string $lastStandCodeFail): static
    {
        $this->lastStandCodeFail = $lastStandCodeFail;

        return $this;
    }

    public function getLastStandCodeSuccessTime(): ?\DateTime
    {
        return $this->lastStandCodeSuccessTime;
    }

    public function setLastStandCodeSuccessTime(?\DateTime $lastStandCodeSuccessTime): static
    {
        $this->lastStandCodeSuccessTime = $lastStandCodeSuccessTime;

        return $this;
    }

    public function getLastStandCodeFailTime(): ?\DateTime
    {
        return $this->lastStandCodeFailTime;
    }

    public function setLastStandCodeFailTime(?\DateTime $lastStandCodeFailTime): static
    {
        $this->lastStandCodeFailTime = $lastStandCodeFailTime;

        return $this;
    }

    public function getLastMobilePayment(): ?MobilePayment
    {
        return $this->lastMobilePayment;
    }

    public function setLastMobilePayment(?MobilePayment $lastMobilePayment): UserStatus
    {
        $this->lastMobilePayment = $lastMobilePayment;
        return $this;
    }

    public function getAppVersion(): ?string
    {
        return $this->appVersion;
    }

    public function setAppVersion(?string $appVersion): UserStatus
    {
        if ($appVersion) {
            $this->appVersion = $appVersion;
        }
        return $this;
    }

    public function getLastStandCodeFailMessage(): ?string
    {
        return $this->lastStandCodeFailMessage;
    }

    public function setLastStandCodeFailMessage(?string $lastStandCodeFailMessage): UserStatus
    {
        $this->lastStandCodeFailMessage = $lastStandCodeFailMessage;
        return $this;
    }
}
