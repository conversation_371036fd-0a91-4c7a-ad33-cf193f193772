<?php

namespace App\Entity;

use App\Entity\Enum\AggrementStatus;
use App\Entity\Enum\Languages;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use I2m\Invoices\Enum\InvoiceGeneratorType;
use I2m\Invoices\Interface\IIssuer;
use Symfony\Component\Serializer\Annotation\Groups;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;

/**
 * Owners
 */
#[ORM\Table(name: 'owners')]
#[ORM\Entity(repositoryClass: \App\Repository\OwnerRepository::class)]
#[ORM\HasLifecycleCallbacks]
class Owners extends Company implements IIssuer
{
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'NONE')]
    #[ORM\Column(type: 'integer')]
    #[Groups(['default:basic', 'owner:list'])]
    private int $id;

    #[ORM\Column(type: 'datetime')]
    #[Groups(['default:basic', 'owner:list'])]
    private ?\DateTimeInterface $ctime = null;


    #[ORM\Column(type: 'datetime')]
    #[Groups(['default:basic', 'owner:list'])]
    private ?\DateTimeInterface $mtime = null;

    #[ORM\OneToMany(targetEntity: Carwash::class, mappedBy: 'owner')]
    #[Groups(['owner:list'])]
    private $carwashes;

    #[ORM\Column(type: 'text', nullable: true)]
    #[Groups(['owner:details', 'owner:list'])]
    private ?string $link = null;

    #[ORM\Column(type: 'string', length: 16, enumType: AggrementStatus::class, nullable: true)]
    #[Groups(['owner:details', 'owner:list'])]
    private AggrementStatus $aggrement = AggrementStatus::INITIATED;

    #[Groups(['default:basic'])]
    #[ORM\Column(length: 16, enumType: InvoiceGeneratorType::class, nullable: true)]
    private ?InvoiceGeneratorType $invoiceType = null;

    #[ORM\Column(nullable: true)]
    private ?array $invoiceConfig = null;

    #[ORM\Column(type: 'string', length: 32, nullable: true)]
    protected ?string $bankAccountNumber = null;

    #[ORM\Column(type: 'text', nullable: true)]
    private ?string $logo;

    #[ORM\Column(enumType: Languages::class, length: 8, nullable: true)]
    private ?Languages $language = null;

    public function __construct()
    {
        $this->carwashes = new ArrayCollection();
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getCtime(): ?\DateTimeInterface
    {
        return $this->ctime;
    }

    public function setCtime(\DateTimeInterface $ctime): self
    {
        $this->ctime = $ctime;
        return $this;
    }

    public function getMtime(): ?\DateTimeInterface
    {
        return $this->mtime;
    }

    public function setMtime(?\DateTimeInterface $mtime): self
    {
        $this->mtime = $mtime;
        return $this;
    }

    /**
     * @return Collection<int, Carwash>
     */
    public function getCarwashes(): Collection
    {
        return $this->carwashes;
    }

    #[ORM\PrePersist]
    #[ORM\PreUpdate]
    public function updatedTimestamps(): void
    {
        if ($this->getCtime() == null) {
            $this->setCtime(new \DateTime('now'));
        }

        $this->setMtime(new \DateTime('now'));
    }

    public function getLink(): ?string
    {
        return $this->link;
    }

    public function setLink(?string $link): Owners
    {
        $this->link = $link;
        return $this;
    }

    public function getAggrement(): AggrementStatus
    {
        return $this->aggrement;
    }

    public function setAggrement(AggrementStatus $aggrement): Owners
    {
        $this->aggrement = $aggrement;
        return $this;
    }

    public function getInvoiceType(): InvoiceGeneratorType
    {
        return $this->invoiceType ?? InvoiceGeneratorType::Disabled;
    }

    public function setInvoiceType(InvoiceGeneratorType $invoiceType): Owners
    {
        $this->invoiceType = $invoiceType;
        return $this;
    }

    public function getInvoiceConfig(): ?array
    {
        return $this->invoiceConfig;
    }

    public function setInvoiceConfig(?array $invoiceConfig): Owners
    {
        $this->invoiceConfig = $invoiceConfig;
        return $this;
    }



    public function getBankAccountNumber(): ?string
    {
        return $this->bankAccountNumber;
    }

    public function setBankAccountNumber(?string $bankAccountNumber): Owners
    {
        $this->bankAccountNumber = $bankAccountNumber;
        return $this;
    }

    public function getLogo(): ?string
    {
        return $this->logo;
    }

    public function setLogo(?string $logo): Owners
    {
        $this->logo = $logo;
        return $this;
    }

    public function getLanguage(): ?Languages
    {
        return $this->language;
    }

    public function setLanguage(?Languages $language): Owners
    {
        $this->language = $language;
        return $this;
    }
}
