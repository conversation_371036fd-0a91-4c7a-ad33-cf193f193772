<?php

namespace App\Entity;

use DateTimeZone;
use <PERSON>trine\ORM\Mapping as ORM;
use <PERSON>ymfony\Component\Serializer\Annotation\Groups;

/**
 * MobilePaymentPackage
 */
#[ORM\Table(name: 'mobile_payment_package')]
#[ORM\Entity(repositoryClass: \App\Repository\MobilePaymentPackageRepository::class)]
class MobilePaymentPackage
{
    #[ORM\Column(name: 'id', type: 'integer')]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    #[Groups(['package:list'])]
    private int $id;


    #[ORM\Column(name: 'payment_value', type: 'float')]
    #[Groups(['package:list', 'package:edit'])]
    private float $paymentValue;

    #[ORM\Column(name: 'package_value', type: 'float')]
    #[Groups(['package:list', 'package:edit'])]
    private float $packageValue;

    #[ORM\Column(name: 'title', type: 'string', length: 75)]
    #[Groups(['package:list', 'package:edit'])]
    private string $title;


    #[ORM\Column(name: 'description', type: 'string', length: 255)]
    #[Groups(['package:list', 'package:edit'])]
    private string $description;

    #[ORM\Column(type: 'integer', nullable: true)]
    #[Groups(['package:list', 'package:edit'])]
    private ?int $validTime = null;

    #[ORM\Column(type: 'integer', nullable: true)]
    #[Groups(['package:list', 'package:edit'])]
    private ?int $sort = null;

    #[ORM\Column(name: 'status', type: 'string', length: 1)]
    private string $status = 'a';

    #[ORM\Column(name: 'hide_on_list', type: 'boolean', nullable: true, options: ['default' => 'false'])]
    private bool $hideOnList = false;
    #[ORM\Column(type: 'datetimetz', nullable: true)]
    #[Groups(['package:list', 'package:edit'])]
    private ?\DateTime $startTime = null;

    #[ORM\Column(type: 'datetimetz', nullable: true)]
    #[Groups(['package:list', 'package:edit'])]
    private ?\DateTime $endTime = null;

    /**
     * Get id.
     *
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * Set payment value
     *
     * @param float $paymentValue
     *
     * @return MobilePaymentPackage
     */
    public function setPaymentValue(float $paymentValue): self
    {
        $this->paymentValue = $paymentValue;

        return $this;
    }

    /**
     * Get payment value.
     *
     * @return float
     */
    public function getPaymentValue(): float
    {
        return $this->paymentValue;
    }

    /**
     * Set package value.
     *
     * @param float $packageValue
     *
     * @return MobilePaymentPackage
     */
    public function setPackageValue(float $packageValue): self
    {
        $this->packageValue = $packageValue;

        return $this;
    }

    /**
     * Get package value.
     *
     * @return float
     */
    public function getPackageValue(): float
    {
        return $this->packageValue;
    }

    /**
     * Set title.
     *
     * @param string $title
     *
     * @return MobilePaymentPackage
     */
    public function setTitle(string $title): self
    {
        $this->title = $title;

        return $this;
    }

    /**
     * Get title.
     *
     * @return string
     */
    public function getTitle(): string
    {
        return $this->title;
    }

    /**
     * Set description.
     *
     * @param string $description
     *
     * @return MobilePaymentPackage
     */
    public function setDescription(string $description): self
    {
        $this->description = $description;

        return $this;
    }

    /**
     * Get description.
     *
     * @return string
     */
    public function getDescription(): string
    {
        return $this->description;
    }

    public function getStatus(): string
    {
        return $this->status;
    }

    /**
     * Set status
     */
    public function setStatus(string $status): MobilePaymentPackage
    {
        $this->status = $status;
        return $this;
    }

    public function isHideOnList(): bool
    {
        return $this->hideOnList;
    }

    public function setHideOnList(bool $hideOnList): MobilePaymentPackage
    {
        $this->hideOnList = $hideOnList;
        return $this;
    }

    public function getBonus(): float
    {
        return ($this->packageValue - $this->paymentValue);
    }

    public function getValidTime(): ?int
    {
        return $this->validTime;
    }

    public function setValidTime(?int $validTime): MobilePaymentPackage
    {
        $this->validTime = $validTime;
        return $this;
    }

    public function getSort(): ?int
    {
        return $this->sort;
    }

    public function setSort(?int $sort): MobilePaymentPackage
    {
        $this->sort = $sort;
        return $this;
    }

    public function getStartTime(): ?\DateTime
    {
        return $this->startTime;
    }

    public function setStartTime(?\DateTime $startTime): MobilePaymentPackage
    {
        $this->startTime = $startTime;
        return $this;
    }

    public function getEndTime(): ?\DateTime
    {
        return $this->endTime;
    }

    public function setEndTime(?\DateTime $endTime): MobilePaymentPackage
    {
        $this->endTime = $endTime;
        return $this;
    }

    #[Groups(['package:list'])]
    public function isActive(): bool
    {
        $now = new \DateTimeImmutable();

        if ($this->status != 'a') {
            return false;
        }

        if ($this->startTime !== null && $now < $this->startTime) {
            return false;
        }

        if ($this->endTime !== null && $now >= $this->endTime) {
            return false;
        }

        return true;
    }
}
