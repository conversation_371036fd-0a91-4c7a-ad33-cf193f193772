<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;
use DateTime;
use Symfony\Component\Serializer\Annotation\Groups;

#[ORM\Table(name: 'external_credit_card_token')]
#[ORM\Entity(repositoryClass: \App\Repository\ExternalCreditCardTokenRepository::class)]
class ExternalCreditCardToken
{
    public const STATUS_INITIATE = 'INITIATE';
    public const STATUS_WAITING_FOR_VERIFICATION = 'WAITING_FOR_VERIFICATION';
    public const STATUS_VERIFIED = 'VERIFIED';
    public const STATUS_CANCELED = 'CANCELED';
    public const STATUS_DELETED = 'DELETED';


    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    #[ORM\Column(type: 'integer')]
    protected $id;

    /**
     * @var DateTime
     */
    #[ORM\Column(name: 'ctime', type: 'datetime')]
    private $ctime;

    /**
     *
     * @var DateTime
     */
    #[ORM\Column(name: 'expiration', type: 'datetime', nullable: true)]
    #[Groups(['creditcard:read'])]
    private $expiration;

    /**
     *
     * @var string
     */
    #[ORM\Column(name: 'status', type: 'string', length: 25)]
    private $status = self::STATUS_INITIATE;

    /**
     *
     * @var string
     */
    #[ORM\Column(name: 'card_brand', type: 'string', length: 16, nullable: true)]
    #[Groups(['creditcard:read'])]
    private $cardBrand;

    /**
     * @var int
     */
    #[ORM\Column(name: 'user_id', type: 'integer', nullable: false)]
    private $userId;

    /**
     * @var string
     */
    #[ORM\Column(name: 'additional_data', type: 'text', nullable: true)]
    private $additionalData;

    /**
     * This field should not contain masked credit card number
     *
     * @var string
     */
    #[Groups(['creditcard:read'])]
    #[ORM\Column(name: 'masked_card_number', type: 'string', length: 160, nullable: true)]
    private $maskedCardNumber;

    /**
     * This field contain
     *
     * @var string
     */
    #[ORM\Column(name: 'card_token', type: 'string', length: 160, nullable: true)]
    #[Groups(['creditcard:read'])]
    private $cardToken;

    #[ORM\Column(type: 'string', length: 16, nullable: true)]
    private ?string $expire = null;

    /**
     * @return mixed
     */
    public function getId()
    {
        return $this->id;
    }

    public function getCtime(): DateTime
    {
        return $this->ctime;
    }

    public function setCtime(DateTime $ctime): ExternalCreditCardToken
    {
        $this->ctime = $ctime;
        return $this;
    }

    /**
     * @return DateTime
     */
    public function getExpiration(): ?DateTime
    {
        return $this->expiration;
    }

    #[Groups(['creditcard:read'])]
    public function getExpire(): ?string
    {
        return
            $this->expire ??
            $this->getExpiration()?->format("Y/m");
    }

    public function setExpireShort(?string $expire): ExternalCreditCardToken
    {
        $this->setExpiration(
            \DateTime::createFromFormat('m/y', $expire)
                ->modify('first day of next month')
                ->setTime(0, 0)
                ->modify("-1 sec")
        );
        $this->expire = $expire;
        return $this;
    }

    public function setExpire(?string $expire): ExternalCreditCardToken
    {
        $this->setExpiration(
            \DateTime::createFromFormat('m/Y', $expire)
                ->modify('first day of next month')
                ->setTime(0, 0)
                ->modify("-1 sec")
        );
        $this->expire = $expire;
        return $this;
    }

    /**
     * @param DateTime $expiration
     */
    public function setExpiration(?DateTime $expiration): ExternalCreditCardToken
    {
        $this->expiration = $expiration;
        return $this;
    }

    public function getStatus(): string
    {
        return $this->status;
    }

    public function setStatus(string $status): ExternalCreditCardToken
    {
        $this->status = $status;
        return $this;
    }

    /**
     * @return string
     */
    public function getCardBrand(): ?string
    {
        return $this->cardBrand;
    }

    /**
     * @param string $cardBrand
     */
    public function setCardBrand(?string $cardBrand): ExternalCreditCardToken
    {
        $this->cardBrand = $cardBrand;
        return $this;
    }

    public function getUserId(): int
    {
        return $this->userId;
    }

    /**
     * @return ExternalCreditCardToken
     */
    public function setUserId(int $userId)
    {
        $this->userId = $userId;
        return $this;
    }

    public function getAdditionalData(): string
    {
        return $this->additionalData;
    }

    /**
     * @param string $additionalData
     */
    public function setAdditionalData(?string $additionalData): ExternalCreditCardToken
    {
        $this->additionalData = $additionalData;
        return $this;
    }

    /**
     * @return string
     */
    public function getMaskedCardNumber(): ?string
    {
        return $this->maskedCardNumber;
    }

    /**
     * @param string $maskedCardNumber
     */
    public function setMaskedCardNumber(?string $maskedCardNumber): ExternalCreditCardToken
    {
        $this->maskedCardNumber = $maskedCardNumber;
        return $this;
    }

    public function getCardToken(): string
    {
        return $this->cardToken;
    }

    public function setCardToken(string $cardToken): ExternalCreditCardToken
    {
        $this->cardToken = $cardToken;
        return $this;
    }
}
