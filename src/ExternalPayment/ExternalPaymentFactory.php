<?php

namespace App\ExternalPayment;

use App\Entity\ExternalPayment;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\ParameterBag;

class ExternalPaymentFactory
{
    public function __construct(
        private ExtPaymentEspagoService $extPaymentEspagoService,
        private ExtPaymentTransferService $extPaymentTransferService,
        private ExtPaymentPayExService $extPaymentPayExService,
        private ExtPaymentGateService $paymentGateService,
        private ExtPaymentKlixService $paymentKlixService,
        private ParameterBagInterface $parameterBag
    ) {
    }

    public function create(string $type): ExternalPaymentInterface
    {
        switch ($type) {
            case ExternalPayment::ISSUER_GATE:
                return $this->paymentGateService;
            case ExternalPayment::ISSUER_CREDIT_CARD:
                return $this->createCreditCardPayment();
            case ExternalPayment::ISSUER_TRANSFER:
                return $this->extPaymentTransferService;
        }

        throw new \Exception("nieznany dostawca platnosci $type");
    }

    private function createCreditCardPayment(): ExternalPaymentInterface
    {
        $type = $this->parameterBag->get("credit_card_provider");
        if ($type == "ESPAGO") {
            return $this->extPaymentEspagoService;
        } elseif ($type == "PAY_EX") {
            return $this->extPaymentPayExService;
        } elseif ($type == "KLIX") {
            return $this->paymentKlixService;
        }
        throw new \Exception("nieznany dostawca platnosci kartą kredytowa");
    }
    public function createCreditCard(): ExtPaymentCreditCardInterface
    {
        $type = $this->parameterBag->get("credit_card_provider");
        if ($type == "ESPAGO") {
            return $this->extPaymentEspagoService;
        } elseif ($type == "PAY_EX") {
            return $this->extPaymentPayExService;
        } elseif ($type == "KLIX") {
            return $this->paymentKlixService;
        }
        throw new \Exception("nieznany dostawca platnosci kartą kredytowa");
    }
}
