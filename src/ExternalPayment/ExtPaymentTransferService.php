<?php

namespace App\ExternalPayment;

use App\Entity\ExternalPayment;
use App\Entity\MobilePayment;
use App\Entity\User;
use App\ExternalPayment\Exceptions\ParameterValidationException;
use App\ExternalPayment\Exceptions\TransferPaymentException;
use App\Repository\ExternalPaymentRepository;

class ExtPaymentTransferService extends AbstractExternalPayment implements ExternalPaymentInterface
{
    public function __construct(
        private readonly ExternalPaymentRepository $externalPaymentRepository
    ) {
    }

    public function init(
        MobilePayment $mp,
        float $paymentValue,
        string $transactionDescription = null,
        ?array $config = null
    ): ExternalPayment {

        $user = $mp->getBkfpayUser();
        $externalPayment = $this->externalPaymentRepository->init(
            $mp,
            $mp->getBkfpayUser(),
            ExternalPayment::ISSUER_TRANSFER,
            $paymentValue
        );


        $externalPayment->setPaymentTerm($config['paymentTerm']);
        $this->externalPaymentRepository->wait($externalPayment);
        $externalPayment->setSuccess(true);

        return $externalPayment;
    }
}
