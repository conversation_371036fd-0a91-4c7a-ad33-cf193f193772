<?php

namespace App\ExternalPayment\Exceptions;

use Throwable;

/**
 * Class PaymentTimeoutException
 * @package App\ExternalPayment\Exception
 */
class PaymentTimeoutException extends \Exception
{
    /**
     * PaymentTimeoutException constructor.
     *
     * @param string $message
     * @param int $code
     * @param Throwable|null $previous
     */
    public function __construct($message = "", $code = 0, Throwable $previous = null)
    {
        $message = 'Payment Timeout. Time to confirm payment expired';

        parent::__construct($message, $code, $previous);
    }
}
