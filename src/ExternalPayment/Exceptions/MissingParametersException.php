<?php

namespace App\ExternalPayment\Exceptions;

/**
 * Class MissingParametersException
 * @package App\ExternalPayment\Exception
 */
class MissingParametersException extends \Exception
{
    public static function fromMissingParametersArray(array $inputArray, array $requiredFieldsArray): self
    {
        $messages = [];

        foreach ($requiredFieldsArray as $requiredField) {
            if (empty($inputArray[$requiredField])) {
                $messages[] = $requiredField;
            }
        }

        return new self(sprintf('Missing fields: %s', implode(', ', $messages)));
    }
}
