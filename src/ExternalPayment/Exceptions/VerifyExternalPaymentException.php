<?php

namespace App\ExternalPayment\Exceptions;

/**
 * Class VerifyExternalPaymentException
 * @package App\ExternalPayment\Exception
 */
class VerifyExternalPaymentException extends \Exception
{
    /** @var string $apiCode */
    private $apiCode;

    /**
     * VerifyExternalPaymentException constructor.
     */
    public function __construct(string $message, string $apiCode)
    {
        $this->apiCode = $apiCode;
        parent::__construct($message);
    }

    /**
     * @return string
     */
    public function getApiCode()
    {
        return $this->apiCode;
    }
}
