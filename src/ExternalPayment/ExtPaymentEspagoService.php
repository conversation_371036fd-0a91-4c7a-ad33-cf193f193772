<?php

namespace App\ExternalPayment;

use App\Entity\ExternalCreditCardToken;
use App\Entity\ExternalPayment;
use App\Entity\MobilePayment;
use App\Entity\User;
use App\ExternalConnectors\CreditCard\EspagoConnector;
use App\ExternalPayment\Exceptions\CreditCardApiException;
use App\Repository\ExternalCreditCardTokenRepository;
use App\Repository\ExternalPaymentRepository;
use App\Repository\LoggerRepository;
use I2m\Payment\Enum\Status;
use Symfony\Component\HttpFoundation\Request;

use function Sentry\captureMessage;

class ExtPaymentEspagoService extends AbstractExternalPayment implements ExternalPaymentInterface, ExtPaymentCreditCardInterface
{
    // https://start.espago.com/pl/docs/360229/profil_klienta-atrybuty_profilu_klienta_i_karty
    private const COMPANY = [
        "VI" => "VISA",
        "MC" => "MasterCard",
        "MD" => "Maestro",
        "AX" => "American Express",
        "DC" => "Diners Club",
        "JC" => "JCB",
        "SW" => "Switch",
        "SO" => "Solo",
        "LA" => "Laser"
        ];
    public function __construct(
        private readonly ExternalPaymentRepository $externalPaymentRepository,
        private readonly EspagoConnector $creditCardApi,
        private readonly ExternalCreditCardTokenRepository $cardTokenRepository,
        private readonly LoggerRepository $loggerRepository
    ) {
    }

    public function init(
        MobilePayment $mp,
        float $paymentValue,
        string $transactionDescription = null,
        ?array $config = null
    ): ExternalPayment {

        $cc_client = $config['token'];
        $skip3DSecure = $config['skip3DSecure'];

        $externalPayment = $this->externalPaymentRepository->init(
            $mp,
            $mp->getBkfpayUser(),
            ExternalPayment::ISSUER_CREDIT_CARD,
            $paymentValue
        );
        $ecct = $this->cardTokenRepository->getCardByToken($mp->getBkfpayUser(), $cc_client);
        $externalPayment->setEcct($ecct);

        $creditCardApiResponse = $this->creditCardApi->charge(
            $ecct->getCardToken(),
            null,
            $paymentValue,
            $transactionDescription,
            $skip3DSecure,
            $mp->getBkfpayUser()->getEmail(),
            $mp->getBkfpayUser()->getClient()?->getName()
        );
        $this->loggerRepository->log($mp->getBkfpayUser(), "ESPAGO", "charge:init", json_encode($creditCardApiResponse));

        $status = $creditCardApiResponse['status'];
        $content = $creditCardApiResponse['content'];

        $externalId = $content['id'];
        $state = $content['state'];


        if ($status != 201) {
            $errors = $status . '';
            if (isset($content['errors'])) {
                foreach ($content['errors'] as $error) {
                    $errors .= $error['message'];
                }
            }
            $this->externalPaymentRepository->reject(
                $externalPayment,
                $externalId,
                $creditCardApiResponse
            );
            throw new CreditCardApiException('Payment failed: ' . $errors);
        }

        if (in_array($state, ['new','executed'])) {
            $externalPayment->setRedirectUrl($content['redirect_url'] ?? null);
            $externalPayment->setPaymentTerm('P0M');
            $this->externalPaymentRepository->wait(
                $externalPayment,
                $externalId,
                $creditCardApiResponse
            );

            return $externalPayment;
        }

        $this->externalPaymentRepository->reject($externalPayment, $externalId, $creditCardApiResponse);
        throw new CreditCardApiException('Payment rejected');
    }

    public function handleStatus(Request $request): ?ExternalPayment
    {
        $back_request = json_decode($request->getContent(), true);
        $transactionId = $back_request['id'];
        $state = $back_request['state'];

        $ep = $this->externalPaymentRepository->findByIssuer(ExternalPayment::ISSUER_CREDIT_CARD, $transactionId);

        $this->externalPaymentRepository->log($ep, $state, $back_request);


        // https://start.espago.com/pl/docs/300215/platnosc_jednorazowa-mozliwe_stany_platnosci
        switch ($state) {
            case 'executed':
                if ($ep->getStatus() == Status::WAITING) {
                    return $this->externalPaymentRepository->confirm($ep, null, $back_request);
                }
                $this->externalPaymentRepository->log($ep, "to early $state", $back_request);
                return $ep;

            case 'rejected':
            case 'failed':
            case 'resigned':
                $this->externalPaymentRepository->reject($ep);
                return $ep;

            case 'reversed':
            case 'refunded':
                $this->externalPaymentRepository->refund($ep);
                return $ep;
        }

        captureMessage("nieobsłużony stan espago $state");

        return $ep;
    }

    public function addInit(User $user, array $config)
    {
        return
            [
                "type" => "app",
                //"redirect_url" => null
            ];
    }

    public function addRegister(User $user, array $cardData): ?ExternalCreditCardToken
    {

        $firstName = $cardData['first_name'];
        $lastName = $cardData['last_name'];
        $number = $cardData['number'];
        $cvv = $cardData['verification_value'];
        $year = $cardData['year'];
        $month = $cardData['month'];

        $cardToken = $this->cardTokenRepository->initCreditCardToken($user);
        $tokenResponse = $this->creditCardApi->registerCreditCard(
            $firstName,
            $lastName,
            $number,
            $cvv,
            $year,
            $month,
            "ecct_{$cardToken->getId()}"
        );

        $this->loggerRepository->log($user, "ESPAGO", "addRegister:token", json_encode($tokenResponse));
        $status = $tokenResponse['status'];

        if ($status != 201) {
            throw new CreditCardApiException("Registration error status: $status");
        }

        $token = $tokenResponse['content']['id'];
        $last4 = $tokenResponse['content']['card']['last4'];
        $company = self::COMPANY[$tokenResponse['content']['card']['company']];

        $cardToken
            ->setMaskedCardNumber("************" . $last4)
            ->setCardBrand($company)
            ->setAdditionalData(json_encode($tokenResponse))
            ->setExpire("$month/$year")
        ;

        $this->cardTokenRepository->save($cardToken);

        $clientResponse = $this->creditCardApi->registerClientToken(
            $token,
            $user->getClient()?->getName(),
            $user->getEmail()
        );

        $this->loggerRepository->log($user, "ESPAGO", "addRegister:client", json_encode($clientResponse));
        $cardToken->setCardToken($clientResponse["content"]["id"]);
        $this->cardTokenRepository->confirm($cardToken);

        return $cardToken;
    }

    public function addStatus(Request $request)
    {
        // TODO: Implement addStatus() method.
    }

    public function refund(
        ExternalPayment $ep,
        ?float $amount = null
    ): bool {
        $data = $this->creditCardApi->refund($ep->getExternalId(), $amount);
        $status = $data['status'];
        $ep->setStatus(Status::REFUNDING);
        $this->loggerRepository->log($ep->getUser(), "ESPAGO", "refund:response", json_encode($data));

        // gdy płatność nie jest potwierdzona nie można zrobić refunda, trzeba anulować płatność
        if ($status !== 201) {
            $data2 = $this->creditCardApi->refundBeforeClearance($ep->getExternalId());
            $status2 = $data2['status'];
            $this->loggerRepository->log($ep->getUser(), "ESPAGO", "refund:response", json_encode($data2));

            if ($status2 >= 299) {
                return false;
            }
            $this->loggerRepository->log(
                $ep->getUser(),
                "ESPAGO",
                "refund:success",
                json_encode(['external_payment_id' => $ep->getExternalId()])
            );
            return true;
        }

        $this->loggerRepository->log(
            $ep->getUser(),
            "ESPAGO",
            "refund:success",
            json_encode(['external_payment_id' => $ep->getExternalId()])
        );
        return true;
    }
}
