<?php

namespace App\ExternalPayment;

use App\Entity\ExternalCreditCardToken;
use App\Entity\ExternalPayment;
use App\Entity\MobilePayment;
use App\Entity\User;
use App\ExternalConnectors\PayEx\PayExConnector;
use App\ExternalPayment\Exceptions\CreditCardApiException;
use App\MobilePayment\MobilePaymentSecurity\VerifyMobilePaymentRequest;
use App\MobilePayment\MobilePaymentService;
use App\Repository\ExternalCreditCardTokenRepository;
use App\Repository\ExternalPaymentRepository;
use App\Repository\LoggerRepository;
use App\Service\AppConfig\AppConfigService;
use I2m\Payment\Enum\Status;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Request;

use function Sentry\captureMessage;

class ExtPaymentPayExService extends AbstractExternalPayment implements ExternalPaymentInterface, ExtPaymentCreditCardInterface
{
    private const CONFIRM_BY_LINK_URL = '/external_payment/pay-ex/confirm-by-link';
    private const SUCCESS_BY_LINK_URL = '/external_payment/pay-ex/success';
    private const CARD_ADD_LINK_URL = '/external_payment/pay-ex/card';


    public function __construct(
        private readonly ExternalPaymentRepository $externalPaymentRepository,
        private readonly PayExConnector $payExConnector,
        private readonly ExternalCreditCardTokenRepository $cardTokenRepository,
        private readonly VerifyMobilePaymentRequest $verifyMobilePaymentRequest,
        private AppConfigService $appConfigService,
        private ExternalCreditCardTokenRepository $externalCreditCardTokenRepository,
        private readonly LoggerRepository $loggerRepository
    ) {
    }
    public function init(
        MobilePayment $mp,
        float $paymentValue,
        string $transactionDescription = null,
        ?array $config = null
    ): ExternalPayment {


        $cardToken = $config['token'] ?? null;

        $creditCardToken =
            $this->cardTokenRepository->getCardByToken($mp->getBkfpayUser(), $cardToken)
        ;

        $externalPayment = $this->externalPaymentRepository->init(
            $mp,
            $mp->getBkfpayUser(),
            ExternalPayment::ISSUER_CREDIT_CARD,
            $paymentValue
        );
        $externalPayment->setEcct($creditCardToken);
        $this->externalPaymentRepository->save($externalPayment);

        $creditCardApiResponse = $this->payExConnector->chargeToken(
            $creditCardToken->getCardToken(),
            $paymentValue * 100, //TODO description
            $this->buildPayLink($externalPayment, self::CONFIRM_BY_LINK_URL),
            $this->buildPayLink($externalPayment, self::SUCCESS_BY_LINK_URL)
        );

        if (
            isset($creditCardApiResponse['status'])
            && ($creditCardApiResponse['status'] == 201)
        ) {
            if (
                isset($creditCardApiResponse['content']['payment']['state'])
                && ($creditCardApiResponse['content']['payment']['state'] === 'Ready')
            ) {
                $externalPayment->setPaymentTerm('P0M');
                $this->externalPaymentRepository->confirm($externalPayment, $creditCardApiResponse['content']['payment']['number'], $creditCardApiResponse);
                $externalPayment->setSuccess(true);
                return $externalPayment;
            }
            $this->externalPaymentRepository->reject($externalPayment, null, $creditCardApiResponse);
            throw new CreditCardApiException('Payment rejected');
        } else {
            $errors = '';

            if (isset($creditCardApiResponse['content']['problems'])) {
                $errors = '';
                foreach ($creditCardApiResponse['content']['problems'] as $error) {
                    $errors .= $error['description'];
                }
            }
            $this->externalPaymentRepository->reject($externalPayment, null, $creditCardApiResponse);
            throw new CreditCardApiException('Payment failed: ' . $errors);
        }
    }

    public function handleStatus(Request $request): ?ExternalPayment
    {

        $back_request = json_decode($request->getContent(), true);
        $paymentId = $request->query->get("paymentId");
        $url = $back_request['payment']['id'];
        $number = $back_request['payment']['number'];
        $data = $this->payExConnector->getResourceData($url);
        $state = $data['content']['payment']['state'];

        $ep = $this->externalPaymentRepository->find($paymentId);
        //https://developer.payex.com/xwiki/wiki/developer/view/Sandbox/payex-checkout-v1/Introduction/Payment/
        switch ($state) {
            case 'Ready':
                if ($ep->getStatus() == Status::WAITING) {
                    return $this->externalPaymentRepository->confirm($ep, $number, $data);
                }
                $this->externalPaymentRepository->log($ep, "$state", $data);
                return null;

            case 'Failed':
            case 'Aborted':
                $this->externalPaymentRepository->reject($ep, null, $data);
                return null;
            default:
                $this->externalPaymentRepository->log($ep, "unknown state: $state", $data);
                return null;
        }
    }

    public function addInit(User $user, array $inptuData)
    {
        $successUrl = $inptuData['onSuccess'];
        $cancelUrl = $inptuData['onCancel'];

        $creditCardToken = $this->externalCreditCardTokenRepository->initCreditCardToken(
            $user
        );

        $response = $this->payExConnector->addCreditCard(
            $this->buildCardLink($creditCardToken, self::CARD_ADD_LINK_URL),
            $successUrl,
            $cancelUrl,
        );
        $creditCardToken->setAdditionalData(json_encode($response['content']));
        $this->loggerRepository->log(null, "PAYEX", "addInit", json_encode($response));
        if (
            $response['status'] == 201
            && isset($response['content']['operations'])
        ) {
            $operations = $response['content']['operations'];
            foreach ($operations as $operation) {
                if ($operation['rel'] === "redirect-verification") {
                    $this->externalCreditCardTokenRepository->wait($creditCardToken);
                    return
                        [
                            "type" => "redirect",
                            "redirect_url" => $operation['href']
                        ];
                }
            }
        }
        throw new CreditCardApiException('Card add error');
    }

    public function addRegister(User $user, array $cardData): ?ExternalCreditCardToken
    {
        return null;
    }

    public function addStatus(Request $request)
    {
        $back_request = json_decode($request->getContent(), true);
        $paymentId = $request->query->get("paymentId");
        $url = $back_request['payment']['id'];
        $number = $back_request['payment']['number'];
        $data = $this->payExConnector->getResourceData($url);
        $this->loggerRepository->log(null, "PAYEX", "addStatus:$paymentId", json_encode($data));
        $payment = $data['content']['payment'];
        $state = $payment['state'];


        $ecct = $this->externalCreditCardTokenRepository->find($paymentId);
        switch ($state) {
            case 'Ready':
                $recurrenceToken = $payment["recurrenceToken"];
                $cardInfo = $this->payExConnector->getCreditCardInfo($recurrenceToken);
                $this->loggerRepository->log(null, "PAYEX", "addStatus:cardInfo", json_encode($cardInfo));
                $instrumentData = $cardInfo['content']['instrumentData'];
                $ecct
                    ->setCardBrand($instrumentData['cardBrand'])
                    ->setExpire($instrumentData['expiryDate'])
                    ->setMaskedCardNumber($instrumentData['maskedPan'])
                    ->setCardToken($recurrenceToken)
                    ;
                $this->externalCreditCardTokenRepository->confirm($ecct);
                return [];
            default:
                captureMessage("Nieobsluzony stan karty payex $state");
                return null;
        }
    }

    private function buildCardLink(ExternalCreditCardToken $cct, string $link)
    {
        $digest = $this->verifyMobilePaymentRequest->calculateDigest(
            0,
            $cct->getId(),
            (string)$cct->getCtime()->getTimestamp()
        );

        $param = [
            'paymentId' => $cct->getId(),
            'digest' => $digest
        ];
        return $this->buildLink($param, $link);
    }

    private function buildPayLink(ExternalPayment $ep, $link)
    {
        $digest = $this->verifyMobilePaymentRequest->calculateDigest(
            $ep->getValue(),
            $ep->getId(),
            (string)$ep->getInitiatedTimestamp()->getTimestamp()
        );

        return $this->buildLink([
                                    'paymentId' => $ep->getId(),
                                    'digest' => $digest
                                ], $link);
    }

    private function buildLink(array $param, string $link): string
    {
        $query = http_build_query($param);

        return $this->appConfigService->getBackendUrl() .
            $link . '?' .
            $query;
    }
}
