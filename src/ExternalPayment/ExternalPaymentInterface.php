<?php

namespace App\ExternalPayment;

use App\Entity\ExternalPayment;
use App\Entity\MobilePayment;
use Symfony\Component\HttpFoundation\Request;

interface ExternalPaymentInterface
{
    public function init(
        MobilePayment $mp,
        float $paymentValue,
        string $transactionDescription = null,
        ?array $config = null
    ): ExternalPayment;

    public function refund(
        ExternalPayment $ep,
        ?float $amount = null
    ): bool;
}
