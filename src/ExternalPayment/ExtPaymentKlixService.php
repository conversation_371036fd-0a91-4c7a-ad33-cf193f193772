<?php

namespace App\ExternalPayment;

use App\Entity\Enum\Issuer;
use App\Entity\Enum\Product;
use App\Entity\Enum\TransactionType;
use App\Entity\ExternalCreditCardToken;
use App\Entity\ExternalPayment;
use App\Entity\MobilePayment;
use App\Entity\User;
use App\ExternalPayment\Exceptions\CreditCardApiException;
use App\Repository\ExternalCreditCardTokenRepository;
use App\Repository\ExternalPaymentRepository;
use App\Repository\LoggerRepository;
use App\Repository\MobilePaymentRepository;
use App\Repository\PaymentGateRepository;
use I2m\Payment\Enum\GateList;
use I2m\Payment\Enum\Status;
use I2m\Payment\Service\Klix\KlixGate;
use Symfony\Component\HttpFoundation\Request;

use function Sentry\captureMessage;

class ExtPaymentKlixService extends AbstractExternalPayment implements ExternalPaymentInterface, ExtPaymentCreditCardInterface
{
    public function __construct(
        private readonly ExternalPaymentRepository $externalPaymentRepository,
        private readonly ExternalCreditCardTokenRepository $cardTokenRepository,
        private MobilePaymentRepository $mobilePaymentRepository,
        private KlixGate $klixGate,
        private PaymentGateRepository $paymentGateRepository,
    ) {
    }

    public function init(
        MobilePayment $mp,
        float $paymentValue,
        string $transactionDescription = null,
        ?array $config = null
    ): ExternalPayment {
        $successUrl = $config['onSuccess'] ?? null;
        $cardToken = $config['token'] ?? null;

        $creditCardToken =
            $this->cardTokenRepository->getCardByToken($mp->getBkfpayUser(), $cardToken)
        ;

        $gate = $this->paymentGateRepository->findOneBy(["type" => GateList::KLIX, 'enable' => true]);
        $externalPayment = $this->externalPaymentRepository->init(
            $mp,
            $mp->getBkfpayUser(),
            $gate->getType()->value,
            $paymentValue
        );
        $externalPayment
            ->setGate($gate)
            ->setDescription($transactionDescription)
            ->setOnSuccess($successUrl)
            ->setEcct($creditCardToken)
        ;

        $this->klixGate->chargeToken(
            $externalPayment,
            $creditCardToken->getCardToken(),
        );

        $externalPayment->setPaymentTerm('P0M');
        $this->externalPaymentRepository->save($externalPayment);

        return $externalPayment;
    }

    public function addInit(User $user, array $config)
    {
        $successUrl = $config['onSuccess'] ?? null;
        $value = 0.1;

        $gate = $this->paymentGateRepository->findOneBy(["type" => GateList::KLIX, 'enable' => true]);
        $mp = $this->mobilePaymentRepository->init(
            Issuer::BKF_PAY,
            TransactionType::TOP_UP,
            $value,
            Product::BKFPAY_TOPUP,
            $user,
        );

        $externalPayment = $this->externalPaymentRepository->init(
            $mp,
            $user,
            $gate->getType()->value,
            $value
        );

        $externalPayment
            ->setGate($gate)
            ->setDescription("add card")
            ->setOnSuccess($successUrl)
        ;

        $this->klixGate->initToken($externalPayment);
        $this->externalPaymentRepository->save($externalPayment);

        if ($externalPayment->getRedirectUrl()) {
            return [
                "type" => "redirect",
                "redirect_url" => $externalPayment->getRedirectUrl(),
            ];
        }

        throw new CreditCardApiException('Card add error');
    }

    public function addRegister(User $user, array $cardData): ?ExternalCreditCardToken
    {
        return null;
    }

    public function addStatus(Request $request)
    {
        // TODO: Implement addStatus() method.
    }
}
