<?php

namespace App\ExternalPayment;

use App\Entity\Enum\MobilePaymentStatus;
use App\Entity\ExternalPayment;
use App\Entity\MobilePayment;
use App\Entity\User;
use App\MobilePayment\MobilePaymentService;
use App\Repository\ExternalCreditCardTokenRepository;
use App\Repository\ExternalPaymentRepository;
use App\Repository\MobilePaymentRepository;
use App\Repository\PaymentGateRepository;
use I2m\Payment\Enum\Action;
use I2m\Payment\Enum\Status;
use I2m\Payment\Model\Callback;
use I2m\Payment\Service\PaymentGateFactory;
use Symfony\Component\HttpFoundation\Request;

use function Sentry\captureException;
use function Sentry\captureMessage;

class ExtPaymentGateService extends AbstractExternalPayment implements ExternalPaymentInterface
{
    public function __construct(
        private readonly ExternalPaymentRepository $externalPaymentRepository,
        private readonly MobilePaymentRepository $mobilePaymentRepository,
        private PaymentGateRepository $paymentGateRepository,
        private readonly PaymentGateFactory $paymentGateFactory,
        private ExternalCreditCardTokenRepository $cardTokenRepository,
        private readonly MobilePaymentService $mobilePaymentService,
    ) {
    }

    public function init(
        MobilePayment $mp,
        float $paymentValue,
        string $transactionDescription = null,
        ?array $config = null
    ): ExternalPayment {
        $successUrl = $config['onSuccess'] ?? null;
        $gate = $this->paymentGateRepository->find($config['token']);
        $externalPayment = $this->externalPaymentRepository->init(
            $mp,
            $mp->getBkfpayUser(),
            $gate->getType()->value,
            $paymentValue
        );

        $externalPayment
            ->setGate($gate)
            ->setDescription($transactionDescription)
            ->setOnSuccess($successUrl)
        ;

        $this->paymentGateFactory->initPayment($externalPayment);
        $externalPayment->setPaymentTerm('P0M');

        $this->externalPaymentRepository->save($externalPayment);

        return $externalPayment;
    }

    public function handleStatus(Request $request): ExternalPayment
    {
        $ep = $this->externalPaymentRepository->find($request->get('id'));
        //$content = $request->getContent();

        //$this->externalPaymentRepository->log($ep, "gate:handleStatus:content", json_decode($content, true));
        //$this->externalPaymentRepository->log($ep, "gate:handleStatus:json", json_decode($request->get('json'), true));

        $callback = $this->paymentGateFactory->callback($ep, $request);
        $this->handleAction($callback, $ep);

        $this->externalPaymentRepository->save($ep);


        return $ep;
    }

    public function refund(
        ExternalPayment $ep,
        ?float $amount = null
    ): bool {
        try {
            $this->paymentGateFactory->refundPayment($ep, $amount);
            $this->externalPaymentRepository->setAdditionalData($ep, ["refunded" => $amount]);
            $this->externalPaymentRepository->save($ep);

            $mp = $ep->getMobilePayment();
            $mp->setStatus(MobilePaymentStatus::REFUNDED);
            $this->mobilePaymentRepository->save($mp);
        } catch (\Exception $e) {
            captureException($e);
            return false;
        }

        return true;
    }

    public function handleAction(Callback $callback, ?ExternalPayment $ep): void
    {
        if (!$callback->getAction()) {
            return;
        }

        switch ($callback->getAction()) {
            case Action::CONFIRM_CARD:
                /** @var User $user */
                $user = $ep->getUser();
                $token = $callback->getAddData()['token'];
                $ecct = $this->cardTokenRepository->getCardByToken($user, $token);
                if (is_null($ecct)) {
                    $ecct = $this->cardTokenRepository->initCreditCardToken($user);
                    $ecct
                        ->setCardBrand("KLIX")
                        ->setExpireShort($callback->getAddData()['expiring'])
                        ->setMaskedCardNumber($callback->getAddData()['numberMasked'])
                        ->setCardToken($callback->getAddData()['token']);
                }
                captureMessage("Dodano kartę do płatności {$ecct->getId()}");
                $this->cardTokenRepository->confirm($ecct);
                break;
            case Action::REFUNDED:
                captureMessage("dokonano zwrotu do płatności {$ep->getId()}");
                $ep->addRefunded($callback->getAddData()['amount']);
                $this->externalPaymentRepository->save($ep);
                break;
            case Action::PAYMENT_CONFIRMED:
                $this->mobilePaymentService->confirm($ep->getMobilePayment());
                break;

            default:
                break;
        }
    }
}
