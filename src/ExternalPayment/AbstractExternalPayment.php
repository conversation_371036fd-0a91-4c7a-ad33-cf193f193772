<?php

namespace App\ExternalPayment;

use App\Entity\ExternalPayment;
use App\Entity\MobilePayment;
use App\Exception\ClientSideException;
use phpDocumentor\Reflection\Types\Boolean;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

abstract class AbstractExternalPayment implements ExternalPaymentInterface
{
    public function refund(
        ExternalPayment $ep,
        ?float $amount = null
    ): bool {
        throw new ClientSideException('Not implemented', Response::HTTP_NOT_IMPLEMENTED);
    }
}
