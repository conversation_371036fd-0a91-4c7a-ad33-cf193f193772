<?php

namespace App\Controller;

use App\Service\AppConfig\AppConfigService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class TermsController extends AbstractController
{
    public function getFile($file)
    {
        $filePath =
            $this->getParameter('kernel.project_dir') .
            "/public/" .
            $this->getParameter('brandingAssets') .
            "/terms/" . $file;

        // Załaduj zawartość pliku
        return file_get_contents($filePath);
    }

    /**
     * Terms and conditions url
     *
     * @Route("/terms/of_use",  methods={"GET"})
     * @Route("/beloyal/regulamin",methods={"GET"}) // deprecated
     */
    public function regulaminBrandingAction(): Response
    {
        return $this->render(
            'terms.html.twig',
            [
                'title' => 'terms_of_use',
                'content' => $this->getFile('of_use.html')
            ]
        );
    }

    /**
     * Privacy policy url
     * @Route("/beloyal/rodo", name="bkfpay_rodo", methods={"GET"}) // deprecated
     * @Route("/terms/rodo", methods={"GET"}) // deprecated
     * @Route("/beloyal/privacy", name="bkfpay_privacy", methods={"GET"}) // deprecated
     * @Route("/terms/privacy", methods={"GET"})
    */
    public function privacyPolicyAction(): Response
    {
        return $this->render(
            'terms.html.twig',
            [
                'title' => 'privacy_policy',
                'content' => $this->getFile('rodo.html')
            ]
        );
    }

    /**
     * Delete account info
     *
     * @Route("/beloyal/delete_account_info", name="delete_account_info", methods={"GET"})
     * @Route("/terms/delete_account_info", methods={"GET"})
   */
    public function deleteAccountInfo(): Response
    {
        return $this->render(
            'terms.html.twig',
            [
                'title' => 'terms.delete-account',
                'content' => $this->getFile('delete-account.html')
            ]
        );
    }

    /**
     * @Route("/terms/issuer", methods={"GET"})
     */
    public function appOwner(AppConfigService $appConfigService): JsonResponse
    {
        return $this->json(
            data: $appConfigService->getAppIssuer(),
            context: [
                'groups' => [
                    'default:basic',
                    'company:data',
                ]
            ],
        );
    }
}
