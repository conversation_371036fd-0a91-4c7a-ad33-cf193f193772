<?php

namespace App\Controller;

use App\Entity\Receipt;
use App\Repository\MobilePaymentRepository;
use App\Repository\ReceiptRepository;
use App\Service\AppConfig\AppConfigService;
use App\Service\SalesDocument\Invoices\InvoiceSender;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use Sentry\State\Scope;
use Symfony\Bridge\Twig\Mime\TemplatedEmail;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Mailer\MailerInterface;
use Symfony\Component\Mime\Address;
use Symfony\Component\Routing\Annotation\Route;

use function Sentry\captureException;
use function Sentry\captureMessage;
use function Sentry\withScope;

/**
 * @Route("/api/v3/receipt")
 */
class ReceiptController extends AbstractController
{
    private const API_TOKEN = '8TQN3Q3BYJ66TNXOWK6GZZ52MB80IXMO';

    /**
     * @Route("/receive", name="receipt_recive_action", methods={"POST"})
     */
    public function receiveAction(
        Request $request,
        ReceiptRepository $receiptRepository,
        MobilePaymentRepository $mobilePaymentRepository,
        InvoiceSender $sender
    ): JsonResponse {
        try {
            if ($request->headers->get('x-api-key') !== self::API_TOKEN) {
                captureMessage("Drukarka fiskalna wysłała paragon z błędnym kluczem");
                return $this->json(['Unauthorized'], 401);
            }

            $content = $request->getContent();
            $inputData = json_decode($content, true);

            if ($inputData === null || !array_key_exists('eparagon', $inputData)) {
                throw new Exception('No recepits in request');
            }

            foreach ($inputData['eparagon'] as $eParagon) {
                $mobilePaymentId = $eParagon['idTransakcji'][0] ?? null;
                $mp = $mobilePaymentRepository->find($mobilePaymentId);
                $receipt = $receiptRepository->buildReceiptEntity($mp, $eParagon);
                if ($receipt) {
                    $sender->sendReceiptWithEmail($receipt);
                }
            }
        } catch (Exception $e) {
            captureException($e);
            return $this->json([$e->getMessage()], 400);
        }

        return $this->json("OK", 202);
    }
}
