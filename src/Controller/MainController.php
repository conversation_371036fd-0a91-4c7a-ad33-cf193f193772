<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class MainController extends AbstractController
{
    /**
     * @Route("/", name="main")
     */
    public function index(): Response
    {
        // Do not redirect in development
        if ($this->getParameter('kernel.environment') === 'dev') {
            return new Response('<body></body>');
        }

        return new RedirectResponse($this->getParameter('front_url'), 301);
    }
}
