<?php

namespace App\Controller\Security;

use App\Entity\Token;
use App\Entity\User;
use App\Security\ApiTokenGenerator;
use App\Security\LegacyUserSecurityService;
use DateInterval;
use DateTimeImmutable;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Core\Authentication\Token\Storage\UsageTrackingTokenStorage;
use Throwable;

class SecurityController extends AbstractController
{
    private ApiTokenGenerator $tokenGenerator;

    private EntityManagerInterface $entityManager;

    private User $user;

    public function __construct(
        ApiTokenGenerator $tokenGenerator,
        EntityManagerInterface $entityManager
    ) {
        $this->tokenGenerator = $tokenGenerator;
        $this->entityManager = $entityManager;
    }

    /**
     * @Route("/api/v3/user/login", name="new_user_login", methods={"POST", "OPTIONS"})
     */
    public function loginNewApp(
        Request $request,
        LegacyUserSecurityService $securityService,
    ): Response {
        try {
            $this->user = $securityService->validateUserLogin($request);
        } catch (Throwable $exception) {
            return $this->json(null, 401);
        }

        $token = $this->generateNewToken();

        $response = [
            'status'                  => true,
            'token'                   => $token,
            'tokenV2'                 => $token,
            'userId'                  => $this->user->getId(),
            'isFleetManager'          => $this->user->isFleetManager(),
            'isTrustedPartner'        => $this->user->isTrustedPartner(),
            'isUsingFleetApplication' => $this->user->isFleetManager(), // depracated
            'terms'                   => $this->user->getTermsOfUseAccepted()
        ];

        return $this->json($response);
    }

    /**
     * @Route("/api/v3/user", name="new_user_info", methods={"GET", "OPTIONS"}) - api depricated
     */
    public function userNewApp(): Response
    {
        /** @var User $userInfo */
        $userInfo = $this->getUser();

        $response = [
            'id'               => $userInfo->getId(),
            'name'             => $userInfo->getUserIdentifier(),
            'terms'            => $userInfo->getTermsOfUseAccepted(),
            'currency'         => $userInfo->getCurrency()->getSymbol(),
            'currencyCode'     => $userInfo->getCurrency()->getCode(),
            'isFleetManager'   => $userInfo->isFleetManager() === true,
            'isTrustedPartner' => $userInfo->isTrustedPartner() === true,
        ];

        return $this->json($response);
    }

    /**
     * @Route("/api/v3/user/logout", name="new_user_logout", methods={"GET"})
     */
    public function logoutNewApp(Request $request, UsageTrackingTokenStorage $tokenStorage)
    {
        // this is never called see /src/Security/LogoutListener
    }

    /**
     * @throws Exception
     */
    private function generateNewToken(): string
    {
        $token = $this->tokenGenerator->generate();
        $this->saveTokenToDatabase($token);

        return $token;
    }

    private function saveTokenToDatabase(string $tokenString): void
    {
        $expiresAt = (new DateTimeImmutable())->add(new DateInterval('P1Y'));

        $token = (new Token())->setToken($tokenString)->setUser($this->user)->setExpiresAt($expiresAt);

        $this->entityManager->persist($token);
        $this->entityManager->flush();
    }
}
