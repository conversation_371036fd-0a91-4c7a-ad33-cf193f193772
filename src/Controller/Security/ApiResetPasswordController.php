<?php

namespace App\Controller\Security;

use App\Entity\User;
use App\Service\AppConfig\AppConfigService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bridge\Twig\Mime\TemplatedEmail;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Mailer\Exception\TransportExceptionInterface;
use Symfony\Component\Mailer\MailerInterface;
use Symfony\Component\Mime\Address;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Contracts\Translation\TranslatorInterface;
use SymfonyCasts\Bundle\ResetPassword\Controller\ResetPasswordControllerTrait;
use SymfonyCasts\Bundle\ResetPassword\Exception\ResetPasswordExceptionInterface;
use SymfonyCasts\Bundle\ResetPassword\ResetPasswordHelperInterface;

/**
 * @Route("/api/users")
 */
class ApiResetPasswordController extends AbstractController
{
    use ResetPasswordControllerTrait;




    public function __construct(
        private ResetPasswordHelperInterface $resetPasswordHelper,
        private EntityManagerInterface $em,
        private TranslatorInterface $translator,
        private ParameterBagInterface $parameterBag,
        private AppConfigService $appConfigService
    ) {
    }

    /**
     * Display & process form to request a password reset.
     *
     * @Route("/resets", name="api_forgot_password_request")
     * @throws TransportExceptionInterface
     */
    public function request(
        Request $request,
        MailerInterface $mailer,
        ParameterBagInterface $parameterBag
    ): Response {
        if (empty($request->getContent())) {
            return $this->json(['error' => 'no data'], 400);
        }

        $requestArray = $request->toArray();

        if (empty($requestArray['email'])) {
            return $this->json(['error' => 'no email'], 400);
        }

        return $this->processSendingPasswordResetEmail(
            $requestArray['email'],
            $mailer,
            $parameterBag->get('front_url')
        );
    }

    /**
     * @throws TransportExceptionInterface
     */
    private function processSendingPasswordResetEmail(
        string $emailFormData,
        MailerInterface $mailer,
        string $frontUrl
    ): JsonResponse {
        $user = $this->em
            ->getRepository(User::class)
            ->findOneBy(['email' => $emailFormData]);

        // Do not reveal whether a user account was found or not.
        if (!$user) {
            return $this->json([]);
        }

        /** @phpstan-ignore-next-line */
        $this->translator->setLocale($user->getLocale());

        try {
            $resetToken = $this->resetPasswordHelper->generateResetToken($user);
        } catch (ResetPasswordExceptionInterface $e) {
            return $this->json(['error' => $e->getMessage()], 400);
        }

        $email = (new TemplatedEmail())
            ->from(new Address(
                $this->parameterBag->get('mailer_address_no_replay'),
                $this->appConfigService->getAppName()
            ))
            ->to(new Address($user->getEmail()))
            ->subject($this->translator->trans('email.password-reset.request'))
            ->htmlTemplate('reset_password/email.html.twig')
            ->context([
                          'user' => $user,
                          'resetToken' => $resetToken,
                          'frontUrl' => $frontUrl
                      ]);

        $mailer->send($email);

        return $this->json([]);
    }
}
