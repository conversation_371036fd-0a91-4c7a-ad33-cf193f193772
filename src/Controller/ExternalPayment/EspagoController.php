<?php

namespace App\Controller\ExternalPayment;

use App\Entity\ExternalPayment;
use App\Entity\User;
use App\ExternalPayment\ExtPaymentEspagoService;
use App\MobilePayment\MobilePaymentService;
use App\Repository\LoggerRepository;
use App\Repository\MobilePaymentRepository;
use Doctrine\ORM\EntityManagerInterface;
use I2m\Payment\Enum\Status;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

use function Sentry\captureException;
use function Sentry\captureMessage;

class EspagoController extends AbstractController
{
    #[Route('/external_payment/espago/success', name: 'app_external_payment_espago_success', methods: ['GET'])]
    public function success(): Response
    {
        return $this->redirect('beloyal://beloyal24.com/payment/success'); // success
    }

    #[Route('/external_payment/espago/error', name: 'app_external_payment_espago_error', methods: ['GET'])]
    public function error(): Response
    {

        return $this->redirect('beloyal://beloyal24.com/payment/error'); // success
    }

    #[Route(
        '/external_payment/espago/back_request',
        name: 'app_external_payment_espago_back_request',
        methods: ['POST']
    )]
    public function backRequest(
        Request $request,
        ExtPaymentEspagoService $extPaymentEspagoService,
        MobilePaymentService $mobilePaymentService,
        LoggerRepository $logger,
        EntityManagerInterface $em,
    ): Response {
        if ($em->getFilters()->isEnabled('status_filter')) {
            $em->getFilters()->disable('status_filter');
        }
        /** @var User $user */
        $user = $this->getUser();
        $logger->log($user, "EXTERNAL_PAYMENT", "ESPAGO", $request->getContent());

        try {
            $ep = $extPaymentEspagoService->handleStatus($request);
            match ($ep->getStatus()) {
                Status::CONFIRMED => $mobilePaymentService->confirm($ep->getMobilePayment()),
                Status::REJECTED => $mobilePaymentService->reject($ep->getMobilePayment()),
                Status::REFUNDED => $mobilePaymentService->refund($ep->getMobilePayment()),
                default => captureMessage("nieobsłużony status płatności {$ep->getStatus()->value}")
            };

            return $this->json([]);
        } catch (\Exception $e) {
            captureException($e);
            $exceptionMessage = explode(';', $e->getMessage());
            $message['status'] = $exceptionMessage[0];
            if (isset($exceptionMessage[1]) === true) {
                $message['message'] = $e->getMessage();
            }
            return $this->json($message);
        }
    }
}
