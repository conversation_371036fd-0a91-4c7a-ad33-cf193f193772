<?php

namespace App\Controller\ExternalPayment;

use App\Entity\ExternalPayment;
use App\Entity\User;
use App\ExternalPayment\ExtPaymentGateService;
use App\MobilePayment\MobilePaymentService;
use App\Repository\LoggerRepository;
use Detection\MobileDetect;
use I2m\Payment\Enum\Status;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

use function Sentry\captureException;

class PaymentGateController extends AbstractController
{
    #[Route('/external_payment/gate/callback')]
    public function callback(
        Request $request,
        ExtPaymentGateService $paymentGateService,
        MobilePaymentService $mobilePaymentService,
        LoggerRepository $logger
    ): Response {
        /** @var User $user */
        $user = $this->getUser();
        $logger->log($user, "EXTERNAL_PAYMENT", "GATE", $request->getContent());
        $logger->log($user, "EXTERNAL_PAYMENT", "GATE", json_encode($request->request->all()));
        try {
            $ep = $paymentGateService->handleStatus($request);


            return $this->json(['status' => 'success']);
        } catch (\Exception $e) {
            captureException($e);
            return $this->json(['status' => 'error']);
        }
    }

    #[Route('/external_payment/gate/success')]
    #[Route("/external_payment/gate/{id}/success")]
    public function success(ParameterBagInterface $parameterBag, ?ExternalPayment $ep)
    {

        $detector = new MobileDetect();
        if ($detector->isMobile()) {
            return $this->redirect(
                $ep?->getOnSuccess() ?? 'beloyal://beloyal24.com/payment/success'
            );
        }
        $id = $ep?->getId() ?? 0;
        return $this->redirect($parameterBag->get('front_url') . "/#/top-up/$id");
    }

    #[Route('/external_payment/gate/error')]
    #[Route("/external_payment/gate/{id}/error")]
    public function error(ParameterBagInterface $parameterBag, ?ExternalPayment $ep)
    {
        $detector = new MobileDetect();
        if ($detector->isMobile()) {
            return $this->redirect('beloyal://beloyal24.com/payment/error');
        }
        $id = $ep?->getId() ?? 0;
        return $this->redirect($parameterBag->get('front_url') . "/#/top-up/$id");
    }
}
