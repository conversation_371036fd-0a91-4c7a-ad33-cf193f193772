<?php

namespace App\Controller\ExternalPayment;

use App\Entity\ExternalCreditCardToken;
use App\Entity\User;
use App\ExternalPayment\ExtPaymentPayExService;
use App\MobilePayment\MobilePaymentManager;
use App\MobilePayment\MobilePaymentService;
use App\Repository\ExternalCreditCardTokenRepository;
use App\Repository\LoggerRepository;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Exception;

use function Sentry\captureException;

class PayExController extends AbstractController
{
    #[Route('/external_payment/pay-ex/confirm-by-link', name: 'app_external_payment_payex_confirm_by_link')]
    public function confirmByLink(
        Request $request,
        LoggerRepository $logger,
        ExtPaymentPayExService $extPaymentPayExService,
        MobilePaymentService $mobilePaymentService
    ): JsonResponse {
        /** @var User $user */
        $user = $this->getUser();
        $logger->log(
            $user,
            "EXTERNAL_PAYMENT",
            "PAYEX/confirm-by-link2",
            $request->getUri() . " : " .
                     $request->getMethod() . " : " .
            $request->getContent()
        );

        try {
            $ep = $extPaymentPayExService->handleStatus($request);
            if ($ep) {
                $mobilePaymentService->confirm($ep->getMobilePayment());
            }
            return $this->json([]);
        } catch (\Exception $e) {
            captureException($e);
            $exceptionMessage = explode(';', $e->getMessage());
            $message['status'] = $exceptionMessage[0];
            if (isset($exceptionMessage[1]) === true) {
                $message['message'] = $e->getMessage();
            }
            return $this->json($message);
        }
    }

    #[Route('/external_payment/pay-ex/success', name: 'app_external_payment_payex_success')]
    public function success(
        Request $request,
        LoggerRepository $logger
    ): JsonResponse {
        /** @var User $user */
        $user = $this->getUser();
        $logger->log(
            $user,
            "EXTERNAL_PAYMENT",
            "PAYEX/success",
            $request->getUri() . " : " .
                     $request->getMethod() . " : " .
            $request->getContent()
        );
        return $this->json([]);
    }
    #[Route("/external_payment/pay-ex/card")]
    public function card(
        Request $request,
        ExtPaymentPayExService $extPaymentPayExService,
        LoggerRepository $loggerRepository
    ): JsonResponse {
        try {
            $loggerRepository->log(
                null,
                "/external_payment/pay-ex/card",
                null,
                $request->getUri() . " " . $request->getContent()
            );
            $data = $extPaymentPayExService->addStatus($request);
            $loggerRepository->log(
                null,
                "/external_payment/pay-ex/card",
                null,
                json_encode($data)
            );
        } catch (Exception $e) {
            captureException($e);
            return $this->json(['error' => $e->getMessage()]);
        }
        return $this->json($data);
    }
}
