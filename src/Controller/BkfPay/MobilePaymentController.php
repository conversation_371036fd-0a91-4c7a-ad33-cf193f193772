<?php

namespace App\Controller\BkfPay;

use App\Repository\LoggerRepository;
use App\Service\BkfPay\MobilePaymentService;
use App\Service\BkfPay\VerifyMobilePaymentRequest;
use Exception;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

use function Sentry\captureException;

class MobilePaymentController extends AbstractController
{
    public function __construct(private LoggerRepository $loggerRepository)
    {
    }

    #[Route("/bkfpay/mobilepayment", methods: ["GET", "OPTIONS"])]
    public function wrongMethod(
        Request $request,
        LoggerRepository $loggerRepository
    ): JsonResponse {
        $loggerRepository->log(
            null,
            "MOBILE_PAYMENT2",
            'wrong_method',
            "{$request->getMethod()} request: " . $request->getContent()
        );
        return $this->json(
            [
                'status' => 'invalidMethod',
                "message" => "Only POST is allowed, for api testing use methodId: checkAlive"
            ],
            JsonResponse::HTTP_METHOD_NOT_ALLOWED
        );
    }

    #[Route("/bkfpay/mobilepayment", methods: ["POST"])]
    #[Route("/bkfpay/mobilepayment/", methods: ["POST"])]
    public function main(
        Request $request,
        MobilePaymentService $mobilePaymentService,
        VerifyMobilePaymentRequest $verifyMobilePaymentRequest
    ): JsonResponse {
        $message = [];

        try {
            $inputData = $verifyMobilePaymentRequest->verifyRequest($request);

            $methodId = $inputData['methodId'];
            $this->loggerRepository->log(
                $inputData['user'],
                "MOBILE_PAYMENT",
                $methodId,
                "{$request->getMethod()} request: " . $request->getContent()
            );

            switch ($methodId) {
                case 'checkAvailability':
                    $message = $mobilePaymentService->checkAvailability(
                        standId: $inputData['standId'],
                    )['data'];
                    break;
                case 'initPayment':
                    $message = $mobilePaymentService->initPayment(
                        user: $inputData['user'],
                        standId: $inputData['standId'],
                    );
                    break;
                case 'confirmPayment':
                    $message = $mobilePaymentService->confirmPayment(
                        user:      $inputData['user'],
                        paymentId: $inputData['paymentId'],
                        data:     $inputData,
                    );
                    break;
                case 'availableCarwashes':
                    $message = $mobilePaymentService->availableCarwashes();
                    break;
                case 'checkAlive':
                    $message = $mobilePaymentService->checkAlive();
                    break;

                case 'washingState':
                    $message = $mobilePaymentService->washingState(
                        user: $inputData['user'],
                        paymentId: $inputData['paymentId']
                    );
                    break;

                case 'washingStop':
                    $message = $mobilePaymentService->washingStop(
                        user: $inputData['user'],
                        paymentId: $inputData['paymentId']
                    );
                    break;

                case 'washingResume':
                    $message = $mobilePaymentService->washingResume(
                        user: $inputData['user'],
                        paymentId: $inputData['paymentId']
                    );
                    break;

                default:
                    $message['status'] = 'invalidFormat';
                    $message['message'] = 'Unknow method Id';
            }
            $this->loggerRepository->log(
                $inputData['user'],
                "MOBILE_PAYMENT2",
                $methodId,
                "{$request->getMethod()} response: " . json_encode($message)
            );
        } catch (Exception $e) {
            captureException($e);
            $message = $this->errorMassage($e, $request);
        }

        return $this->json($message);
    }

    public function errorMassage(\Exception $e, Request $request)
    {
        $exceptionMessage = explode(';', $e->getMessage());
        $message['status'] = $exceptionMessage[0];
        if (isset($exceptionMessage[1])) {
            $message['message'] = $exceptionMessage[1];
        }
        $this->loggerRepository->log(
            null,
            "MOBILE_PAYMENT",
            "exception",
            "{$request->getMethod()} request: " . $request->getContent()
        );
        $this->loggerRepository->log(
            null,
            "MOBILE_PAYMENT",
            "exception",
            "{$request->getMethod()} response: " . json_encode($message)
        );
        return $message;
    }
}
