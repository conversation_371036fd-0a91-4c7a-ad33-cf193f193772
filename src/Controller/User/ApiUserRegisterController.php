<?php

namespace App\Controller\User;

use App\Entity\User;
use App\Model\Auth\ResetPasswordDTO;
use App\User\Registration\UserRegisterManager;
use Doctrine\DBAL\Exception\UniqueConstraintViolationException;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Serializer\SerializerInterface;
use SymfonyCasts\Bundle\ResetPassword\Exception\ResetPasswordExceptionInterface;
use SymfonyCasts\Bundle\ResetPassword\ResetPasswordHelperInterface;
use Throwable;

use function Sentry\captureException;

class ApiUserRegisterController extends AbstractController
{
    /**
     * @Route("/api/users/registers", name="api_user_register")
     */
    public function register(Request $request, UserRegisterManager $userRegisterManager): Response
    {
        if (!$userRegisterManager->isApiRegisterRequestValid($request)) {
            return $this->json(['status' => false, 'message' => "No email"], Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        try {
            $userRegisterManager->registerApiUser();
        } catch (Throwable $exception) {
            captureException($exception);
            if ($exception instanceof UniqueConstraintViolationException) {
                return $this->json(
                    [
                        'status'  => false,
                        'message' => "Twoje konto już istnieje w systemie",
                    ],
                    Response::HTTP_CONFLICT
                );
            }
            return $this->json(
                [
                    'status'  => false,
                    'message' => $exception->getMessage(),
                ],
                Response::HTTP_UNPROCESSABLE_ENTITY
            );
        }

        return $this->json(['status' => true]);
    }

    /**
     * Validates and process the reset URL that the user clicked in their email.
     *
     * @Route("/api/users/new-password", name="app_set_new_password", methods={"POST", "OPTIONS"})
     */
    public function setNewPassword(
        EntityManagerInterface $em,
        ResetPasswordHelperInterface $resetPasswordHelper,
        Request $request,
        SerializerInterface $serializer,
        UserPasswordHasherInterface $passwordEncoder
    ): Response {
        /** @var ResetPasswordDTO $paswordReset */
        $paswordReset = $serializer
            ->deserialize(
                $request->getContent(),
                ResetPasswordDTO::class,
                'json'
            );

        $token = $paswordReset->getToken();
        $plainNewPassword = $paswordReset->getPlainPassword();

        if (null === $token) {
            throw $this->createNotFoundException('No reset password token found in the URL or in the session.');
        }

        try {
            /** @var User $user */
            $user = $resetPasswordHelper->validateTokenAndFetchUser($token);
        } catch (ResetPasswordExceptionInterface $e) {
            return $this->json(
                [
                    'message' => 'There was a problem validating your reset request ' . $e->getReason(),
                ],
                400
            );
        }

        if (!empty($plainNewPassword)) {
            // A password reset token should be used only once, remove it.
            $resetPasswordHelper->removeResetRequest($token);

            // Encode the plain password, and set it.
            $encodedPassword = $passwordEncoder->hashPassword(
                $user,
                $plainNewPassword
            );

            $user->setPassword($encodedPassword);
            $em->flush();

            return $this->json(
                ['message' => 'success'],
            );
        }

        return $this->json(
            ['message' => 'Password invalid'],
            400
        );
    }

    /**
     * @Route("/api/users/accept_terms_of_use", name="api_user_accept_terms_of_use", methods={"POST", "OPTIONS"}) - działa w apce mobilnej od wersji 3.8.12
     * @Route("/api/user/accept_terms_of_use", name="api_user_accept_terms_of_use_deprecated", methods={"POST", "OPTIONS"}) - deprecated
     */
    public function postAcceptTermsOfUseAction(EntityManagerInterface $em): JsonResponse
    {
        /** @var User $user */
        $user = $this->getUser();
        // accept terms of use
        $user->setTermsOfUseAccepted(true);
        $em->flush();

        return $this->json([
            'status' => true,
        ]);
    }
}
