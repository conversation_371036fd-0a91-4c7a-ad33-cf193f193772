<?php

namespace App\Controller\User;

use App\ApiConnector\CarwashApiConnector;
use App\Entity\User;
use OpenApi\Annotations as OA;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Annotation\Route;

class VirtualCardsListController extends AbstractController
{
    /**
     * @Route("/api/users/virtual_cards", name="list_user_virtual_cards2", methods={"GET"})
     *
     * @OA\Response(
     *     response="200",
     *     description="Returns all list of virtual cards assignet for be loyal user email",
     *     content={
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 example={
     *                          {
     *                              "id":123,
     *                              "number":"VRR5KDZR",
     *                              "alias":"Test",
     *                              "balance":0,
     *                              "status":false,
     *                              "enabled":true,
     *                              "ctime":"2023-03-07T13:34:46+00:00",
     *                              "mtime":"2023-03-07T14:25:44+00:00",
     *                              "lastContact":null,
     *                              "value":0,
     *                              "client":117381,
     *                              "owner":{
     *                                  "name":"Test Owner CM: <EMAIL>"
     *                               },
     *                              "additInfo":null,
     *                              "isVirtual":true,
     *                              "email":"<EMAIL>"
     *                          }
     *                      }
     *             )
     *         )
     *     }
     * )
     * @OA\Response(response="401",description="Unauthorized")
     *
     * @OA\Tag(name="Virtual Cards")
     */
    public function getAllVirtualCards(): JsonResponse
    {
        // karty wirtualne są wyłączone w BL
        return new JsonResponse(
            [],
            200,
            []
        );
    }

    /**
     * @Route(
     *      "/api/users/virtual_cards/for_stand/{standCode}",
     *      name="list_user_virtual_cards_for_stand2",
     *      methods={"GET"}
     *  )
     *
     * @OA\Response(
     *     response="200",
     *     description="Returns list of virtual cards for carwashsatnd assignet for be loyal logged user",
     *     content={
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 example={
     *                          {
     *                              "id":123,
     *                              "number":"VRR5KDZR",
     *                              "alias":"Test",
     *                              "balance":0,
     *                              "status":false,
     *                              "enabled":true,
     *                              "ctime":"2023-03-07T13:34:46+00:00",
     *                              "value":0,
     *                              "client":117381,
     *                              "owner":{
     *                                  "name":"Test Owner CM: <EMAIL>"
     *                               },
     *                              "additInfo":null,
     *                              "isVirtual":true,
     *                              "email":"<EMAIL>"
     *                          }
     *                      }
     *             )
     *         )
     *     }
     * )
     * @OA\Response(response="401",description="Unauthorized")
     *
     * @OA\Tag(name="Virtual Cards")
     */
    public function getVirtualCardsForStand(): JsonResponse
    {
        return new JsonResponse(
            [],
            200,
            []
        );
    }
}
