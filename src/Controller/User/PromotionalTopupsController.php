<?php

namespace App\Controller\User;

use App\Entity\User;
use App\TopUps\TopUpManager;
use Exception;
use OpenApi\Annotations as OA;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Contracts\Translation\TranslatorInterface;

class PromotionalTopupsController extends AbstractController
{
    /**
     * @Route("/api/users/topups/promotional", name="promotional_topups2", methods={"POST"})
     * @OA\RequestBody(
     *      description= "Allows topup user account by promotional code",
     *      required= true,
     *      content={
     *             @OA\MediaType(
     *                 mediaType="application/json",
     *                 @OA\Schema(
     *                     example={
     *                          "code": "1111-111",
     *                      }
     *                  )
     *              )
     *       }
     * )
     *
     * @OA\Response(
     *      response="200",
     *      description="ok",
     *         content={
     *             @OA\MediaType(
     *                 mediaType="application/json",
     *                 @OA\Schema(
     *                     example={}
     *                 )
     *          )
     *       }
     *   )
     *
     * @OA\Tag(name="PromotionalCodes")
     */
    public function checkPromotionalTopup(
        Request $request,
        TopUpManager $topupManager,
        TranslatorInterface $translator
    ): JsonResponse {
        try {
            /** @var User $user */
            $user = $this->getUser();
            $requestArray = $request->request->all();
            if (empty($requestArray)) {
                $requestArray = $request->toArray();
            }

            $code = strtoupper($requestArray['code']);

            $promotionalCode = $topupManager->findPromotionalCodes($code);
            $topupManager->applyCode($promotionalCode, $user);

            $data = [
                'status'  => true,
                'message' => $translator->trans('promotion.topup-success'),
            ];
        } catch (Exception $e) {
            $error = [
                'error'             => $e->getMessage(),
                'error_description' => $e->getMessage(),
            ];

            return $this->json($error, 400);
        }

        return $this->json($data);
    }
}
