<?php

namespace App\Controller\User;

use App\Entity\User;
use App\Service\Mobile\MobileNotificationService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

class UserMobileDevicesController extends AbstractController
{
    #[Route(path: '/api/users/mobile_devices', name: 'update_user_mobile_token', methods: ['PATCH', 'OPTIONS'])]
    public function patchUserMobileToken(Request $request, MobileNotificationService $notificationService): JsonResponse
    {
        /** @var User $user */
        $user = $this->getUser();
        $notificationService->storeUserMobileToken(
            $user,
            $request->get('token'),
            $request->get('device_info', '')
        );
        return $this->json([]);
    }

    #[Route(path: '/api/users/mobile_devices', name: 'remove_user_mobile_token', methods: ['DELETE', 'OPTIONS'])]
    public function removeUserMobileToken(Request $request, MobileNotificationService $notificationService): JsonResponse
    {
        /** @var User $user */
        $user = $this->getUser();
        $notificationService->removeUserMobileToken(
            $user,
            $request->get('token'),
        );
        return $this->json([]);
    }
}
