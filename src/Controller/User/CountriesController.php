<?php

namespace App\Controller\User;

use App\Entity\Country;
use App\Repository\CountryRepository;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Contracts\Translation\TranslatorInterface;
use Symfony\Component\HttpFoundation\Response;

class CountriesController extends AbstractController
{
    /**
     * @Route(
     *      "/api/users/countries",
     *      name="api_user_settings_countries2",
     *      methods={"GET"}
     *  )
     */
    public function getSettingsCountriesAction(
        CountryRepository $countryRepository,
    ) {
        $countries = $countryRepository->findAll();
        return $this->json($countries, Response::HTTP_OK, [], ['groups' => "default:basic"]);
    }
}
