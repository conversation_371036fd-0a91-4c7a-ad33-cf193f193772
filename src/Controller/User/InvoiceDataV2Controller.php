<?php

namespace App\Controller\User;

use App\ApiConnector\Exceptions\TaxPayerInfoServiceInternalErrorException;
use App\ApiConnector\TaxPayerConnector;
use App\Entity\Client;
use App\Entity\User;
use App\Repository\LoggerRepository;
use App\Service\AppConfig\AppConfigService;
use Doctrine\ORM\EntityManagerInterface;
use I2m\Invoices\Service\CompanyData\CompanyDataProvider;
use I2m\StandardTypes\Enum\Country;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Serializer\SerializerInterface;

use function Sentry\captureMessage;

class InvoiceDataV2Controller extends AbstractController
{
    #[Route("/api/users/invoice_data", methods: ["GET"])]
    public function getUserInvoiceData(
        ParameterBagInterface $parameterBag
    ): JsonResponse {


        /** @var User $user */
        $user = $this->getUser();
        $client = $user->getClient();

        if ($client === null) {
            $countryCode = Country::from($parameterBag->get('branding_country_code'));

            $client = (new Client())
                ->setEmail($user->getEmail())
                ->setCountry($countryCode)
            ;
        }
        return $this->json($client, JsonResponse::HTTP_OK, [], ['groups' => ["client:data", "company:data", "default:basic"]]);
    }

    #[Route("/api/users/invoice_data", methods: ["POST"])]
    public function postUserInvoiceData(
        Request $request,
        SerializerInterface $serializer,
        EntityManagerInterface $em,
        LoggerRepository $logger,
    ): JsonResponse {

        /** @var User $user */
        $user = $this->getUser();
        $logger->log($user, "INVOICEDATA", "save", $request->getContent());

        // jeśli to użytkownik floty wtedy nie może zmienić danych do faktury
        if ($user->getManager()) {
            $logger->log($user, "INVOICEDATA", "forbiden", $request->getContent());
            return $this->json($user->getClient(), JsonResponse::HTTP_FORBIDDEN, [], ['groups' => ["client:data", "company:data", "default:basic"]]);
        }

        $client = $user->getClient();

        if (is_null($client)) {
            $client = (new Client());
            $user->setClient($client);
            $em->persist($client);
        }

        $serializer->deserialize($request->getContent(), Client::class, 'json', ['object_to_populate' => $client]);

        $em->flush();

        return $this->json($client, JsonResponse::HTTP_OK, [], ['groups' => ["client:data", "company:data", "default:basic"]]);
    }

    #[Route("/api/users/invoice_data/find/{taxNumber}", methods: ["GET"])]
    public function getByTaxNumber(
        Request $request,
        CompanyDataProvider $companyDataProvider,
        AppConfigService $appConfigService,
        string $taxNumber,
    ): JsonResponse {
        /** @var User $user */
        $user = $this->getUser();

        $country =
            Country::tryFrom($request->get('country')) ??
            $user->getClient()->getCountry() ??
            $appConfigService->getAppIssuer()->getCountry();

        $client =
            ($user->getClient() ?? new Client())
                ->setCountry($country)
                ->setTaxNumber($taxNumber);


        $companyDataProvider->updateData($country, $taxNumber, $client);

        return $this->json($client, JsonResponse::HTTP_OK, [], ['groups' => ["client:data", "company:data", "default:basic"]]);
    }
}
