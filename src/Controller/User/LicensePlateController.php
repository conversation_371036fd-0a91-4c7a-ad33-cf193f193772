<?php

namespace App\Controller\User;

use App\Entity\LicensePlate;
use App\Entity\User;
use App\Repository\LicensePlateRepository;
use Doctrine\ORM\EntityManagerInterface;
use OpenApi\Annotations as OA;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Serializer\Normalizer\AbstractNormalizer;
use Symfony\Component\Serializer\SerializerInterface;

class LicensePlateController extends AbstractController
{
    /**
     * @Route("/api/users/license_plates/list", name="license_plate_list2", methods={"GET"})
     *
     * @OA\Response(
     *     response="200",
     *     description="On success",
     *     content={
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 example={
     *                 }
     *             )
     *         )
     *     }
     * )
     *
     * @OA\Response(
     *     response="401",
     *     description="Unauthorized",
     * )
     *
     * @OA\Tag(name="LicensePlates")
     */
    public function listAction(LicensePlateRepository $repository): Response
    {
        /** @var User $user */
        $user = $this->getUser();
        $licensePlates = $repository->findBy(['user' => [$user, $user->getManager()]]);
        return $this->json($licensePlates, Response::HTTP_OK, [], ['groups' => 'plate_list']);
    }

    /**
     * @Route("/api/users/license_plates", name="license_plate_add2", methods={"POST"})
     *
     * @OA\RequestBody(
     *      description= "Provide payment parameters",
     *      required= true,
     *      content={
     *             @OA\MediaType(
     *                 mediaType="application/json",
     *                 @OA\Schema(
     *                     example={
     *                         {
     *                           "id": 1,
     *                           "number": "ZS 123123",
     *                           "name": "Fleet car van",
     *                         }
     *                     }
     *                 )
     *              )
     *       }
     * ),
     *
     * @OA\Response(
     *     response="200",
     *     description="On success",
     *     content={
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 example={
     *                 }
     *             )
     *         )
     *     }
     * )
     *
     * @OA\Response(
     *     response="401",
     *     description="Unauthorized",
     * )
     *
     * @OA\Tag(name="LicensePlates")
     */
    public function addAction(
        Request $request,
        SerializerInterface $serializer,
        EntityManagerInterface $em
    ): Response {
        /** @var User $user */
        $user = $this->getUser();

        /** @var LicensePlate $licensePlate */
        $licensePlate = $serializer->deserialize(
            $request->getContent(),
            LicensePlate::class,
            'json',
        );

        $em->persist($licensePlate);
        $user->addLicensePLate($licensePlate);
        $em->persist($user);
        $em->flush();

        return $this->json($licensePlate, Response::HTTP_OK, [], ['groups' => 'plate_list']);
    }

    /**
     * @Route("/api/users/license_plates/{id}", name="license_plate_edit2", methods={"PATCH"})
     *
     * @OA\RequestBody(
     *      description= "Provide payment parameters",
     *      required= true,
     *      content={
     *             @OA\MediaType(
     *                 mediaType="application/json",
     *                 @OA\Schema(
     *                     example={
     *                          "number": "ZS 123123",
     *                          "name": "Fleet car van",
     *                      }
     *                  )
     *              )
     *       }
     * ),
     *
     * @OA\Parameter(
     *     name="id",
     *     in="path",
     *     required=true,
     *     description="license plaete id",
     *     @OA\Schema(type="integer", example=1)
     * )
     *
     * @OA\Response(
     *     response="200",
     *     description="On success",
     *     content={
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 example={
     *                 }
     *             )
     *         )
     *     }
     * )
     *
     * @OA\Response(
     *     response="401",
     *     description="Unauthorized",
     * )
     *
     * @OA\Tag(name="LicensePlates")
     */
    public function editAction(
        int $id,
        Request $request,
        SerializerInterface $serializer,
        EntityManagerInterface $em,
        LicensePlateRepository $lpRepository
    ): Response {
        /** @var ?LicensePlate $licensePlate */
        $licensePlate = $lpRepository->find($id);

        if ($licensePlate === null) {
            return $this->json([], Response::HTTP_NOT_FOUND);
        }

        /** @var User $user */
        $user = $this->getUser();
        // tylko użytkownik który jest właścicielem tablicy, lub jego manager mogą edytować rejesracje
        if (!in_array($licensePlate->getUser(), [$user, $user->getManager()])) {
            return $this->json([], Response::HTTP_FORBIDDEN);
        }

        $serializer->deserialize(
            $request->getContent(),
            LicensePlate::class,
            'json',
            [
                AbstractNormalizer::OBJECT_TO_POPULATE => $licensePlate,
            ],
        );

        $em->persist($licensePlate);
        $em->flush();

        return $this->json([], Response::HTTP_OK, [], ['groups' => 'plate_list']);
    }

    /**
     * @Route("/api/users/license_plates/{id}", name="license_plate_get_entity2", methods={"GET"})
     *
     * @OA\RequestBody(
     *      description= "Provide payment parameters",
     *      required= true,
     *      content={
     *             @OA\MediaType(
     *                 mediaType="application/json",
     *                 @OA\Schema(
     *                     example={
     *                          "id": 1,
     *                          "number": "ZS 123123",
     *                          "name": "Fleet car van",
     *                      }
     *                  )
     *              )
     *       }
     * ),
     *
     * @OA\Parameter(
     *     name="id",
     *     in="path",
     *     required=true,
     *     description="license plaete id",
     *     @OA\Schema(type="integer", example=1)
     * )
     *
     * @OA\Response(
     *     response="200",
     *     description="On success",
     *     content={
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 example={
     *                 }
     *             )
     *         )
     *     }
     * )
     *
     * @OA\Response(
     *     response="401",
     *     description="Unauthorized",
     * )
     *
     * @OA\Tag(name="LicensePlates")
     */
    public function returnAction(
        int $id,
        LicensePlateRepository $lpRepository
    ): Response {
        /** @var ?LicensePlate $licensePlate */
        $licensePlate = $lpRepository->find($id);

        if ($licensePlate === null) {
            return $this->json([], Response::HTTP_NOT_FOUND);
        }

        /** @var User $user */
        $user = $this->getUser();
        // tylko użytkownik który jest właścicielem tablicy, lub jego manager mogą widziec
        if (!in_array($licensePlate->getUser(), [$user, $user->getManager()])) {
            return $this->json([], Response::HTTP_FORBIDDEN);
        }

        return $this->json($licensePlate, Response::HTTP_OK, [], ['groups' => 'plate_list']);
    }
}
