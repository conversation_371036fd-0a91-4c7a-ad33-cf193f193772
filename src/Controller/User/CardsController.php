<?php

namespace App\Controller\User;

use App\Entity\User;
use App\User\UserService;
use Exception;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class CardsController extends AbstractController
{
    private UserService $userService;

    public function __construct(UserService $userService)
    {
        $this->userService = $userService;
    }

    /**
     * @Route(
     *     "/api/users/cards",
     *     name="api_users_cards",
     *     methods={"GET"}
     * )
     */
    public function getCardsAction()
    {
        /** @var User $user */
        $user = $this->getUser();

        $mytransactions = $this->userService->getUserCards($user);

        $data['trans'] = $mytransactions;
        $data['transSum'] = $this->userService->summarizeUserCards($mytransactions);

        return $this->json($data);
    }
}
