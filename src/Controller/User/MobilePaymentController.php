<?php

namespace App\Controller\User;

use App\Entity\User;
use App\MobilePayment\MobilePaymentManager;
use App\Repository\LoggerRepository;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Translation\Translator;
use Symfony\Contracts\Translation\TranslatorInterface;

use function Sentry\captureException;

class MobilePaymentController extends AbstractController
{
    /**
     * @Route("/api/users/mobilepayment", name="app_users_mobilepayment", methods={"POST"})
     */
    public function main(Request $request, MobilePaymentManager $paymentManager, LoggerRepository $logger, EntityManagerInterface $em, TranslatorInterface $translator): JsonResponse
    {
        /** @var User $user */
        $user = $this->getUser();
        $message = [];

        try {
            $inputData = json_decode($request->getContent(), true);

            // Ustaw locale translatora na podstawie języka z inputData
            if (isset($inputData['language'])) {
                /** @var Translator $translatorInstance */
                $translatorInstance = $translator;
                $translatorInstance->setLocale($inputData['language']);
            }

            $paySystem = $inputData['pay_system'] ?? null;
            $methodId = $inputData['methodId'];
            $user->getLiveStatus()->setAppVersion($inputData['appVersion'] ?? null);
            $logIdent = "$methodId : $paySystem";
            $context = [];
            $logger->log($user, "MOBILE_PAYMENT", $logIdent, "request: " . $request->getContent());
            switch ($methodId) {
                case 'initPayment':
                    $message = $paymentManager->initPayment($user, $inputData);
                    break;
                case 'getCarwashPaymentMethods':
                    $message = $paymentManager->getCardsForCarwash($user, $inputData);
                    break;
                case 'getPackagesPaymentMethods':
                    $message = $paymentManager->getCardsForPackage($user, $inputData);
                    break;
                case 'getTopUpValueMethods':
                    $message = $paymentManager->getCardsForTopUpValue($user, $inputData);
                    break;
                case 'availableCarwashes':
                    $message = $paymentManager->availableCarwashes();
                    break;
                case 'preparePayment':
                    $message = $paymentManager->preparePayment($user, $inputData);
                    $context['groups'] = ['invoices_list', 'default:basic'];
                    break;
                case 'washingState':
                    $message = $paymentManager->washingState(
                        user: $user,
                        paymentId: $inputData['paymentId']
                    );
                    break;

                case 'washingStop':
                    $message = $paymentManager->washingStop(
                        user: $user,
                        paymentId: $inputData['paymentId']
                    );
                    break;

                case 'washingResume':
                    $message = $paymentManager->washingResume(
                        user: $user,
                        paymentId: $inputData['paymentId']
                    );
                    break;
                default:
                    $message['status'] = 'invalidFormat';
                    $message['message'] = 'Unknow method Id';
                    break;
            }

            $logger->log($user, "MOBILE_PAYMENT", $logIdent, "response: " . json_encode($message));
        } catch (Exception $e) {
            captureException($e);
            $em->flush();
            $exceptionMessage = explode(';', $e->getMessage());
            $message['status'] = $exceptionMessage[0];
            if (isset($exceptionMessage[1]) === true) {
                $message['message'] = $exceptionMessage[1];
            }
            $logger->error($user, "MOBILE_PAYMENT", "Exception", "Exception: " . $e->getMessage(), true);
        }
        $em->flush();
        return $this->json($message, JsonResponse::HTTP_OK, [], $context);
    }
}
