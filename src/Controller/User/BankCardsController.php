<?php

namespace App\Controller\User;

use App\Entity\ExternalCreditCardToken;
use App\Entity\User;
use App\ExternalPayment\ExternalPaymentFactory;
use App\Repository\ExternalCreditCardTokenRepository;
use App\Repository\LoggerRepository;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

use function Sentry\captureException;

class BankCardsController extends AbstractController
{
    #[Route('/api/users/cards/register/init', name: 'app_users_cards_register_init', methods: "POST")]
    public function registerInit(
        Request $request,
        ExternalPaymentFactory $externalPaymentFactory,
        LoggerRepository $loggerRepository,
    ) {
        $user = $this->getUser();
        try {
            /** @var User $user */
            $creditCardService = $externalPaymentFactory->createCreditCard();

            $res = $creditCardService->addInit($user, $request->toArray());
            $loggerRepository->log($user, "BANKCARDS", "registerInit:request", $request->getContent());
            $loggerRepository->log($user, "BANKCARDS", "registerInit:response", json_encode($res));
            return $this->json($res);
        } catch (\Exception $e) {
            captureException($e);
            $loggerRepository->log($user, "BANKCARDS", "registerInit:exception", $e->getMessage());
            return $this->json(['error' => ['code' => 400, 'message' => $e->getMessage()]], 400);
        }
    }

    #[Route('/api/users/cards/register', name: 'app_users_cards_register', methods: "POST")]
    public function register(
        Request $request,
        ExternalPaymentFactory $externalPaymentFactory,
    ) {
        try {
            /** @var User $user */
            $user = $this->getUser();
            $creditCardService = $externalPaymentFactory->createCreditCard();

            $cards = $creditCardService->addRegister($user, $request->toArray());
            return $this->json(
                $cards,
                JsonResponse::HTTP_OK,
                [],
                ['groups' => 'creditcard:read']
            );
        } catch (\Exception $e) {
            captureException($e);
            return $this->json(['error' => ['code' => 400, 'message' => $e->getMessage()]], 400);
        }
    }

    #[Route('/api/users/cards/list', methods: "GET")]
    public function getCards(
        ExternalCreditCardTokenRepository $externalCreditCardTokenRepository
    ) {
        try {
            /** @var User $user */
            $user = $this->getUser();
            $cards = $externalCreditCardTokenRepository->getTokensForUser($user);
            return $this->json(
                $cards,
                JsonResponse::HTTP_OK,
                [],
                ['groups' => 'creditcard:read']
            );
        } catch (\Exception $e) {
            captureException($e);
            return $this->json(['error' => ['code' => 400, 'message' => $e->getMessage()]], 400);
        }
    }

    #[Route('/api/users/cards/{cardToken}', methods: "DELETE")]
    public function deleteCreditCard(
        string $cardToken,
        ExternalCreditCardTokenRepository $externalCreditCardTokenRepository
    ): JsonResponse {
        try {
            /** @var User $user */
            $user = $this->getUser();
            $externalCreditCardTokenRepository->deleteCardByToken($user, $cardToken);
            return $this->json([]);
        } catch (\Exception $e) {
            captureException($e);
            return $this->json([], 400);
        }
    }
}
