<?php

namespace App\Controller\User;

use App\Entity\Enum\Languages;
use App\Entity\User;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class LanguagesUserController extends AbstractController
{
    #[Route('/api/users/language', methods: ['PATCH'])]
    public function setAction(
        Request $request,
        EntityManagerInterface $em,
    ) {
        /** @var User $user */
        $user = $this->getUser();
        $langLocale = json_decode($request->getContent(), true)['language'];

        try {
            $user->setLanguage(Languages::from($langLocale));

            $em->persist($user);
            $em->flush();
        } catch (Exception $e) {
            return $this->json(['error' => ['code' => 400, 'message' => 'Bad Request']], 400);
        }

        return $this->json([], Response::HTTP_OK);
    }
}
