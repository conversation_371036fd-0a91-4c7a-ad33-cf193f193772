<?php

namespace App\Controller\User;

use App\ApiConnector\CarwashApiConnector;
use App\Repository\CarwashRepository;
use DateInterval;
use DateTime;
use Doctrine\Common\Collections\ArrayCollection;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Serializer\SerializerInterface;

class CarwashListController extends AbstractController
{
    /**
     * @deprecated use avaliableCarwashes
     * @Route("/api/users/carwash", methods={"GET", "OPTIONS"})
     */
    public function getCarwashListForFlutterApp(CarwashRepository $carwashRepository): JsonResponse
    {
        $carwashes =
            $carwashRepository->findBy(
                [
                    'testCarwash' => false,
                    'paymentEnabled' => true,
                ]
            );
        return $this->json(data: $carwashes, context: ['groups' => 'flutter_app']);
    }
}
