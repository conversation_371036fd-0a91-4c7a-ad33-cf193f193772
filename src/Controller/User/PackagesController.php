<?php

namespace App\Controller\User;

use App\Repository\MobilePaymentPackageRepository;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Annotation\Route;

class PackagesController extends AbstractController
{
    #[Route('/api/users/packages', name: 'app_user_packages', methods: ['GET'])]
    public function getPackages(
        MobilePaymentPackageRepository $mobilePaymentPackageRepository
    ): JsonResponse {
        $packages = $mobilePaymentPackageRepository->findPackages();
        return $this->json(
            $packages,
            JsonResponse::HTTP_OK,
            [],
            [
                'groups' => [
                    "package:list",
                    'datetime_format' => 'Y-m-d H:i:s'
                ]
            ]
        );
    }
}
