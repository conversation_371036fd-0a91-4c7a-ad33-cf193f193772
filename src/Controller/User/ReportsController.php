<?php

namespace App\Controller\User;

use App\Entity\User;
use App\Service\Reports\ReportGenerator;
use I2m\Reports\Enum\FileExtention;
use I2m\Reports\Service\ReportService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\ResponseHeaderBag;
use Symfony\Component\Routing\Annotation\Route;

class ReportsController extends AbstractController
{
    #[Route(path: '/api/users/reports/factory/generate', methods: ['GET'])]
    public function generateAction(
        Request $request,
        ReportGenerator $reportGenerator,
        ReportService $reportService
    ): Response {
        /** @var User $user */
        $user = $this->getUser();
        $reportConfig = $reportGenerator->getConfig($request, $user);

        $reportFile = $reportGenerator->generateFile(
            $reportConfig
        );
        $blobData = $reportService->getStream($reportFile);
        $fileName = basename($reportConfig->getName() . '.' . $reportConfig->getExt()->value);

        $response = new Response(stream_get_contents($blobData));
        $response->headers->set('Content-Type', 'application/octet-stream');
        $response->headers->set('Content-Disposition', 'attachment; filename="' . $fileName . '"');

        return $response;
    }
}
