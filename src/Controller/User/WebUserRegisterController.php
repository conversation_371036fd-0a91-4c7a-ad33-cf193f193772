<?php

namespace App\Controller\User;

use App\Entity\User;
use App\Form\RegistrationFormType;
use App\Repository\UserRepository;
use App\Security\EmailVerifier;
use App\User\Registration\UserRegisterManager;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use SymfonyCasts\Bundle\VerifyEmail\Exception\VerifyEmailExceptionInterface;

class WebUserRegisterController extends AbstractController
{
    /**
     * @Route("/register", name="user_register")
     */
    public function register(Request $request, UserRegisterManager $userRegisterManager): Response
    {
        $user = new User();
        $form = $this->createForm(RegistrationFormType::class, $user);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $userRegisterManager->registerWebUser($user, $form->get('plainPassword')->getData());
        }

        return $this->render(
            'registration/register.html.twig',
            [
                'registrationForm' => $form->createView(),
            ]
        );
    }

    /**
     * @Route("/verify/email", name="app_verify_email")
     */
    public function verifyUserEmail(
        Request $request,
        UserRepository $userRepository,
        EmailVerifier $emailVerifier,
        ParameterBagInterface $parameterBag
    ): Response {
        $id = $request->get('id');
        $frontUrl = $parameterBag->get('front_url');
        $redirectUrlSuccess = $frontUrl . '/#/users/register-status/success';
        $redirectUrlFail = $frontUrl . '/#/users/register-status/fail';

        if (null === $id) {
            return $this->redirect($redirectUrlFail);
        }

        $user = $userRepository->find($id);

        if (null === $user) {
            return $this->redirect($redirectUrlFail);
        }

        // validate email confirmation link, sets User::isVerified=true and persists
        try {
            $emailVerifier->handleEmailConfirmation($request, $user);
        } catch (VerifyEmailExceptionInterface $exception) {
            $this->addFlash('verify_email_error', $exception->getReason());

            return $this->redirect($redirectUrlFail);
        }

        return $this->redirect($redirectUrlSuccess);
    }
}
