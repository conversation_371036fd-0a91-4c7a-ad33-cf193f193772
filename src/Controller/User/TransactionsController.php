<?php

namespace App\Controller\User;

use App\Entity\Enum\MobilePaymentStatus;
use App\Entity\Enum\ReportType;
use App\Model\DateTimeNullableRangeRequest;
use App\Repository\MobilePaymentRepository;
use App\Service\Reports\ReportService;
use I2m\Reports\Enum\ReportInterval;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use App\Entity\User;

class TransactionsController extends AbstractController
{
    #[Route(path: '/api/users/transactions_report', methods: ['GET'])]
    public function getTransactionsListReportData(
        Request $request,
        ReportService $reportService,
    ): JsonResponse {
        $page = $request->get('pageNumber');
        $perPage = $request->get('itemsPerPage');
        /** @var User $user */
        $user = $this->getUser();

        $report = $reportService->getData(
            reportType: ReportType::TRANSACTIONS_REPORT,
            criteria:   $request->query->all(),
            user:       $user,
            page:       $page,
            perPage:    $perPage,
        );

        return $this->json(data: $report, context: ['groups' => ["report:data"]]);
    }

    #[Route('/api/users/transactions', methods: ['GET'])] // deprecated, use /api/users/transactions_report
    public function getTransactions(Request $request, MobilePaymentRepository $paymentRepository, ParameterBagInterface $parameterBag): JsonResponse
    {
        /** @var User $user */
        $user = $this->getUser();
        $itemsPerPage = $request->get('itemsPerPage', 10);
        $pageNumber = $request->get('pageNumber', 1);
        $status = $request->get('status') ? explode(',', $request->get('status')) : [MobilePaymentStatus::CONFIRMED];
        ;
        $extPay = $request->get('extPay') ? explode(',', $request->get('extPay')) : null;
        $type = $request->get('type') ? explode(',', $request->get('type')) : null;
        $issuer = $request->get('issuer') ? explode(',', $request->get('issuer')) : null;
        $search = $request->get('search');

        $dateRange = new DateTimeNullableRangeRequest(
            $request->get('dateFrom'),
            $request->get('dateTo'),
            $request->get($parameterBag->get('branding_timezone'))
        );

        $mobilePayments = $paymentRepository->findPaymentsByDateRangeAndUser(
            $user,
            null,
            $dateRange->getDateFrom(),
            $dateRange->getDateTo(),
            $status,
            $issuer,
            $extPay,
            $type,
            $search,
            $pageNumber,
            $itemsPerPage
        );


        return $this->json($mobilePayments, Response::HTTP_OK, [], [
            'groups' => [
                'payments:list',
                'default:basic',
                "locale:list"
            ],
            'datetime_format' => 'Y-m-d H:i:s']);
    }

    #[Route('/api/users/transactions/{id}', methods: ['GET'])]
    public function getTransaction(int $id, MobilePaymentRepository $paymentRepository): JsonResponse
    {
        $mobilePayment = $paymentRepository->findOneBy(
            [
                'id' => $id,
                'bkfpayUser' => $this->getUser()
            ]
        );
        if (is_null($mobilePayment)) {
            return $this->json(['error' => "payment not found"], JsonResponse::HTTP_NOT_FOUND);
        }

        return $this->json($mobilePayment, Response::HTTP_OK, [], [
            'groups' => [
                'payments:list',
                'default:basic',
                "locale:list"
            ],
            'datetime_format' => 'Y-m-d H:i:s']);
    }
}
