<?php

namespace App\Controller\User;

use App\Entity\User;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class PostPaidSettingsController extends AbstractController
{
    #[Route('/api/users/settings/post-paid', name: 'app_users_settings_post_paid', methods: ['GET'])]
    public function download(): Response
    {
        /** @var User|null $user */
        $user = $this->getUser();

        return $this->json(
            $user?->getPostPaidSettings(),
            Response::HTTP_OK,
            [],
            ['groups' => ['user:settings:post-paid']]
        );
    }
}
