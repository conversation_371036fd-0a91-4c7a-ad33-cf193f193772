<?php

namespace App\Controller\User;

use App\Entity\User;
use App\Repository\LoggerRepository;
use App\Support\MobileAppSupportReport;
use App\Support\SupportReportService;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

use function Sentry\captureException;

class SupportController extends AbstractController
{
    #[Route("/api/users/support", methods : ["POST", "OPTIONS"])]
    public function sendSupportEmail(
        LoggerRepository $loggerRepository,
        Request $request,
        SupportReportService $supportReportService,
        #[MapRequestPayload] MobileAppSupportReport $supportReport
    ): Response {
        /** @var User $user */
        $user = $this->getUser();
        $loggerRepository->log(
            $user,
            "SUPPORT",
            $supportReport->getType()->value,
            $request->getContent()
        );
        try {
            if ($supportReportService->supportAction($user, $supportReport)) {
                return $this->json([], JsonResponse::HTTP_NO_CONTENT);
            }
            return $this->json([], JsonResponse::HTTP_NO_CONTENT);
        } catch (\Exception $e) {
            captureException($e);
            return $this->json(['status' => $e->getMessage()], 400);
        }
    }
}
