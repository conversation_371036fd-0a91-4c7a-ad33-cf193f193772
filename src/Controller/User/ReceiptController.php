<?php

namespace App\Controller\User;

use App\Entity\Receipt;
use App\Repository\ReceiptRepository;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class ReceiptController extends AbstractController
{
    #[Route('/api/users/receipt/download/{receiptId}', name: 'app_user_receipt_download', methods: ['GET'])]
    #[Route('/api/users/receipt/{receiptId}/download', methods: ['GET'])]
    public function download(int $receiptId, ReceiptRepository $receiptRepository): Response
    {
        $user = $this->getUser();


        $receipt = $receiptRepository->findOneBy([
                                                     'user' => $user,
                                                     'id' => $receiptId
                                                 ]);

        if (is_null($receipt)) {
            return new Response(
                "Receipt not found for logged user",
                Response::HTTP_NOT_FOUND
            );
        }

        $imageBinaryData = base64_decode($receipt->getImage());

        // Zwracamy odpowiedź z plikiem do pobrania
        return new Response(
            $imageBinaryData,
            Response::HTTP_OK,
            [
                'Content-Type' => 'image/png',
                'Content-Disposition' => 'attachment; filename="receipt-' . $receipt->getJpkId() . '.png"',
            ]
        );
    }
}
