<?php

namespace App\Controller\Admin;

use App\Repository\OwnerRepository;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class OwnersController extends AbstractController
{
    #[Route('/api/admin/owners', methods: "GET")]
    public function getList(OwnerRepository $ownerRepository): JsonResponse
    {
        return $this->json($ownerRepository->findAll(), Response::HTTP_OK, [], [
            'groups' =>
                [
                    'owner:list',
                    'default:basic'
                ]
            ,'datetime_format' => 'Y-m-d H:i:s']);
    }
}
