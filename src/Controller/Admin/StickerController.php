<?php

namespace App\Controller\Admin;

use App\Service\StickerGenerator\StickerService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\HttpFoundation\ResponseHeaderBag;
use Symfony\Component\Routing\Annotation\Route;

class StickerController extends AbstractController
{
    #[Route('/api/admin/sticker/{code}', methods: ['GET'])]
    public function dowload(string $code, StickerService $stickerService): BinaryFileResponse
    {
          $filePath = $stickerService->generateSingle($code);

        // Tworzenie odpowiedzi BinaryFileResponse
        $response = new BinaryFileResponse($filePath);
        $response->setContentDisposition(
            ResponseHeaderBag::DISPOSITION_ATTACHMENT,
            basename($filePath)
        );

        return $response;
    }
}
