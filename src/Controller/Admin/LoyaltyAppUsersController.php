<?php

namespace App\Controller\Admin;

use App\Entity\User;
use App\MobilePayment\MobilePaymentFacade\Facade\BkfPayTransferFacade;
use App\Repository\LoggerRepository;
use App\Repository\MobilePaymentRepository;
use App\Repository\UserRepository;
use App\Service\AppConfig\AppConfigService;
use App\Service\Mobile\MobileNotificationService;
use App\User\Registration\UserRegisterManager;
use App\User\UserService;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use DateTime;
use Doctrine\DBAL\Exception\UniqueConstraintViolationException;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Serializer\Normalizer\AbstractNormalizer;
use Symfony\Component\Serializer\Normalizer\AbstractObjectNormalizer;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use OpenApi\Annotations as OA;
use Symfony\Contracts\Translation\TranslatorInterface;
use Throwable;

class LoyaltyAppUsersController extends AbstractController
{
    public function __construct(
        private EntityManagerInterface $em,
        private LoggerRepository $loggerRepository
    ) {
    }

    /**
     * @Route("/api/admin/users", name="admin_user_create", methods={"POST"})
     * @OA\Post(summary="Create a new user")
     * @OA\RequestBody(
     *      description= "Create user with optional promotional package and company data",
     *      required= true,
     *      content={
     *             @OA\MediaType(
     *                 mediaType="application/json",
     *                 @OA\Schema(
     *                      example={
     *                          "email":"<EMAIL>",
     *                          "packageId": 1,
     *                          "companyData": {
     *                              "name": "Przykładowa Firma Sp. z o.o.",
     *                              "taxNumber": "1234567890",
     *                              "address": "ul. Przykładowa 123",
     *                              "postCode": "00-001",
     *                              "city": "Warszawa",
     *                              "country": "PL",
     *                              "email": "<EMAIL>"
     *                          }
     *                      }
     *                  )
     *              )
     *       }
     * )
     *
     * @OA\Response(
     *      response="200",
     *      description="ok",
     *      content={
     *          @OA\MediaType(
     *              mediaType="application/json",
     *              @OA\Schema(
     *                  example={
     *                      "status": true
     *                  }
     *              )
     *          )
     *      }
     * )
     *
     * @OA\Response(response="401",description="Unauthorized")
     * @OA\Response(
     *     response="400",
     *     description="Bad Request - When promotional package doesn't exist or is not active",
     *      content={
     *          @OA\MediaType(
     *              mediaType="application/json",
     *              @OA\Schema(
     *                  example={
     *                        "status": false,
     *                        "message": "Pakiet promocyjny o ID 1 nie istnieje"
     *                  }
     *              )
     *          )
     *      }
     * )
     * @OA\Response(
     *     response="409",
     *     description="Conflict - When trying to create user with already existing email",
     *      content={
     *          @OA\MediaType(
     *              mediaType="application/json",
     *              @OA\Schema(
     *                  example={
     *                        "status": false,
     *                        "message": "Twoje konto już istnieje w systemie"
     *                  }
     *              )
     *          )
     *      }
     * )
     * @OA\Response(response="403",description="Forbiden - when user dont's have role ROLE_APP_MANAGER ")
     *
     * @OA\Tag(name="LoyalApp")
     *
     *
     * @return JsonResponse
     */
    public function createUserAction(
        Request $request,
        UserRegisterManager $userRegisterManager
    ) {
        if (!$userRegisterManager->isApiUserCreationRequestValid($request)) {
            return $this->json(['status' => false, 'message' => "No email"]);
        }

        try {
            $userRegisterManager->createApiUser();
        } catch (Throwable $exception) {
            if ($exception instanceof UniqueConstraintViolationException) {
                return $this->json(
                    [
                        'status'  => false,
                        'message' => "Twoje konto już istnieje w systemie",
                    ],
                    409
                );
            }

            throw $exception;
        }

        return $this->json(['status' => true]);
    }

    /**
     * @deprecated use createUserAction
     * @Route ("/api/admin/users/invite", name="admin_user_invite", methods={"POST"})
     */

    public function inviteUserAction(
        Request $request,
        UserRegisterManager $userRegisterManager
    ) {
        return $this->createUserAction($request, $userRegisterManager);
    }

    /**
     * @Route("/api/admin/users", name="admin_user_get_list", methods={"GET"})
     */
    public function getList(Request $request, UserRepository $userRepository): JsonResponse
    {
        $search = $request->get('search');
        $itemsPerPage = $request->get('perPage', 10);
        $pageNumber = $request->get('page', 1);
        $dateFrom = new DateTime($request->query->get('startDate', '-1 year'));
        $dateTo = new DateTime($request->query->get('endDate', 'now'));
        $showTotal = $request->query->getBoolean('showTotal', true);
        $showStats = $request->query->getBoolean('showStats', false);
        $showInfo = $request->query->getBoolean('showInfo', false);
        $orderDescending = $request->query->getBoolean('orderDescending', true);
        $orderBy = $request->query->get('orderBy', null);

        $users = $userRepository->getList(
            $search,
            null,
            $pageNumber,
            $itemsPerPage,
            $showTotal,
            $orderBy,
            $orderDescending,
        );

        $context = [
            'groups' => [
                "default:basic",
                "user:list",
                "user:admin",
            ],
        ];

        if ($showStats) {
            $context['groups'][] = "user:stats";
            $context['stats'] = [
                'from' => $dateFrom,
                'to' => $dateTo
            ];
        }

        if ($showInfo) {
            $context['groups'][] = 'user:details';
        }

        return $this->json(
            $users,
            Response::HTTP_OK,
            [],
            $context
        );
    }

    #[Route('/api/admin/user/{id}', methods: ['GET'])]
    public function getUserDetailsAction(Request $request, User $user): JsonResponse
    {
        $dateFrom = new DateTime($request->query->get('startDate', '-1 year'));
        $dateTo = new DateTime($request->query->get('endDate', 'now'));

        return $this->json(
            $user,
            Response::HTTP_OK,
            [],
            [
                'groups' => [
                    "default:basic",
                    "user:list",
                    "user:admin",
                    'user:details',
                    'user:stats',
                    'company:data',
                ],
                'stats' => [
                    'from' => $dateFrom,
                    'to' => $dateTo
                ],
            ]
        );
    }

    #[Route('/api/admin/user/{id}/cards', methods: ['GET'])]
    public function getUserCardsAction(User $user, UserService $userService): JsonResponse
    {
        $myTansactions = $userService->getUserCards($user);

        return $this->json(['items' => $myTansactions]);
    }
    #[Route('/api/admin/user/{id}', methods: ['PATCH'])]
    public function updateUserAction(User $user, Request $request, SerializerInterface $serializer)
    {

        $serializer
            ->deserialize(
                $request->getContent(),
                User::class,
                'json',
                [
                    AbstractNormalizer::OBJECT_TO_POPULATE => $user,
                    AbstractObjectNormalizer::DEEP_OBJECT_TO_POPULATE => true,
                ]
            );

        if ($user->isFleetManager() === false && $user->isTrustedPartner() === true) {
            return new JsonResponse(
                [
                    'error'      => true,
                    'error_code' => 'could_not_set_trusted_partner_to_regular_user',
                ],
                200
            );
        }
        $this->em->persist($user);
        $this->em->flush();

        return $this->json(
            $user,
            Response::HTTP_OK,
            [],
            [
                'groups' => [
                    "default:basic",
                    "user:list",
                    "user:admin",
                    'user:details',
                    'company:data',
                ],
            ]
        );
    }

    #[Route('/api/admin/user/{id}/transfer', methods: ['POST'])]
    public function addTransfer(
        User $user,
        Request $request,
        BkfPayTransferFacade $bkfPayTransferFacade,
        MobileNotificationService $notificationService,
        AppConfigService $appConfigService,
        TranslatorInterface $translator
    ) {
        $inputData = json_decode($request->getContent(), true);
        $response = $bkfPayTransferFacade->initPayment($user, $inputData);

        $this->sendAccountTopupNotification(
            $user,
            $inputData['value'] / 100,
            $notificationService,
            $translator,
            $appConfigService,
            "TRANSFER_NOTIFICATION_ERROR"
        );

        return $this->json(
            $response,
            context: [
                'groups' => [
                    "default:basic",
                    'payments:list'
                ],
            ],
        );
    }

    #[Route('/api/admin/user/{id}/bonus', methods: ['POST'])]
    public function addBonus(
        User $user,
        Request $request,
        MobilePaymentRepository $mobilePaymentRepository,
        MobileNotificationService $notificationService,
        TranslatorInterface $translator,
        AppConfigService $appConfigService
    ) {
        $inputData = json_decode($request->getContent(), true);
        $response = $mobilePaymentRepository->addBonus(
            $user,
            $inputData['value'] / 100,
            $inputData['appName'] ?? null,
        );

        $this->sendAccountTopupNotification(
            $user,
            $inputData['value'] / 100,
            $notificationService,
            $translator,
            $appConfigService,
            "BONUS_NOTIFICATION_ERROR"
        );

        return $this->json(
            $response,
            context: [
                'groups' => [
                    'default:basic',
                    'payments:list'
                ],
            ],
        );
    }

    #[Route('/api/admin/user/{id}/send', methods: ['POST'])]
    public function send(Request $request, MobileNotificationService $notificationService, int $id): JsonResponse
    {
        $data = json_decode($request->getContent(), true);

        if (!isset($data['title'], $data['body'])) {
            return new JsonResponse(
                ['error' => 'Missing required fields: title, body'],
                Response::HTTP_BAD_REQUEST
            );
        }

        try {
            $notificationService->sendNotification(
                $id,
                $data['title'],
                $data['body']
            );

            return new JsonResponse([
                'message' => 'Notification sent successfully',
            ]);
        } catch (NotFoundHttpException $e) {
            return new JsonResponse(
                ['error' => $e->getMessage()],
                Response::HTTP_NOT_FOUND
            );
        } catch (\Exception $e) {
            return new JsonResponse(
                [
                    'error' => 'Failed to send notification',
                ],
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    #[Route('/api/admin/users/send', methods: ['POST'])]
    public function sendTopicNotification(
        Request $request,
        MobileNotificationService $notificationService,
        ParameterBagInterface $parameterBag
    ): JsonResponse {
        $data = json_decode($request->getContent(), true);

        if (!isset($data['title'], $data['body'])) {
            return new JsonResponse(
                ['error' => 'Missing required fields: title, body'],
                Response::HTTP_BAD_REQUEST
            );
        }

        $topic = $parameterBag->get('branding_name');

        try {
            $notificationService->sendTopicNotification(
                $topic,
                $data['title'],
                $data['body'],
                $data['data'] ?? []
            );

            return new JsonResponse([
                'message' => 'Topic notification sent successfully',
                'topic' => $topic
            ]);
        } catch (\Exception $e) {
            return new JsonResponse(
                [
                    'error' => 'Failed to send topic notification',
                    'message' => $e->getMessage()
                ],
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Helper method to send account top-up notification
     */
    private function sendAccountTopupNotification(
        User $user,
        float $amount,
        MobileNotificationService $notificationService,
        TranslatorInterface $translator,
        AppConfigService $appConfigService,
        string $errorType = "NOTIFICATION_ERROR",
    ): void {
        try {
            $formattedAmount = number_format($amount, 2);
            $currencyCode = $appConfigService->getCurrency()->value;

            $title = $translator->trans('notification.account.topup.title');
            $body = $translator->trans('notification.account.topup.body', [
                '%amount%' => $formattedAmount,
                '%currency%' => $currencyCode
            ]);

            $notificationService->sendNotification(
                $user->getId(),
                $title,
                $body
            );
        } catch (\Exception $e) {
            // Log the error but don't fail the operation
            $this->loggerRepository->notice(
                user: $user,
                type: $errorType,
                identifier: null,
                comment: sprintf("Error sending notification: %s", $e->getMessage())
            );
        }
    }
}
