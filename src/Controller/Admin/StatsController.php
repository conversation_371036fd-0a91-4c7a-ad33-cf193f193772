<?php

namespace App\Controller\Admin;

use App\Model\DateTimeNullableRangeRequest;
use App\Repository\Enum\Period;
use App\Repository\MobilePaymentRepository;
use App\Repository\OwnerRepository;
use App\Repository\UserRepository;
use App\Service\AppConfig\AppConfigService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class StatsController extends AbstractController
{
    #[Route('/api/admin/stats/trend/payments', methods: "GET")]
    public function getTrendPayment(
        EntityManagerInterface $em,
        Request $request,
        MobilePaymentRepository $paymentRepository,
        AppConfigService $appConfigService
    ): JsonResponse {
        if ($em->getFilters()->isEnabled('status_filter')) {
            $em->getFilters()->disable('status_filter');
        }

        $dateRange = new DateTimeNullableRangeRequest(
            $request->get('dateFrom', '2020-01-01'),
            $request->get('dateTo', 'now'),
            $request->get('timezone')
        );

        $trend = $paymentRepository->getPaymentTrend(
            $dateRange->getDateFrom(),
            $dateRange->getDateTo(),
            $dateRange->getTimezone(),
            Period::tryFrom($request->get('period', Period::MONTH->value))
        );



        return $this->json(['data' => $trend, 'currency' => $appConfigService->getCurrency()]);
    }

    #[Route('/api/admin/stats/trend/balance', methods: "GET")]
    public function getTrendBalance(
        EntityManagerInterface $em,
        Request $request,
        MobilePaymentRepository $paymentRepository,
        AppConfigService $appConfigService
    ): JsonResponse {
        if ($em->getFilters()->isEnabled('status_filter')) {
            $em->getFilters()->disable('status_filter');
        }

        $dateRange = new DateTimeNullableRangeRequest(
            $request->get('dateFrom', '2020-01-01'),
            $request->get('dateTo', 'now'),
            $request->get('timezone')
        );

        $trend = $paymentRepository->getBkfPayTrend(
            $dateRange->getDateFrom(),
            $dateRange->getDateTo(),
            $dateRange->getTimezone(),
            Period::tryFrom($request->get('period', Period::MONTH->value))
        );
        return $this->json(['data' => $trend, 'currency' => $appConfigService->getCurrency()]);
    }

    #[Route('/api/admin/stats/balance', methods: "GET")]
    public function getBalance(
        EntityManagerInterface $em,
        MobilePaymentRepository $paymentRepository,
        AppConfigService $appConfigService
    ): JsonResponse {
        if ($em->getFilters()->isEnabled('status_filter')) {
            $em->getFilters()->disable('status_filter');
        }

        $data = $paymentRepository->getStats(
        );
        return $this->json(['data' => $data, 'currency' => $appConfigService->getCurrency()]);
    }
}
