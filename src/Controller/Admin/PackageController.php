<?php

namespace App\Controller\Admin;

use App\Entity\MobilePaymentPackage;
use App\Repository\MobilePaymentPackageRepository;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Component\Routing\Annotation\Route;

/**
 * Class PromotionalPackageController
 *
 * @package CarwashManagerBundle\Controller
 */
class PackageController extends AbstractController
{
    /**
     * @Route("/api/admin/packages" , methods={"POST"})
     */
    public function newAction(
        Request $request,
        SerializerInterface $serializer,
        MobilePaymentPackageRepository $packageRepository,
        ParameterBagInterface $parameterBag,
    ): JsonResponse {

        /** @var MobilePaymentPackage $item */
        $item = $serializer->deserialize($request->getContent(), MobilePaymentPackage::class, 'json', ['groups' => ['package:edit']]);

        $timezone = new \DateTimeZone($parameterBag->get('branding_timezone'));
        if ($item->getStartTime()) {
            $item->setStartTime(
                (new \DateTime($item->getStartTime()->format('Y-m-d 0:0:0'), $timezone))
            );
        }

        if ($item->getEndTime()) {
            $item->setEndTime(
                (new \DateTime($item->getEndTime()->format('Y-m-d 23:59:59'), $timezone))
            );
            ;
        }

        $packageRepository->save($item);

        return $this->json(
            data: $item,
            context: [
                'groups' => ['package:list'],
                'datetime_format' => 'Y-m-d H:i:s'
            ]
        );
    }

    /**
     * @Route("/api/admin/packages" , methods={"GET"})
     */
    public function getList(
        MobilePaymentPackageRepository $packageRepository
    ): JsonResponse {


        return $this->json(
            data: $packageRepository->getList(),
            context:
            [
                'groups' => [
                    'package:list'
                ],
                'datetime_format' => 'Y-m-d H:i:s'
            ]
        );
    }

    /**
     * @Route("/api/admin/package/{id}" , methods={"GET"})
     */
    public function getItem(
        int $id,
        MobilePaymentPackageRepository $packageRepository
    ): JsonResponse {
        return $this->json(
            data: $packageRepository->find($id),
            context: [
                'groups' => ['package:list'],
                'datetime_format' => 'Y-m-d H:i:s'
            ]
        );
    }

    /**
     * @Route("/api/admin/package/{id}" , methods={"PUT"})
     */
    public function updateItem(
        int $id,
        Request $request,
        MobilePaymentPackageRepository $packageRepository,
        SerializerInterface $serializer,
        ParameterBagInterface $parameterBag,
    ): JsonResponse {
        $item = $packageRepository->find($id);
        $serializer->deserialize($request->getContent(), MobilePaymentPackage::class, 'json', ['object_to_populate' => $item, 'groups' => ['package:edit']]);

        $timezone = new \DateTimeZone($parameterBag->get('branding_timezone'));

        if ($item->getStartTime()) {
            $item->setStartTime(
                (new \DateTime($item->getStartTime()->format('Y-m-d 0:0:0'), $timezone))
            );
        }

        if ($item->getEndTime()) {
            $item->setEndTime(
                (new \DateTime($item->getEndTime()->format('Y-m-d 23:59:59'), $timezone))
            );
            ;
        }

        $packageRepository->save($item);

        return $this->json(
            data: $item,
            context: [
                'groups' => ['package:list'],
                'datetime_format' => 'Y-m-d H:i:s'
            ]
        );
    }

    /**
     * @Route("/api/admin/package/{id}" , methods={"DELETE"})
     */
    public function deleteItem(
        int $id,
        MobilePaymentPackageRepository $packageRepository,
    ): JsonResponse {
        $item = $packageRepository->find($id);
        $item->setHideOnList(true);
        $packageRepository->save($item);

        return $this->json(
            data: $item,
            context: [
                'groups' => ['package:list'],
                'datetime_format' => 'Y-m-d H:i:s'
            ]
        );
    }
}
