<?php

namespace App\Controller\Admin;

use App\Service\SalesDocument\MissingSalesDocumentFinder;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

/**
 * Alerts controller.
 *
 */
class AlertsController extends AbstractController
{
    #[Route('/api/admin/alerts', methods: ['GET'])]
    public function getAlertsList(
        MissingSalesDocumentFinder $missingSalesDocumentFinder,
    ): JsonResponse {
        $alerts = $missingSalesDocumentFinder->generate();

        return $this->json(
            ['data' => $alerts, 'count' => count($alerts)],
            Response::HTTP_OK,
            [],
            [
                'groups' => [
                    'alert:info',
                ]
            ]
        );
    }
}
