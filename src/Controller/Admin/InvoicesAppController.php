<?php

namespace App\Controller\Admin;

use App\Entity\Invoices;
use App\Model\DateTimeNullableRangeRequest;
use App\Repository\InvoicesRepository;
use App\Service\SalesDocument\Invoices\PaymentInvoiceGenerator;
use Doctrine\ORM\EntityManagerInterface;
use I2m\Invoices\Service\InvoiceService;
use I2m\Payment\Enum\Status;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

/**
 * Subscribers controller.
 *
 */
class InvoicesAppController extends AbstractController
{
    #[Route('/api/admin/invoices', methods: ['GET'])]
    public function getInvoicesList(
        EntityManagerInterface $em,
        Request $request,
        InvoicesRepository $invoicesRepository,
        ParameterBagInterface $parameterBag
    ): JsonResponse {
        if ($em->getFilters()->isEnabled('status_filter')) {
            $em->getFilters()->disable('status_filter');
        }

        $itemsPerPage = $request->get('itemsPerPage', 10);
        $pageNumber = $request->get('pageNumber', 1);
        $search = $request->get('search');
        $status = $request->get('status') ? explode(',', $request->get('status')) : null;

        $dateRange = new DateTimeNullableRangeRequest(
            $request->get('dateFrom'),
            $request->get('dateTo'),
            $request->get($parameterBag->get('branding_timezone'))
        );

        $invoicesList = $invoicesRepository->getList(
            null,
            $dateRange->getDateFrom(),
            $dateRange->getDateTo(),
            $pageNumber,
            $itemsPerPage,
            $search,
            $status
        );

        return $this->json(
            $invoicesList,
            Response::HTTP_OK,
            [],
            ['groups' => ['invoice:list', 'company:data', 'client:data',
                "default:basic"]]
        );
    }

    #[Route('/api/admin/invoice/{id}/download', methods: ['GET'])]
    public function downloadInvoice(
        Invoices $invoice,
        InvoiceService $invoiceService,
    ): Response {
        if (is_null($invoice->getCloudPath())) {
            return new Response(
                "invoice not found",
                Response::HTTP_NOT_FOUND
            );
        }

        return $invoiceService->downloadResponse($invoice);
    }

    #[Route('/api/admin/invoice/{id}/send', methods: ['GET'])]
    public function sendInvoice(
        Invoices $invoice,
        PaymentInvoiceGenerator $paymentInvoiceGenerator,
    ): Response {

        $paymentInvoiceGenerator->send($invoice);
        return $this->json([]);
    }

    #[Route('/api/admin/invoice/{id}/confirm', methods: ['POST'])]
    public function confirm(
        Invoices $invoice,
        InvoicesRepository $invoicesRepository,
    ): Response {

        $invoice->getExternalPayment()->setStatus(Status::CONFIRMED);
        $invoicesRepository->save($invoice);
        return $this->json(
            $invoice,
            Response::HTTP_OK,
            [],
            ['groups' => ['invoice:list',
                "default:basic"]]
        );
    }
}
