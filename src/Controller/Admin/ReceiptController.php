<?php

namespace App\Controller\Admin;

use App\Entity\Receipt;
use App\Model\DateTimeNullableRangeRequest;
use App\Repository\ReceiptRepository;
use App\Repository\UserRepository;
use Doctrine\ORM\EntityManagerInterface;
use Nelmio\Alice\ParameterBag;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class ReceiptController extends AbstractController
{
    #[Route('/api/admin/receipt/{receiptId}/download', name: 'app_admin_receipt_image', methods: ['GET'])]
    public function download(int $receiptId, ReceiptRepository $receiptRepository): Response
    {
        $receipt = $receiptRepository->findOneBy([
                                                     'id' => $receiptId
                                                 ]);

        if (is_null($receipt)) {
            return new Response(
                "Receipt not found",
                Response::HTTP_NOT_FOUND
            );
        }

        $imageBinaryData = base64_decode($receipt->getImage());

        // Zwracamy odpowiedź z plikiem do pobrania
        return new Response(
            $imageBinaryData,
            Response::HTTP_OK,
            [
                'Content-Type' => 'image/png',
                'Content-Disposition' => 'attachment; filename="receipt-' . $receipt->getJpkId() . '.png"',
            ]
        );
    }

    #[Route('/api/admin/receipt/{receiptId}/jws', name: 'app_admin_receipt_jws', methods: ['GET'])]
    public function read(int $receiptId, ReceiptRepository $receiptRepository): JsonResponse
    {

        $receipt = $receiptRepository->find($receiptId);

        if (is_null($receipt)) {
            return new JsonResponse(
                "Receipt not found",
                JsonResponse::HTTP_NOT_FOUND
            );
        }
        return $this->json($receipt->getJws(), JsonResponse::HTTP_OK, [], ['groups' => ['receipt:read']]);
    }

    #[Route('/api/admin/receipts', name: 'app_admin_receipt_list', methods: ['GET'])]
    public function getList(
        Request $request,
        UserRepository $userRepository,
        ReceiptRepository $receiptRepository,
        ParameterBagInterface $parameterBag,
        EntityManagerInterface $em,
    ): JsonResponse {
        if ($em->getFilters()->isEnabled('status_filter')) {
            $em->getFilters()->disable('status_filter');
        }

        $user = $request->get('user') ? $userRepository->find($request->get('user')) : null;
        $itemsPerPage = $request->get('itemsPerPage');
        $pageNumber = $request->get('pageNumber');

        $dateRange = new DateTimeNullableRangeRequest(
            $request->get('dateFrom'),
            $request->get('dateTo'),
            $request->get($parameterBag->get('branding_timezone'))
        );

        $receipts = $receiptRepository->findUserReceipts(
            $user,
            $dateRange->getDateFrom(),
            $dateRange->getDateTo(),
            $pageNumber,
            $itemsPerPage
        );

        return $this->json(
            $receipts,
            JsonResponse::HTTP_OK,
            [],
            [
               'groups' => [
                    'receipt:list',
                    "default:basic",
                    "default:basic",
                    'locale:list'
               ]]
        );
    }
}
