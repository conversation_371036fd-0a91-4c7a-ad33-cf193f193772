<?php

namespace App\Controller\Admin;

use App\PromoCodes\PromotionalCodesGeneratorService;
use App\Repository\PromotionalTopupCodeRepository;
use App\Service\AppConfig\AppConfigService;
use DateTime;
use Exception;
use OpenApi\Annotations as OA;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;

/**
 * Class LoyalAppPromotionalCodesController
 * @package CarwashManagerBundle\Controller
 */
class PromotionalCodesGeneratorController extends AbstractController
{
    public function __construct(
        private PromotionalCodesGeneratorService $promotionalCodesGeneratorService
    ) {
    }

    /**
     * @Route("/api/admin/promotionalcodes", methods={"POST"})
     *
     * @OA\RequestBody(
     *      description= "Generate set of rpomotional codes",
     *      required= true,
     *      content={
     *             @OA\MediaType(
     *                 mediaType="application/json",
     *                 @OA\Schema(
     *                     example={
     *                          "quantity": "10",
     *                          "value": "2.5",
     *                          "name": "Promo",
     *                          "type": "DISPLOSABLE"
     *                      }
     *                  )
     *              )
     *       }
     * )
     *
     * @OA\Response(
     *      response="200",
     *      description="ok",
     *         content={
     *             @OA\MediaType(
     *                 mediaType="application/json",
     *                 @OA\Schema(
     *                     example={}
     *                 )
     *          )
     *       }
     *   )
     *
     * @OA\Response(response="401",description="Unauthorized")
     *
     * @OA\Tag(name="PromotionalCodes")
     */
    public function newAction(
        Request $request,
    ): JsonResponse {
        try {
            $data = json_decode($request->getContent(), true);

            $quantity = (int)$data['quantity'];
            $value = (float)$data['value'];
            $name = $data['name'];
            $type = $data['type'];
            $startDate = $data['startDate'] ?? null;
            $expirationDate = $data['expirationDate'] ?? null;

            if ($startDate) {
                $startDate = new DateTime($startDate);
            }

            if ($expirationDate) {
                $expirationDate = new DateTime($expirationDate);
            }

            $codes = $this->promotionalCodesGeneratorService->generate(
                $quantity,
                $value,
                $name,
                $type,
                $startDate,
                $expirationDate
            );

            if (count($codes) < 1) {
                return new JsonResponse([], 400);
            }

            $data = json_encode(true);

            return new JsonResponse($data, 200, [], true);
        } catch (Exception $e) {
            return new JsonResponse([], 400, []);
        }
    }

    /**
     * @Route("/api/admin/promotionalcodes", methods={"GET"})
     *
     * @OA\Parameter(
     *     name="dateFrom",
     *     in="query",
     *     example="2022-01-01",
     *     description="The field used to order rewards",
     *     @OA\Schema(type="string")
     * )
     * @OA\Parameter(
     *     name="dateTo",
     *     in="query",
     *     description="The field used to order rewards",
     *     @OA\Schema(type="string")
     * )
     * @OA\Parameter(
     *     name="orderBy",
     *     in="query",
     *     description="The field used to order rewards",
     *     @OA\Schema(type="string")
     * )
     * @OA\Parameter(
     *     name="orderDescending",
     *     in="query",
     *     description="1 = descending",
     *     @OA\Schema(type="integer")
     * )
     * @OA\Parameter(
     *     name="page",
     *     in="query",
     *     description="Page number",
     *     @OA\Schema(type="string")
     * )
     * @OA\Parameter(
     *     name="itemsPerPage",
     *     in="query",
     *     description="Page rows limit",
     *     @OA\Schema(type="integer")
     * )
     * @OA\Parameter(
     *     name="fullTextSearch",
     *     in="query",
     *     description="Search",
     *     @OA\Schema(type="string")
     * )
     * @OA\Parameter(
     *     name="codeUsed",
     *     in="query",
     *     description="codeUsed = 1",
     *     @OA\Schema(type="integer")
     * )
     *
     * @OA\Response(
     *      response="200",
     *      description="ok",
     *         content={
     *             @OA\MediaType(
     *                 mediaType="application/json",
     *                 @OA\Schema(
     *                     example={
     *                      "items": {
     *                          {
     *                            "id": 1,
     *                            "code_used": null,
     *                            "value": "1",
     *                            "ctime": "2022-01-24 09:43:02",
     *                            "group_name": "test",
     *                            "promotional_code": "6A4B0-7C6D7",
     *                            "totalRows": 60
     *                          },
     *                      },
     *                      "count": 60
     *                     }
     *                 )
     *          )
     *       }
     *   )
     * @OA\Response(response="401",description="Unauthorized")
     *
     * @OA\Tag(name="PromotionalCodes")
     */
    public function getPromotionalCodeListAction(
        Request $request,
        PromotionalTopupCodeRepository $promotionalTopupCodeRepository,
        AppConfigService $appConfigService
    ): JsonResponse {
        $orderType = null;
        $order = null;
        $orderBy = $request->query->get('orderBy', null);

        if (isset($orderBy)) {
            $descending = $request->query->get('orderDescending');
            $orderType = $descending == 1 ? 'DESC' : 'ASC';

            $orderApiMapping = [
                'id' => 'id',
                'promotional_code' => 'code',
                'startDate' => 'startDate',
                'type' => 'type',
                'value' => 'value',
                'expirationDate' => 'expirationDate',
                'ctime' => 'ctime',
                'group_name' => 'name',
                'code_used' => 'mobile_payment_id'
            ];

            if (array_key_exists($orderBy, $orderApiMapping)) {
                $order = $orderApiMapping[$orderBy];
            }
        }

        [$codes, $count] = $promotionalTopupCodeRepository->findCodesByParameters(
            [
                'sortBy' => $order,
                'sortType' => $orderType,
                'page' => $request->query->get('page'),
                'limit' => $request->query->get('itemsPerPage'),
                'search' => $request->query->get('fullTextSearch'),
                'codeUsed' => $request->query->get('codeUsed'),
                'dateFrom' => $request->query->get('dateFrom'),
                'dateTo' => $request->query->get('dateTo'),
            ],
        );

        foreach ($codes as $key => $data) {
            $codes[$key]['value'] = number_format(
                floatval($codes[$key]['value'])
            )
            . ' ' . $appConfigService->getCurrency()->symbol();
        }

        $data = json_encode(['items' => $codes, 'count' => $count]);

        return new JsonResponse($data, 200, [], true);
    }

    /**
     * @Route("/api/admin/promotionalcode/{codeId}", name="admin_promotionalcode_show", methods={"GET"})
     */
    public function getCodeAction(int $codeId, PromotionalTopupCodeRepository $promotionalTopupCodeRepository): JsonResponse
    {

        $code = $promotionalTopupCodeRepository->findOneBy(['id' => $codeId]);

        if (null === $code) {
            return new JsonResponse([], 204);
        }

        $data = [
            'id' => $code->getId(),
            'code' => $code->getCode(),
            'value' => $code->getValue(),
            'createDate' => 0,
            'user' => $code->getBkfpayUser()->getEmail()
        ];

        $data = json_encode($data);

        return new JsonResponse($data, 200, [], true);
    }
}
