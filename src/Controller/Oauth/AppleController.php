<?php

namespace App\Controller\Oauth;

use KnpU\OAuth2ClientBundle\Client\ClientRegistry;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

class AppleController extends AbstractController
{
    /**
     * @Route("/connect/apple/mobile/check", name="connect_apple_check")
     */
    public function mobileConnectCheckAction(Request $request, ClientRegistry $clientRegistry)
    {
    }
}
