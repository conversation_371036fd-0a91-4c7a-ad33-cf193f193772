<?php

namespace App\Controller\Sticker;

use Detection\MobileDetect;
use Exception;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class QrLinksController extends AbstractController
{
    #[Route('/stand/{code}')]
    public function stand(
        ParameterBagInterface $parameterBag
    ): Response {
        $mobileDetect = new MobileDetect();


        $url = $parameterBag->get('mobile_app.android');
        if ($url && $mobileDetect->isAndroidOS()) {
            return $this->redirect($url);
        }

        $url = $parameterBag->get('mobile_app.ios');
        if ($url && $mobileDetect->isiOS()) {
            return $this->redirect($url);
        }

        return $this->redirect($parameterBag->get('front_url'));
    }
}
