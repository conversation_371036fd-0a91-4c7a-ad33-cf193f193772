<?php

namespace App\Fixtures;

use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Persistence\ObjectManager;
use Nelmio\Alice\Loader\NativeLoader;
use Symfony\Component\Finder\Finder;
use Symfony\Component\Finder\SplFileInfo;

class FixturesLoader extends Fixture
{
    private const FIXTURES_DIR = 'fixtures';

    private string $projectDir;
    private LoadMobilePaymentsData $loadMobilePaymentsData;

    /**
     * FixturesLoader constructor.
     *
     */
    public function __construct(string $projectDir, LoadMobilePaymentsData $loadMobilePaymentsData)
    {
        $this->projectDir = $projectDir;
        $this->loadMobilePaymentsData = $loadMobilePaymentsData;
    }

    /**
     * @throws \Nelmio\Alice\Throwable\LoadingThrowable
     */
    public function load(ObjectManager $manager): void
    {
        $files = $this->findFiles();

        $loader = new NativeLoader();
        $objectSet = $loader->loadFiles($files)->getObjects();

        foreach ($objectSet as $object) {
            $manager->persist($object);
        }

        $manager->flush();

        $this->loadMobilePaymentsData->load(
            $manager
        );
    }

    private function findFiles(): array
    {
        $finder = new Finder();
        $finder
            ->files()
            ->in($this->projectDir . '/' . self::FIXTURES_DIR)
            ->name('/\.yaml$/')
            ->sortByName();

        $files = [];
        /** @var SplFileInfo $file */
        foreach ($finder as $file) {
            $files[] = $file->getRealPath();
        }

        return $files;
    }
}
