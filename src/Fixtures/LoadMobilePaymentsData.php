<?php

namespace App\Fixtures;

use App\Entity\Carwash;
use App\Entity\Enum\Issuer;
use App\Entity\Enum\MobilePaymentStatus;
use App\Entity\Enum\TransactionType;
use App\Entity\Invoices;
use App\Entity\MobilePayment;
use App\Entity\User;
use App\Repository\UserRepository;
use Doctrine\Persistence\ObjectManager;

/**
 * Class LoadMobilePaymentsData
 *
 * @package BKFPayBundle\DataFixtures\ORM
 */
class LoadMobilePaymentsData
{
    /**
     * Load data fixtures with the passed EntityManager
     *
     */
    public function load(ObjectManager $manager)
    {

        $carwashRepository = $manager->getRepository(Carwash::class);
        /** @var UserRepository $userRepository */
        $userRepository = $manager->getRepository(User::class);
        $invoicesRepository = $manager->getRepository(Invoices::class);

        $carwash = $carwashRepository->findOneBy(['serialNumber' => 141]);
        $carwash2 = $carwashRepository->findOneBy(['serialNumber' => 142]);

        $userRepository->findAll();

        $user1 = $userRepository->findOneByEmail('<EMAIL>');
        $userRepository->findOneByEmail('<EMAIL>');
        $user3 = $userRepository->findOneByEmail('<EMAIL>');
        $user4 = $userRepository->findOneByEmail('<EMAIL>');



        $invoicesRepository->findOneBy(['id' => 1]);
        $invoicesRepository->findOneBy(['id' => 2]);
        $invoicesRepository->findOneBy(['id' => 3]);

        $mobilePayment = new MobilePayment();
        $mobilePayment
            ->setIssuer(Issuer::BKF_PAY)
            ->setCarwash($carwash)
            ->setBkfpayCompany($user4)
            ->setBkfpayUser($user4)
            ->setConfirmedTimestamp(new \DateTime("2017-10-24T11:03:39+0000"))
            ->setInitiatedTimestamp(new \DateTime("2017-10-24T11:03:39+0000"))
            ->setStatus(MobilePaymentStatus::CONFIRMED)
            ->setStandCode('00141017')
            ->setValue(-0.012)
            ->setTransactionType(TransactionType::PAYMENT)
            ->setMtime(new \DateTime("2017-10-24T11:03:39+0000"))
            ;

        $manager->persist($mobilePayment);

        $mobilePayment2 = new MobilePayment();
        $mobilePayment2
            ->setIssuer(Issuer::BKF_PAY)
            ->setCarwash($carwash2)
            ->setBkfpayCompany($user4)
            ->setBkfpayUser($user4)
            ->setConfirmedTimestamp(new \DateTime("2017-10-24T11:03:39+0000"))
            ->setInitiatedTimestamp(new \DateTime("2017-10-24T11:03:39+0000"))
            ->setStatus(MobilePaymentStatus::CONFIRMED)
            ->setStandCode('00141017')
            ->setValue(-0.0144)
            ->setTransactionType(TransactionType::PAYMENT)
            ->setMtime(new \DateTime("2017-10-24T11:03:39+0000"))
        ;

        $manager->persist($mobilePayment2);

        $mobilePayment3 = new MobilePayment();
        $mobilePayment3
            ->setIssuer(Issuer::BKF_PAY)
            ->setCarwash($carwash2)
            ->setBkfpayCompany($user1)
            ->setBkfpayUser($user1)
            ->setConfirmedTimestamp(new \DateTime("2017-10-02T11:03:39+0000"))
            ->setInitiatedTimestamp(new \DateTime("2017-10-02T11:03:39+0000"))
            ->setStatus(MobilePaymentStatus::CONFIRMED)
            ->setStandCode('00141017')
            ->setValue(-0.96)
            ->setTransactionType(TransactionType::PAYMENT)
            ->setMtime(new \DateTime("2017-10-02T11:03:39+0000"))
        ;

        $manager->persist($mobilePayment3);

        $mobilePayment4 = new MobilePayment();
        $mobilePayment4
            ->setIssuer(Issuer::BKF_PAY)
            ->setCarwash($carwash2)
            ->setBkfpayCompany($user1)
            ->setBkfpayUser($user1)
            ->setInitiatedTimestamp(new \DateTime("2017-10-02T11:03:39+0000"))
            ->setStatus(MobilePaymentStatus::TIMEOUT)
            ->setStandCode('00141017')
            ->setValue(-0.96)
            ->setTransactionType(TransactionType::PAYMENT)
            ->setMtime(new \DateTime("2017-10-02T11:03:39+0000"))
        ;

        $manager->persist($mobilePayment4);

        $topUp = new MobilePayment();
        $topUp
            ->setIssuer(Issuer::BKF_PAY)
            ->setCarwash($carwash2)
            ->setBkfpayCompany($user1)
            ->setBkfpayUser($user1)
            ->setConfirmedTimestamp(new \DateTime("2017-10-02T11:03:39+0000"))
            ->setInitiatedTimestamp(new \DateTime("2017-10-02T11:03:39+0000"))
            ->setStatus(MobilePaymentStatus::CONFIRMED)
            ->setValue(1.60)
            ->setTransactionType(TransactionType::TOP_UP)
            ->setMtime(new \DateTime("2017-10-02T11:03:39+0000"))
        ;

        $manager->persist($topUp);

        $topUp2 = new MobilePayment();
        $topUp2
            ->setIssuer(Issuer::BKF_PAY)
            ->setCarwash($carwash2)
            ->setBkfpayCompany($user4)
            ->setBkfpayUser($user4)
            ->setConfirmedTimestamp(new \DateTime("2017-10-02T11:03:39+0000"))
            ->setInitiatedTimestamp(new \DateTime("2017-10-02T11:03:39+0000"))
            ->setStatus(MobilePaymentStatus::CONFIRMED)
            ->setValue(1.60)
            ->setTransactionType(TransactionType::TOP_UP)
            ->setMtime(new \DateTime("2017-10-02T11:03:39+0000"))
        ;

        $manager->persist($topUp2);

        $topUp3 = new MobilePayment();
        $topUp3
            ->setIssuer(Issuer::BKF_PAY)
            ->setCarwash($carwash2)
            ->setBkfpayCompany($user3)
            ->setBkfpayUser($user3)
            ->setInitiatedTimestamp(new \DateTime("2017-10-02T11:03:39+0000"))
            ->setStatus(MobilePaymentStatus::TIMEOUT)
            ->setValue(1300)
            ->setTransactionType(TransactionType::TOP_UP)
            ->setMtime(new \DateTime("2017-10-02T11:03:39+0000"))
        ;

        $manager->persist($topUp3);

        $manager->flush();
    }
}
