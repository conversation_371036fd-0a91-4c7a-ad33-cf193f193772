<?php

namespace App\ExternalConnectors\PayEx;

use App\MobilePayment\MobilePaymentSecurity\VerifyMobilePaymentRequest;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Symfony\Component\HttpFoundation\Request;

/**
 * Class PayExConnector
 * @package App\ExternalConnectors\PayEx
 */
class PayExConnector
{
    private const HTTP_ALLOWED_METHODS = [
        Request::METHOD_POST,
        Request::METHOD_GET,
        Request::METHOD_PATCH
    ];

    protected string $merchantToken;
    protected string $merchantId;
    protected string $payeeId;
    protected HttpClientInterface $httpClient;
    protected VerifyMobilePaymentRequest $verifyMobilePaymentRequest;
    private string $apiUrl; // = 'https://api.payex.com';
    private string $hostUrl;
    private string $brandingAssets;
    /**
     * PayExConnector constructor.
     */
    public function __construct(
        HttpClientInterface $httpClient,
        VerifyMobilePaymentRequest $verifyMobilePaymentRequest,
        ParameterBagInterface $parameterBag
    ) {
        $this->merchantToken = $parameterBag->get('pay_ex_merchant_token');
        $this->merchantId = $parameterBag->get('pay_ex_merchant_id');
        $this->payeeId = $parameterBag->get('pay_ex_payee_id');
        $this->httpClient = $httpClient;
        $this->verifyMobilePaymentRequest = $verifyMobilePaymentRequest;
        $this->apiUrl = $parameterBag->get('pay_ex_api_url');
        $this->hostUrl = $parameterBag->get('web_url');
        $this->brandingAssets = $parameterBag->get('brandingAssets');
    }

    /**
     * Method is used to get resource data from payEx api
     *
     *
     * @param string $resourceId - psp/creditcard/payments
     */
    public function getResourceData(string $resourceId): array
    {
        return $this->sendRequest(
            '',
            $resourceId . '?$expand=[authorizations|verifications]',
            Request::METHOD_GET
        );
    }

    /**
     * get masked Credit Cart data based on credit card token
     * @param string $recurrenceToken
     * @return array credit card token details
     */
    public function getCreditCardInfo($recurrenceToken): array
    {
        return $this->sendRequest(
            '',
            '/psp/creditcard/payments/instrumentData/' . $recurrenceToken,
            Request::METHOD_GET
        );
    }


    public function deleteCreditCardToken($recurrenceToken): bool
    {
        $data = [
            "state"     => "Deleted",
            "tokenType" => "RecurrenceToken",
            "comment"   => "Request by client",
        ];

        $payload = json_encode($data);

        $returnData = $this->sendRequest(
            $payload,
            '/psp/creditcard/payments/instrumentData/' . $recurrenceToken,
            Request::METHOD_PATCH
        );

        if ($returnData['status'] == 200) {
            return ($returnData['content']['instrumentData']['isDeleted']);
        }

        return false;
    }

    /**
     * Makes single payment to verify and store credit cards data on PayEx server
     * (parameter generateRecurrenceToken: true is required).
     *
     * In response content in operations section "redirect-verification" url will by presented.
     * Redirect client this site this url to fulfill all necessary credit card data.
     * After client action one PayEx site, browser will by redirect to (cancelUrl or completeUrl).
     * Also callbackUrl should by
     *
     */
    public function addCreditCard(
        $callbackUrl,
        $successUrl,
        $cancelUrl
    ): array {
        $data = [
            'payment' => [
                'operation' => 'Verify',
                'intent' => 'AutoCapture',
                'paymentToken' => '',
                'currency' => 'SEK',
                'prices' => [
                    [
                        'type' => 'CreditCard',
                        'amount' => 100,
                        'vatAmount' => 0 //@TODO
                    ]
                ],
                'description' => 'Verification Purchase',
                'payerReference' => 'AB1234',
                'generatePaymentToken' => false,
                'generateRecurrenceToken' => true,
                'userAgent' => 'Mozilla/5.0...',
                'language' => 'sv-SE',
                'urls' => [
                    'hostUrls' => [
                        $this->hostUrl,
                    ],
                    'completeUrl' => $successUrl,
                    'cancelUrl' => $cancelUrl,
                    'callbackUrl' => $callbackUrl,
                    'logoUrl' => $this->hostUrl . '/' . $this->brandingAssets . '/logo.png',
                    'termsOfServiceUrl' => $this->hostUrl . '/terms/of_use'
                ],
                'payeeInfo' => [
                    'payeeId' => 'e46ac605-2ee4-43d1-835c-f9f8c3f3d9c9',
                    'payeeReference' => $this->generateRandomString(12),
                    'payeeName' => 'Washstop Scandinavia AB',
                    'orderReference' => 'veryfication-' . $this->generateRandomString(12),
                ],
            ],
            'creditCard' => [
               'no3DSecure' => false,
               'mailOrderTelephoneOrder' => false,
               'rejectCardNot3DSecureEnrolled' => false,
               'rejectCreditCards' => false,
               'rejectDebitCards' => false,
               'rejectConsumerCards' => false,
               'rejectCorporateCards' => false,
               'rejectAuthenticationStatusA' => false,
               'rejectAuthenticationStatusU' => false
            ]
        ];

        $payload = json_encode($data);

        return $this->sendRequest($payload, '/psp/creditcard/payments', Request::METHOD_POST);
    }

    /**
     * Charge card identified by $cardRecurenceToken
     * Create recurrence payment
     */
    public function chargeToken(
        $cardRecurrenceToken,
        $amount,
        $callbackUrl,
        $successUrl
    ): array {
        $data = [
            'payment' => [
                'operation' => 'Recur',
                'intent' => 'AutoCapture',
                "recurrenceToken" => $cardRecurrenceToken,
                "currency" => 'SEK',
                'userAgent' => 'Mozilla/5.0...',
                "amount" => $amount,
                "vatAmount" => 0,
                "description" => "Payment",
                "language" => "sv-SE",
                "urls" => [
                    // 'hostUrls' => [
                    //     [ 'https://beloyal24.com' ]
                    // ],
                    'completeUrl' => $successUrl,
                    'callbackUrl' => $callbackUrl,
                ],
                'payeeInfo' => [
                    'payeeId' => 'e46ac605-2ee4-43d1-835c-f9f8c3f3d9c9',
                    'payeeReference' => $this->generateRandomString(12),
                    'payeeName' => 'Washstop Scandinavia AB', // @TODO to config
                    'orderReference' => 'or-' . $this->generateRandomString(12), // id zamowienia z be loyal'a np. id płatności
                ]
            ]
        ];

        $payload = json_encode($data);

        return $this->sendRequest($payload, '/psp/creditcard/payments', Request::METHOD_POST);
    }

    /**
     * Send request to specified method with some params
     * This method is only used with other methods
     * This is only interface to call other methods
     *
     */
    private function sendRequest(string $payload, string $apiMethod, string $requestMethod): array
    {
        if (!in_array($requestMethod, self::HTTP_ALLOWED_METHODS)) {
            return ['status' => false, 'result' => null];
        }

        $response = $this->httpClient->request(
            $requestMethod,
            $this->apiUrl . $apiMethod,
            [
                'body' => $payload,
                'headers' => [
                    'Authorization: Bearer ' . $this->merchantToken,
                    'Content-Type: application/json; charset=utf-8',
                    'Accept: application/json'
                ]
            ]
        );

        return [
            'status' => $response->getStatusCode(),
            'content' => json_decode($response->getContent(), true)
        ];
    }

    /**
     * Helper method used to generate random alfa-num strings
     */
    private function generateRandomString(int $length): string
    {
        return substr(str_shuffle(md5((string)time())), 0, $length);
    }
}
