<?php

namespace App\ExternalConnectors\CreditCard;

use Carbon\Carbon;
use Exception;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\DecodingExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;

/**
 * Class EspagoConnector
 *
 * @package App\ExternalPayment\Connector\EspagoConnector
 *
 * Connector to make payments using Espago Api
 * https://developers.espago.com/pl/v3
 *
 * This was implemented for api version 3.0
 */
class EspagoConnector
{
    private HttpClientInterface $httpClient;
    private string $gatewayUrl;
    private string $espagoCurrency;
    private string $espagoId;
    private string $espagoPassword;
    private const METHOD_CHARGE = 'charges';
    private const METHOD_CLIENTS = 'clients';
    private const METHOD_TOKENS = 'tokens';

    public function __construct(HttpClientInterface $httpClient, ParameterBagInterface $parameterBag)
    {
        $this->httpClient = $httpClient;

        $this->gatewayUrl = $parameterBag->get('espago_gateway');
        $this->espagoCurrency = $parameterBag->get('espago_currency');
        $this->espagoId = $parameterBag->get('espago_id');
        $this->espagoPassword = $parameterBag->get('espago_password');
    }

    public function refund($externalId, float $amount = null)
    {
        $method = "charges/$externalId/refund";
        $data = [];

        if ($amount !== null) {
            $data['amount'] = $amount;
        }

        return $this->sendRequest($method, $data);
    }

    public function refundBeforeClearance($externalId)
    {
        $method = "charges/$externalId";

        return $this->sendRequest($method, [], 'DELETE');
    }

    /**
     * Make single payment(/api/charges)
     * This payment is
     *
     * @return array
     * @throws ClientExceptionInterface
     * @throws DecodingExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     */
    public function charge(
        $clientId,
        $card,
        $amount,
        $description,
        $skip3DSecure = false,
        ?string $email = null,
        ?string $clientDescription = null,
    ) {
        $requestParams = [
            'amount'      => sprintf("%.2f", (float)$amount),
            'currency'    => $this->espagoCurrency,
            'description' => $description,
            'complete'    => true,
            'skip_3ds'    => $skip3DSecure,
            //'positive_url' => "{$this->webUrl}/external_payment/espago/success",
            //'negative_url'  => "{$this->webUrl}/external_payment/espago/error",
        ];

        if (!empty($email)) {
            $requestParams['email'] = $email;
            $requestParams['skip_email'] = true;
        }

        if (!empty($clientDescription)) {
            $requestParams['client_description'] = $clientDescription;
        }

        if (!empty($clientId)) {
            $requestParams['client'] = $clientId;
        }

        if (!empty($card)) {
            $requestParams['card'] = $card;
        }

        return $this->sendRequest(self::METHOD_CHARGE, $requestParams);
    }

    /**
     * Send request to specified method with some params
     * This method is only used with other methods
     * This is only interface to call other methods
     *
     * @throws ClientExceptionInterface
     * @throws DecodingExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     */
    private function sendRequest($method, array $requestParams, string $httpMethod = 'POST')
    {
        try {
            $response = $this->httpClient->request(
                $httpMethod,
                $this->gatewayUrl . '/api/' . $method,
                [
                    'body'       => $requestParams,
                    'auth_basic' => [$this->espagoId, $this->espagoPassword],
                    'headers'    => [
                        'Accept' => 'application/vnd.espago.v3+json',
                    ],
                ]
            );

            $data['status'] = $response->getStatusCode();
            $data['content'] = json_decode($response->getContent(), true);
        } catch (Exception $exception) {
            $data = ['status' => false, 'content' => null];
        }

        return $data;
    }

    /**
     * Function registers card details
     *
     *
     * @return array response data
     */
    public function registerCreditCard(
        string $cardFirstName,
        string $cardLastName,
        string $cardNumber,
        string $verificationCode,
        int $cardYear,
        int $cardMonth,
        ?string $description = null
    ) {
        $requestParams = [
            'card' => [
                'first_name'         => $cardFirstName,
                'last_name'          => $cardLastName,
                'number'             => $cardNumber,
                'verification_value' => $verificationCode,
                'year'               => $cardYear,
                'month'              => $cardMonth,
                'description'        => $description
            ],
        ];

        return $this->sendRequest(self::METHOD_TOKENS, $requestParams);
    }

    /**
     * Function registers card details
     *
     *
     * @return array response data
     */
    public function registerClientToken(
        string $token,
        ?string $description = null,
        ?string $email = null
    ) {
        $requestParams = [
            'card'  => $token,
            'email' => $email,
            'description' => $description
        ];
        return $this->sendRequest(self::METHOD_CLIENTS, $requestParams);
    }
}
