<?php

namespace App\Request\Traits;

use Symfony\Component\Validator\Constraints as Assert;

trait HasExtraParams
{
    /**
     * @Assert\Type("string")
     */
    private string $extraParams = '';

    public function getExtraParams(): string
    {
        return $this->extraParams;
    }

    public function setExtraParams(string $extraParams): void
    {
        $this->extraParams = $extraParams;
    }
}
