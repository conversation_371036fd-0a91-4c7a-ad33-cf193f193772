<?php

namespace App\Request\Traits;

use DateInterval;
use DateTime;
use Symfony\Component\Validator\Constraints as Assert;

trait HasDatesWithInterval
{
    /**
     * @Assert\DateTime()
     */
    private DateTime $dateTo;

    /**
     * @Assert\Type("DateInterval")
     */
    private DateInterval $interval;

    public function __construct(?DateTime $dateTo = null, ?DateInterval $interval = null)
    {
        $this->setDateTo($dateTo);
        $this->setInterval($interval);
    }

    public function getDateFrom(): DateTime
    {
        $dateFrom = clone $this->getDateTo();
        return $dateFrom->sub($this->getInterval())
            ->add(new DateInterval('P1D'))
            ->setTime(0, 0);
    }

    public function getDateTo(): DateTime
    {
        $this->dateTo->setTime(23, 59, 59, 999999);
        return $this->dateTo;
    }

    public function setDateTo(?DateTime $dateTo): void
    {
        $this->dateTo = $dateTo ?? new DateTime('now');
    }

    public function getInterval(): DateInterval
    {
        return $this->interval;
    }

    public function setInterval(?DateInterval $interval): void
    {
        $this->interval = $interval ?? new DateInterval('P1M');
    }
}
