<?php

namespace App\Request\Invoices;

use Symfony\Component\Validator\Constraints as Assert;

class GetInvoiceActionDTO
{
    /**
     * @Assert\Type("bool")
     */
    private bool $auto_login_file_url = true;

    /**
     * @return mixed
     */
    public function getAutoLoginFileUrl()
    {
        return $this->auto_login_file_url;
    }

    /**
     * @param mixed $auto_login_file_url
     */
    public function setAutoLoginFileUrl($auto_login_file_url): void
    {
        $this->auto_login_file_url = $auto_login_file_url;
    }
}
