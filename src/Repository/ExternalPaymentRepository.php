<?php

namespace App\Repository;

use App\Entity\ExternalPayment;
use App\Entity\ExternalPaymentLog;
use App\Entity\MobilePayment;
use App\Entity\User;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use I2m\Payment\Enum\Status;

/**
 * Class ExternalPaymentRepository
 * @package App\Entity\Repository
 * @method ExternalPayment|null find($id, $lockMode = null, $lockVersion = null)
 * @method ExternalPayment|null findOneBy(array $criteria, array $orderBy = null)
 */
class ExternalPaymentRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, ExternalPayment::class);
    }

    public function init(?MobilePayment $mp, User $user, string $issuer, ?float $value, ?array $additionalData = null, ?string $transactionId = null): ExternalPayment
    {

        $externalPayment = (new ExternalPayment())
           ->setStatus(Status::INITIATED)
           ->setMobilePayment($mp)
           ->setIssuer($issuer)
           ->setInitiatedTimestamp(new \DateTime())
           ->setUser($user)
           ->setValue($value);



        if ($additionalData) {
            $externalPayment->setAdditionalData($additionalData);
        }

        if ($transactionId) {
            $externalPayment->setExternalId($transactionId);
        }

        $this->getEntityManager()->persist($externalPayment);
        $this->getEntityManager()->flush();

        $this->log($externalPayment, "ep created", $additionalData);


        if ($mp) {
            $this->getEntityManager()->refresh($mp); // powiedz mp ze pojawilo sie ep dla niego
        }
        return $externalPayment;
    }

    public function confirm(ExternalPayment $ep, ?string $externalId = null, ?array $additionalData = null): ExternalPayment
    {
        $ep
            ->setConfirmedTimestamp(new \DateTime())
            ->setStatus(Status::CONFIRMED)
            ;

        if ($externalId) {
            $ep->setExternalId($externalId);
        }
        $this->log($ep, "ep confirm", $additionalData);

        $this->getEntityManager()->flush();
        return $ep;
    }

    public function reject(ExternalPayment $ep, ?string $externalId = null, ?array $additionalData = null): ExternalPayment
    {
        $ep
            ->setStatus(Status::REJECTED)
        ;

        if ($additionalData) {
            $ep->setAdditionalData($additionalData);
        }

        if ($externalId) {
            $ep->setExternalId($externalId);
        }
        $this->log($ep, "ep reject", $additionalData);
        $this->getEntityManager()->flush();
        return $ep;
    }

    public function refund(ExternalPayment $ep): ExternalPayment
    {
        $ep
            ->setStatus(Status::REFUNDED)
        ;
        $this->getEntityManager()->flush();
        return $ep;
    }

    public function setAdditionalData($ep, ?array $additionalData = null): ExternalPayment
    {
        if ($additionalData) {
            $ep->setAdditionalData($additionalData);
        }
        $this->log($ep, "ep setAdditionalData", $additionalData);
        $this->getEntityManager()->flush();
        return $ep;
    }

    public function wait(ExternalPayment $ep, ?string $externalId = null, ?array $additionalData = null): ExternalPayment
    {
        $ep
            ->setStatus(Status::WAITING)
        ;

        if ($additionalData) {
            $ep->setAdditionalData($additionalData);
        }

        if ($externalId) {
            $ep->setExternalId($externalId);
        }
        $this->log($ep, "ep wait", $additionalData);
        $this->getEntityManager()->flush();
        return $ep;
    }

    public function findByIssuer(string $issuer, string $transactionId): ExternalPayment
    {
        $ep = $this->findOneBy(['externalId' => $transactionId, 'issuer' => $issuer]);
        if (is_null($ep)) {
            throw new \Exception("nie znaleziono płatności $issuer $transactionId");
        }
        return $ep;
    }

    public function log(ExternalPayment $ep, string $comment, ?array $info = null)
    {
        $log =
            (new ExternalPaymentLog())
                ->setExternalPayment($ep)
                ->setCtime(new \DateTime())
                ->setComment($comment)
                ->setAddInfo($info);

        $this->getEntityManager()->persist($log);
        $this->getEntityManager()->flush();
    }

    public function findPaymentsByDateRangeAndUser(
        \DateTimeInterface $startDate = null,
        \DateTimeInterface $endDate = null,
        ?array $statusArray = null,
        ?array $issuer = null,
        int $page = 1,
        int $perPage = 50,
        ?string $search = null
    ): array {
        $baseQuery = $this->createQueryBuilder('ep')
            ->leftJoin('ep.mobilePayment', 'mp')
            ->leftJoin('mp.invoice', 'i')
            ->leftJoin('mp.bkfpayUser', 'u1')
            ->leftJoin('mp.bkfpayCompany', 'u2')
        ;

        if ($startDate) {
            $baseQuery
                ->andWhere('ep.initiatedTimestamp >= :start_date')
                ->setParameter('start_date', $startDate);
        }

        if ($endDate) {
            $baseQuery
                ->andWhere('ep.initiatedTimestamp <= :end_date')
                ->setParameter('end_date', $endDate);
        }

        if ($search) {
            $orX = $baseQuery->expr()->orX();

            $orX->add($baseQuery->expr()->like('LOWER(u1.email)', ':search'));
            $orX->add($baseQuery->expr()->like('LOWER(u2.email)', ':search'));
            $orX->add($baseQuery->expr()->like('LOWER(ep.externalId)', ':search'));
            $orX->add($baseQuery->expr()->like('LOWER(ep.issuer)', ':search'));
            $orX->add($baseQuery->expr()->like('LOWER(i.number)', ':search'));
            $orX->add($baseQuery->expr()->like('LOWER(mp.licensePlate)', ':search'));

            if (is_numeric($search)) {
                $orX->add($baseQuery->expr()->eq('ep.id', ':searchInt'));
                $baseQuery->setParameter('searchInt', $search);
            }

            $baseQuery->andWhere($orX)
                ->setParameter('search', '%' . strtolower($search) . '%');
        }

        $issuerQuery = clone $baseQuery;
        $issuerQuery->select('ep.issuer as name, count(ep.id) as count')
            ->groupBy('ep.issuer')
        ;

        $statusQuery = clone $baseQuery;
        $statusQuery->select('ep.status as name, count(ep.id) as count')
            ->andWhere('ep.status is not null')
            ->groupBy('ep.status')
        ;


        if ($statusArray) {
            $baseQuery
                ->andWhere('ep.status IN (:statuses)')
                ->setParameter('statuses', $statusArray);
        }

        if ($issuer) {
            $baseQuery
                ->andWhere('ep.issuer IN (:issuer)')
                ->setParameter('issuer', $issuer);
        }

        $countQuery = clone $baseQuery;
        $countQuery->select("COUNT(ep.id) as count, SUM(ep.value) as sum");
        $summary = $countQuery->getQuery()->getScalarResult();

        $baseQuery->orderBy('ep.id', 'DESC');
        $baseQuery->setFirstResult(($page - 1) * $perPage)
            ->setMaxResults($perPage);

        return [
            'totalItems' => $summary[0]['count'],
            'sum' => (int) $summary[0]['sum'],
            'items' => $baseQuery->getQuery()->getResult(),
            'issuers' => $issuerQuery->getQuery()->getArrayResult(),
            'statuses' => $statusQuery->getQuery()->getArrayResult(),
        ];
    }

    public function save(ExternalPayment $ep): ExternalPayment
    {
        $this->getEntityManager()->persist($ep);
        $this->getEntityManager()->flush();
        return $ep;
    }

    public function findMissingSalesDocs(
        \DateTimeInterface $startDate = null,
        \DateTimeInterface $endDate = null,
        ?int $maxResult = null
    ) {
        $qb = $this
            ->createQueryBuilder('ep');
        $baseQuery = $qb
            ->leftJoin('ep.mobilePayment', 'mp')
            ->leftJoin('mp.invoice', 'i')
            ->leftJoin('mp.receipt', 'r')
            ->andWhere("ep.status = :status")
            ->setParameter("status", Status::CONFIRMED->value)
            ->andWhere(
                $qb->expr()->andX(
                    $qb->expr()->isNull('i.id'),
                    $qb->expr()->isNull('r.id'),
                )
            )

        ;

        // jeśli chcesz dodać dodatkowe warunki dotyczące daty, możesz to zrobić tutaj
        if ($startDate) {
            $baseQuery->andWhere('ep.confirmedTimestamp >= :startDate')
                ->setParameter('startDate', $startDate);
        }
        if ($endDate) {
            $baseQuery->andWhere('ep.confirmedTimestamp <= :endDate')
                ->setParameter('endDate', $endDate);
        }
        $baseQuery->orderBy("ep.id", "ASC");

        if ($maxResult) {
            $baseQuery->setMaxResults($maxResult);
        };

        return $baseQuery->getQuery()->getResult();
    }
}
