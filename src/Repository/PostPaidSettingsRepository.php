<?php

namespace App\Repository;

use App\Entity\PostPaidSettings;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<PostPaidSettings>
 *
 * @method PostPaidSettings|null find($id, $lockMode = null, $lockVersion = null)
 * @method PostPaidSettings|null findOneBy(array $criteria, array $orderBy = null)
 * @method PostPaidSettings[]    findAll()
 * @method PostPaidSettings[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class PostPaidSettingsRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, PostPaidSettings::class);
    }

    public function add(PostPaidSettings $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(PostPaidSettings $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
