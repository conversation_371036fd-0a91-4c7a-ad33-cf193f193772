<?php

namespace App\Repository;

use App\Entity\MobilePayment;
use App\Entity\Receipt;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use App\Entity\User;

use function Sentry\captureMessage;

class ReceiptRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Receipt::class);
    }

    /**
     * @throws \Doctrine\ORM\NonUniqueResultException
     */
    public function buildReceiptEntity(MobilePayment $mobilePayment, array $paragon): ?Receipt
    {
        $receipt = new Receipt();

        $receipt
            ->setUser($mobilePayment->getBkfpayUser())
            ->setMobilePayment($mobilePayment)
            ->setResponse($paragon)
        ;

        $checkIfExist = $this->findOneBy([
            'uid' => $receipt->getUid(),
            'number' => $receipt->getNumber()
                      ]);

        if ($checkIfExist) {
            return null;
        }


        $this->getEntityManager()->persist($receipt);
        $mobilePayment->setReceipt($receipt);

        $this->getEntityManager()->flush();
        $this->getEntityManager()->refresh($mobilePayment);
        return $receipt;
    }

    public function findUserReceipts(?User $user = null, ?\DateTimeInterface $startDate = null, ?\DateTimeInterface $endDate = null, ?int $page = null, ?int $limit = null): array
    {
        $baseQuery = $this->createQueryBuilder('r');


        if ($user !== null) {
            $baseQuery
                ->andWhere('r.user = :user')
                ->setParameter('user', $user);
        }

        if ($startDate !== null && $endDate !== null) {
            $baseQuery
                ->andWhere('r.time >= :startDate')
                ->andWhere('r.time <= :endDate')
                ->setParameter('startDate', $startDate)
                ->setParameter('endDate', $endDate);
        }

        $countQuery = clone $baseQuery;
        $countQuery->select("COUNT(r.id) as count, sum(r.gross) as gross, sum(r.vat) as vat");
        $summary = $countQuery->getQuery()->getScalarResult();

        if ($page !== null && $limit !== null) {
            $baseQuery
                ->setFirstResult(($page - 1) * $limit)
                ->setMaxResults($limit);
        }
        $baseQuery->orderBy('r.id', 'DESC');


        return [
            "items" => $baseQuery->getQuery()->getResult(),
            'totalItems' => (int) $summary[0]['count'],
            'gross' => (float) $summary[0]['gross'],
            'vat' => (float) $summary[0]['vat'],
        ];
    }
}
