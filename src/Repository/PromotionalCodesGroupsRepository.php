<?php

namespace App\Repository;

use App\Entity\PromotionalCodesGroup;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

    /**
     * @method PromotionalCodesGroup|null find($id, $lockMode = null, $lockVersion = null)
     * @method PromotionalCodesGroup|null findOneBy(array $criteria, array $orderBy = null)
     * @method PromotionalCodesGroup[]    findAll()
     * @method PromotionalCodesGroup[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
     */
class PromotionalCodesGroupsRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, PromotionalCodesGroup::class);
    }

    // /**
    //  * @return Languages[] Returns an array of Languages objects
    //  */
    /*
    public function findByExampleField($value)
    {
        return $this->createQueryBuilder('l')
            ->andWhere('l.exampleField = :val')
            ->setParameter('val', $value)
            ->orderBy('l.id', 'ASC')
            ->setMaxResults(10)
            ->getQuery()
            ->getResult()
        ;
    }
    */

    /*
    public function findOneBySomeField($value): ?Languages
    {
        return $this->createQueryBuilder('l')
            ->andWhere('l.exampleField = :val')
            ->setParameter('val', $value)
            ->getQuery()
            ->getOneOrNullResult()
        ;
    }
    */
}
