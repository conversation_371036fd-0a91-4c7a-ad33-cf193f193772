<?php

namespace App\Repository;

use App\Entity\Currency;
use App\Entity\MobilePaymentSubscriptionPackage;
use App\Service\AppConfig\AppConfigService;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<MobilePaymentSubscriptionPackage>
 */
class MobilePaymentSubscriptionPackageRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry, private AppConfigService $appConfigService)
    {
        parent::__construct($registry, MobilePaymentSubscriptionPackage::class);
    }

    public function getList(
        int $page = 1,
        int $itemsPerPage = 10,
        ?bool $showTotal = true,
        ?string $search = null,
        ?array $statuses = null,
    ) {
        $baseQuery = $this->createQueryBuilder('mps')
            ->leftJoin('mps.user', 'us')
            ->leftJoin('mps.fleet', 'fl')
            ->leftJoin('mps.package', 'p')
        ;

        if ($search) {
            $orX = $baseQuery->expr()->orX();

            $orX->add($baseQuery->expr()->like('LOWER(us.email)', ':search'));
            $orX->add($baseQuery->expr()->like('LOWER(fl.email)', ':search'));
            $orX->add($baseQuery->expr()->like('LOWER(p.title)', ':search'));

            $baseQuery->andWhere($orX)
                ->setParameter('search', '%' . strtolower($search) . '%');

            if (is_numeric($search)) {
                $orX->add($baseQuery->expr()->eq('mps.id', ':searchInt'));
                $baseQuery->setParameter('searchInt', (int)$search);
            }
        }

        $statusQuery = clone $baseQuery;
        $statusQuery->select('mps.status as name')
            ->andWhere('mps.status is not null')
            ->groupBy('mps.status')
        ;

        if ($statuses) {
            $baseQuery
                ->andWhere('mps.status IN (:statuses)')
                ->setParameter('statuses', $statuses);
        }

        $total = null;
        if ($showTotal) {
            $countQuery = clone $baseQuery;
            $countQuery->select("COUNT(mps.id) as count");
            $summary = $countQuery->getQuery()->getScalarResult();

            $total = $summary[0]['count'];
        }

        $baseQuery
            ->orderBy('mps.id', 'DESC')
            ->setFirstResult(($page - 1) * $itemsPerPage)
            ->setMaxResults($itemsPerPage);

        return [
            'totalItems' => $total,
            'items' => $baseQuery->getQuery()->getResult(),
            'statuses' => $statusQuery->getQuery()->getArrayResult(),
            'currency' => $this->appConfigService->getCurrency2()
        ];
    }

    //    /**
    //     * @return MobilePaymentSubscriptionPackage[] Returns an array of MobilePaymentSubscriptionPackage objects
    //     */
    //    public function findByExampleField($value): array
    //    {
    //        return $this->createQueryBuilder('m')
    //            ->andWhere('m.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->orderBy('m.id', 'ASC')
    //            ->setMaxResults(10)
    //            ->getQuery()
    //            ->getResult()
    //        ;
    //    }

    //    public function findOneBySomeField($value): ?MobilePaymentSubscriptionPackage
    //    {
    //        return $this->createQueryBuilder('m')
    //            ->andWhere('m.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->getQuery()
    //            ->getOneOrNullResult()
    //        ;
    //    }
    public function save(MobilePaymentSubscriptionPackage $subscriptionPackage)
    {
        $this->getEntityManager()->persist($subscriptionPackage);
        $this->getEntityManager()->flush();
    }
}
