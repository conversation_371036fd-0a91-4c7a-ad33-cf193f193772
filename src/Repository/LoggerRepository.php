<?php

namespace App\Repository;

use App\Entity\Logger;
use App\Entity\User;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Component\Security\Core\User\UserInterface;

use function Sentry\captureMessage;

/**
 * @extends ServiceEntityRepository<Logger>
 *
 * @method Logger|null find($id, $lockMode = null, $lockVersion = null)
 * @method Logger|null findOneBy(array $criteria, array $orderBy = null)
 * @method Logger[]    findAll()
 * @method Logger[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class LoggerRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Logger::class);
    }

    public function add(Logger $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(Logger $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function notice(?User $user, string $type, ?string $identifier, string $comment, bool $sentry = false)
    {
        $this->log($user, $type, $identifier, $comment, $sentry, "NOTICE");
    }

    public function error(?User $user, string $type, ?string $identifier, string $comment, bool $sentry = false)
    {
        $this->log($user, $type, $identifier, $comment, $sentry, "ERROR");
    }

    public function log(?User $user, string $type, ?string $identifier, string $comment, bool $sentry = false, string $level = 'DEBUG')
    {
        $log = new Logger();
        $log->setUser($user)
            ->setType($type)
            ->setIdentifier($identifier)
            ->setComment($comment)
            ->setLevel($level)
            ->setCtime(new \DateTime())
        ;

        $this->add($log, true);

        if ($sentry) {
            captureMessage("Użytkownik {$user?->getEmail()}, {$type}-{$identifier}: $comment");
        }
    }

//    /**
//     * @return Logger[] Returns an array of Logger objects
//     */
//    public function findByExampleField($value): array
//    {
//        return $this->createQueryBuilder('l')
//            ->andWhere('l.exampleField = :val')
//            ->setParameter('val', $value)
//            ->orderBy('l.id', 'ASC')
//            ->setMaxResults(10)
//            ->getQuery()
//            ->getResult()
//        ;
//    }

//    public function findOneBySomeField($value): ?Logger
//    {
//        return $this->createQueryBuilder('l')
//            ->andWhere('l.exampleField = :val')
//            ->setParameter('val', $value)
//            ->getQuery()
//            ->getOneOrNullResult()
//        ;
//    }
}
