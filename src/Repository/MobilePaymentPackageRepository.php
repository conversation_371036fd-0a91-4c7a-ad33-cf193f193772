<?php

namespace App\Repository;

use App\Entity\MobilePaymentPackage;
use App\Entity\User;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\Query\ResultSetMapping;
use Doctrine\Persistence\ManagerRegistry;

/**
 * MobilePaymentPackageRepository
 *
 * This class was generated by the Doctrine ORM. Add your own custom
 * repository methods below.
 * @method MobilePaymentPackage|null find($id, $lockMode = null, $lockVersion = null)
 */
class MobilePaymentPackageRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, MobilePaymentPackage::class);
    }

    /**
     * Return mobile payment packages for user and
     */
    public function findPackages(): ?array
    {
        $query = $this->createQueryBuilder('p')
            ->select('p')
            ->where('p.status = \'a\'')
            ->andWhere('p.hideOnList = :hide OR p.hideOnList IS NULL')
            ->andWhere('p.endTime > :now OR p.endTime IS NULL')
            ->orderBy('p.paymentValue', 'desc')
            ->setParameter('hide', 0)
            ->setParameter('now', new \DateTime('now'))
        ;

        return $query->getQuery()->getResult();
    }


    public function save(?MobilePaymentPackage $item)
    {
        $this->getEntityManager()->persist($item);
        $this->getEntityManager()->flush();
    }

    public function getList()
    {
        $items = $this->findBy(['hideOnList' => false]);
        return [
            'totalItems' => count($items),
            'items' => $items,
        ];
    }
}
