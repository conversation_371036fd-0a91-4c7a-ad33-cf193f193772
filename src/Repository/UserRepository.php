<?php

namespace App\Repository;

use App\Entity\User;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\Query\ResultSetMapping;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Component\Security\Core\Exception\UnsupportedUserException;
use Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface;
use Symfony\Component\Security\Core\User\PasswordUpgraderInterface;

use function get_class;

/**
 * @method User|null find($id, $lockMode = null, $lockVersion = null)
 * @method User|null findOneBy(array $criteria, array $orderBy = null)
 * @method User|null findOneByEmail(string $email)
 * @method User[]    findAll()
 * @method User[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class UserRepository extends ServiceEntityRepository implements PasswordUpgraderInterface
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, User::class);
    }

    /**
     * Used to upgrade (rehash) the user's password automatically over time.
     */
    public function upgradePassword(PasswordAuthenticatedUserInterface $user, string $newHashedPassword): void
    {
        if (!$user instanceof User) {
            throw new UnsupportedUserException(sprintf('Instances of "%s" are not supported.', get_class($user)));
        }

        $user->setPassword($newHashedPassword);
        $this->save($user);
    }

    public function getList(
        ?string $search,
        ?User $manager = null,
        int $page = 1,
        int $perPage = 10,
        ?bool $showTotal = true,
        ?string $orderBy = null,
        ?bool $orderDescending = null,
    ) {

        $baseQuery = $this->createQueryBuilder('u')
                ->leftJoin('u.client', 'c')
                ->leftJoin('u.lastMobilePayment', 'mp')
            ->leftJoin('u.manager', 'm')

        ;

        if ($search) {
            $orX = $baseQuery->expr()->orX();

            $orX->add($baseQuery->expr()->like('LOWER(u.email)', ':search'));
            $orX->add($baseQuery->expr()->like('LOWER(m.email)', ':search'));
            $orX->add($baseQuery->expr()->like('LOWER(c.taxNumber)', ':search'));
            $orX->add($baseQuery->expr()->like('LOWER(c.name)', ':search'));

            $baseQuery->andWhere($orX)
                ->setParameter('search', '%' . strtolower($search) . '%');

            if (is_numeric($search) && ($search < 1000000)) {
                $orX->add($baseQuery->expr()->eq('u.id', ':searchInt'));
                $baseQuery->setParameter('searchInt', (int)$search);
            }
        }

        if ($manager) {
            $baseQuery->andWhere("u.manager = :manager")
                ->setParameter('manager', $manager);
        }

        $total = null;
        if ($showTotal) {
            $countQuery = clone $baseQuery;
            $countQuery->select("COUNT(u.id) as count");
            $summary = $countQuery->getQuery()->getScalarResult();

            $total = $summary[0]['count'];
        }

        $baseQuery
            //->orderBy('mp.initiatedTimestamp', 'DESC')
                // poniewaz psql tranktuje null jako wartosci najwyzsze
            ->orderBy('CASE WHEN mp.initiatedTimestamp IS NULL THEN 1 ELSE 0 END, mp.initiatedTimestamp', 'DESC')
        ;
        $baseQuery->setFirstResult(($page - 1) * $perPage)
            ->setMaxResults($perPage);

        ;

        if ($orderBy !== null && $orderDescending !== null) {
            $orderApiMapping = [
                'balance' => 'u.balance',
                'email' => 'u.email',
                'last_usage' => 'mp.initiatedTimestamp',
                'ctime' => 'u.ctime',
            ];

            if (array_key_exists($orderBy, $orderApiMapping)) {
                $orderBy = $orderApiMapping[$orderBy];

                $order = $orderDescending ? 'DESC' : 'ASC';
                $baseQuery->orderBy("CASE WHEN $orderBy IS NULL THEN 1 ELSE 0 END, $orderBy", $order);
            }
        }

        return [
            'totalItems' => $total,
            'items' => $baseQuery->getQuery()->getResult(),
        ];
    }

    public function getEmptyBalance()
    {

        $baseQuery = $this->createQueryBuilder('u');

            $baseQuery
                ->andWhere("u.balance IS NULL")
                ->andWhere("u.manager IS NULL")
            //->setMaxResults(1000)
            ;

            return $baseQuery->getQuery()->getResult();
    }

    public function save(User $user)
    {
        $this->getEntityManager()->persist($user);
        $this->getEntityManager()->flush();
    }
}
