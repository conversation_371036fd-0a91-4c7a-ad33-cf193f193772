<?php

namespace App\Repository;

use App\Entity\Token;
use App\Entity\User;
use DateTimeImmutable;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method Token|null find($id, $lockMode = null, $lockVersion = null)
 * @method Token|null findOneBy(array $criteria, array $orderBy = null)
 * @method Token[]    findAll()
 * @method Token[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class TokenRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Token::class);
    }

    /**
     * @throws \Doctrine\ORM\NonUniqueResultException
     */
    public function findUserByToken(string $token): ?Token
    {
        $now = new DateTimeImmutable();

        return $this->createQueryBuilder('t')
            ->andWhere('t.token = :token')
            ->andWhere('t.expiresAt > :now')
            ->setParameter('token', $token)
            ->setParameter('now', $now)
            ->getQuery()
            ->getOneOrNullResult()
        ;
    }

    public function invalidateAllActiveTokensByUser(User $user): bool
    {
        $now = new DateTimeImmutable();

        $tokens = $this->createQueryBuilder('t')
            ->andWhere('t.user = :user')
            ->andWhere('t.expiresAt > :now')
            ->setParameter('user', $user)
            ->setParameter('now', $now)
            ->getQuery()
            ->getResult();

        foreach ($tokens as $token) {
            /** @var Token $token */
            $token->invalidate();
            $this->getEntityManager()->persist($token);
        }
        $this->getEntityManager()->flush();

        return true;
    }
}
