<?php

namespace App\Repository;

use App\Entity\ExternalCreditCardToken;
use App\Entity\User;
use DateTime;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * Class ExternalCreditCardTokenRepository
 * @package App\Repository
 * @method ExternalCreditCardToken|null find($id)
 * @method ExternalCreditCardToken|null findOneBy(array $criteria, ?array $orderBy = null)
 */
class ExternalCreditCardTokenRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, ExternalCreditCardToken::class);
    }
    /**
     * Methods initiate credit card token
     *
     */
    public function initCreditCardToken(
        User $user
    ): ExternalCreditCardToken {
        $creditCardToken = new ExternalCreditCardToken();

        $creditCardToken->setUserId($user->getId())
            ->setCtime(new DateTime())
            ->setStatus(ExternalCreditCardToken::STATUS_INITIATE);

        $this->save($creditCardToken);
        return $creditCardToken;
    }

    public function confirm(ExternalCreditCardToken $cardToken)
    {
        $cardToken
            ->setStatus(ExternalCreditCardToken::STATUS_VERIFIED) ;
        return $this->save($cardToken);
    }

    public function wait(ExternalCreditCardToken $cardToken)
    {
        $cardToken
            ->setStatus(ExternalCreditCardToken::STATUS_WAITING_FOR_VERIFICATION) ;
        return $this->save($cardToken);
    }

    public function save(ExternalCreditCardToken $cardToken)
    {
        $this->getEntityManager()->persist($cardToken);
        $this->getEntityManager()->flush();
        return $cardToken;
    }

    public function getTokensForUser(User $user)
    {
        return $this->findBy(
            ['userId' => $user->getId(),
                'status' => ExternalCreditCardToken::STATUS_VERIFIED]
        );
    }

    public function deleteCardByToken(User $user, string $cardToken): ?ExternalCreditCardToken
    {
        $card =  $this->findOneBy(
            ['userId' => $user->getId(),
                'cardToken' => $cardToken,
                'status' => ExternalCreditCardToken::STATUS_VERIFIED]
        );
        $card->setStatus(ExternalCreditCardToken::STATUS_DELETED);
        $this->save($card);
        return $card;
    }

    public function getCardByToken(?User $user, string $cardToken): ?ExternalCreditCardToken
    {
        return $this->findOneBy(
            ['userId' => $user->getId(),
                'cardToken' => $cardToken,
                'status' => ExternalCreditCardToken::STATUS_VERIFIED]
        );
    }
}
