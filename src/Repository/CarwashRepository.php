<?php

namespace App\Repository;

use App\Entity\Carwash;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\Query\ResultSetMapping;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method Carwash|null find($id, $lockMode = null, $lockVersion = null)
 * @method Carwash|null findOneBy(array $criteria, array $orderBy = null)
 * @method Carwash|null findOneBySerialNumber(int $sn)
 * @method Carwash[]    findAll()
 * @method Carwash[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class CarwashRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Carwash::class);
    }

    public function save(Carwash $carwash)
    {
        $this->getEntityManager()->persist($carwash);
        $this->getEntityManager()->flush();
        return $carwash;
    }

    public function checkIfCarwashExists(string $serial): bool
    {
        if (strlen($serial) < 3) {
            return false;
        }

        $carwash = $this->findOneBy(['serialNumber' => $serial]);

        if (!($carwash instanceof Carwash)) {
            return false;
        }

        return $carwash->isPaymentEnabled();
    }

    public function disableNotIn(array $sns)
    {
        $this->createQueryBuilder('c')
            ->update()
            ->set('c.showOnMap', ':showOnMap')
            ->set('c.paymentEnabled', ':paymentEnabled')
            ->where('c.serialNumber NOT IN (:sns)')
            ->setParameter('sns', $sns)
            ->setParameter('showOnMap', false)
            ->setParameter('paymentEnabled', false)
            ->getQuery()
            ->getSingleScalarResult();
    }
}
