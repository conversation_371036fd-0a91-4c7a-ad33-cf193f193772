<?php

namespace App\Repository;

use App\Entity\PromotionalTopupCode;
use DateTime;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\Query\ResultSetMapping;
use Doctrine\Persistence\ManagerRegistry;

    /**
     * @method PromotionalTopupCode|null find($id, $lockMode = null, $lockVersion = null)
     * @method PromotionalTopupCode|null findOneBy(array $criteria, array $orderBy = null)
     * @method PromotionalTopupCode[]    findAll()
     * @method PromotionalTopupCode[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
     */
class PromotionalTopupCodeRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, PromotionalTopupCode::class);
    }

    public function findActiveCode(string $promoCode): array
    {
        $query = $this->createQueryBuilder('ptcode')
            ->where("ptcode.code = :promoCode")
            ->andWhere("ptcode.mobilePayment IS NULL")
            ->setParameter('promoCode', $promoCode);

        return $query->getQuery()->getResult();
    }

    /**
     * @param array $params
     *              $params['sortBy']
     *              $params['page']
     *              $params['limit']
     *              $params['search']
     */
    public function findCodesByParameters(array $params): array
    {
        $dateFrom = new DateTime($params['dateFrom']);
        $dateTo = new DateTime($params['dateTo']);
        $dateTo->setTime(23, 59, 59);

        $paramsDefault = [
            'clients_type' => 'BELOYAL',
            'sortBy' => 'ctime',
            'sortType' => 'desc',
            'page' => 1,
            'limit' => 25,
            'search' => null,
            'codeUsed' => null,
        ];

        if (empty($params)) {
            $params = $paramsDefault;
        } else {
            $params = array_merge($paramsDefault, $params);
        }

        $rsm = new ResultSetMapping();
        $rsm->addScalarResult('id', 'id');
        $rsm->addScalarResult('name', 'group_name');
        $rsm->addScalarResult('code', 'promotional_code');
        $rsm->addScalarResult('value', 'value');
        $rsm->addScalarResult('ctime', 'ctime');
        $rsm->addScalarResult('code_used', 'code_used');
        $rsm->addScalarResult('total_rows', 'totalRows');

        $sql = "
          SELECT u.id, CAST (u.mobile_payment_id as BOOLEAN) as code_used, u.value, g.ctime, g.name, u.code, count(*) OVER() AS total_rows
          FROM bkfpay_promotional_topups_codes AS u
          LEFT JOIN bkfpay_promotional_codes_groups AS g ON (u.group_id = g.id)
          WHERE g.ctime >= :dateFrom
          AND g.ctime <= :dateTo" .
            ((!empty($params['search'])) ?
                " AND (u.code like :search OR g.name like :search)" : "") .
            ((!empty($params['codeUsed']) && $params['codeUsed'] === "1") ?
                " AND u.mobile_payment_id is not null" : "") .
            (($params['codeUsed'] === "0") ?
                " AND u.mobile_payment_id is null" : "") .
            ((!empty($params['sortBy'] && !empty($params['sortType']))) ?
                " ORDER BY " . $params['sortBy'] . " " . $params['sortType'] . " NULLS LAST" : "") .
            " LIMIT :limit OFFSET :offset
        ";

        $query = $this->getEntityManager()->createNativeQuery($sql, $rsm);

        $query->setParameter('dateFrom', $dateFrom);
        $query->setParameter('dateTo', $dateTo);
        $query->setParameter('offset', ((int)$params['page'] - 1) * $params['limit']);
        $query->setParameter('limit', $params['limit']);

        if (!empty($params['search'])) {
            $query->setParameter('search', '%' . $params['search'] . '%');
        }

        $result = $query->getResult();

        if (!empty($result)) {
            return [$result, $result[0]['totalRows']];
        }

        return [[], 0];
    }
}
