<?php

namespace App\Repository;

use App\Entity\ReportFile;
use App\Entity\User;
use App\Service\Reports\Model\Data;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use I2m\Reports\Enum\ReportStatus;

class ReportFileRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, ReportFile::class);
    }

    public function save(ReportFile $entity): ReportFile
    {
        $this->getEntityManager()->persist($entity);
        $this->getEntityManager()->flush();

        return $entity;
    }

    public function error(ReportFile $reportFile)
    {
        $reportFile
            ->setStatus(ReportStatus::ERROR)
            ->setEtime(new \DateTime())
        ;
        return $this->save($reportFile);
    }
    public function process(ReportFile $reportFile)
    {
        $reportFile
            ->setStatus(ReportStatus::PROCESS)
        ;
        return $this->save($reportFile);
    }
    public function done(ReportFile $reportFile)
    {
        $reportFile
            ->setStatus(ReportStatus::DONE)
            ->setEtime(new \DateTime())
        ;
        return $this->save($reportFile);
    }

    public function getList(
        ?User $user = null,
        ?int $page = 1,
        ?int $perPage = 25,
        ?string $kind = null
    ) {
        $qb = $this->createQueryBuilder('e');

        if ($user) {
            $qb->andWhere('e.user = :user')
                ->setParameter('user', $user);
        }

        if ($kind) {
            $qb->andWhere('e.kind = :kind')
                ->setParameter('kind', $kind);
        }

        $countQuery = clone $qb;
        $count = $countQuery->select('COUNT(e.id)')
            ->getQuery()
            ->getSingleScalarResult();

        $qb->orderBy('e.id', 'DESC');
        if ($page && $perPage) {
            $qb->setFirstResult(($page - 1) * $perPage)
                ->setMaxResults($perPage);
        }

        $results = $qb->getQuery()->getResult();

        return (new Data(data: $results, total: $count));
    }
}
