<?php

namespace App\TopUps\Exceptions;

use App\Exception\ClientSideException;
use Throwable;

/**
 * Class PromotionalTopUpCodeNotExistsException
 */
class PromotionalTopUpCodeNotExistsException extends ClientSideException
{
    /**
     * PromotionalTopUpCodeNotExistsException constructor.
     *
     * @param string $message
     * @param int $code
     * @param Throwable|null $previous
     */
    public function __construct($message = "", $code = 0, Throwable $previous = null)
    {
        parent::__construct($message, $code, $previous, 'beLoyal_promotional_topup_code_doesnt_exists');
    }
}
