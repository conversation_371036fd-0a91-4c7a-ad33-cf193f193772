<?php

namespace App\TopUps;

use App\Entity\MobilePayment;
use App\Entity\PromotionalTopupCode;
use App\Entity\User;
use App\Repository\MobilePaymentRepository;
use App\Repository\PromotionalTopupCodeRepository;
use App\TopUps\Exceptions\MoreThanOneCodeActiveException;
use App\TopUps\Exceptions\PromotionalTopUpCodeNotExistsException;
use App\TopUps\Exceptions\TopUpNoPromotionCodeGivenException;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

class TopUpManager
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private TranslatorInterface $translator,
        private MobilePaymentRepository $mobilePaymentRepository
    ) {
    }

    /**
     * Methods returns PromotionalTopupCode Entity if founded in db based on $code.
     * When no active code, or to many active codes found then throws exception.
     *
     *
     * @throws PromotionalTopUpCodeNotExistsException
     * @throws TopUpNoPromotionCodeGivenException
     * @throws MoreThanOneCodeActiveException
     *
     */
    public function findPromotionalCodes(string $code): PromotionalTopupCode
    {
        if (empty($code)) {
            throw new TopUpNoPromotionCodeGivenException(
                $this->translator->trans('promotion.input-empty-code'),
                400
            );
        }

        /** @var PromotionalTopupCodeRepository $repository */
        $repository = $this->entityManager->getRepository(PromotionalTopupCode::class);

        $promotionTopup = $repository->findActiveCode($code);

        if (empty($promotionTopup)) {
            throw new PromotionalTopUpCodeNotExistsException(
                $this->translator->trans('promotion.no-active-code'),
                400
            );
        }

        if (count($promotionTopup) > 1) {
            throw new MoreThanOneCodeActiveException(
                $this->translator->trans('promotion.more-than-one-active-code-found'),
                400
            );
        }

        return $promotionTopup[0];
    }

    /**
     * Method apply promotion code to user. Update user balance.
     */
    public function applyCode(PromotionalTopupCode $promotionalCode, User $user)
    {
        if (null !== $promotionalCode->getMobilePayment()) {
            throw new PromotionalTopUpCodeNotExistsException(
                $this->translator->trans('promotion.no-active-code'),
                400
            );
        }

        $this->mobilePaymentRepository->addCode($user, $promotionalCode);
    }
}
