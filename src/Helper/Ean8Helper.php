<?php

namespace App\Helper;

class Ean8Helper
{
    /**
     * verify EAN8 code.
     *
     *
     */
    public static function verifyEAN8(string $code): bool
    {
        if (strlen($code) === 8) {
            return (self::checkSumEAN8($code) == $code[7]);
        }

        return false;
    }

    /**
     * calculate check sum for EAN8.
     */
    public static function checkSumEAN8(string $digits): int
    {
        $sum = 0;

        if (strlen($digits) < 7) {
            return -1;
        }

        for ($i = 0; $i < 7; $i += 1) {
            if ($i % 2 !== 0) {
                $sum += (int) $digits[$i];
            } else {
                $sum += (int) $digits[$i] * 3;
            }
        }

        return (10 - ($sum % 10)) % 10;
    }
}
