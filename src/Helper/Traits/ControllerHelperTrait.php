<?php

namespace App\Helper\Traits;

use App\Exception\ClientSideException;
use Exception;

trait ControllerHelperTrait
{
    public function getViewException(Exception $exception)
    {
        if (is_subclass_of($exception, ClientSideException::class)) {
            return $this->json($exception->getMessage(), $exception->getClientCode());
        } else {
            //@todo add login error ro sentry
            // return $this->json([], 500);
            throw $exception;
        }
    }
}
