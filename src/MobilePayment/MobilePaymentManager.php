<?php

namespace App\MobilePayment;

use App\Entity\Carwash;
use App\Entity\ExternalCreditCardToken;
use App\Entity\MobilePayment;
use App\Entity\User;
use App\ExternalPayment\Exceptions\TransferPaymentException;
use App\MobilePayment\Exceptions\CarwashParameterException;
use App\MobilePayment\MobilePaymentFacade\Facade\BkfPayTransferFacade;
use App\MobilePayment\MobilePaymentFacade\MobilePaymentFacadeInterface;
use App\MobilePayment\MobilePaymentFacadeFactory\MobilePaymentFacadeFactory;
use App\MobilePayment\MobilePaymentFacadeFactory\Paysystem;
use App\Repository\CarwashRepository;
use App\Repository\ExternalCreditCardTokenRepository;
use App\Repository\MobilePaymentRepository;
use App\Repository\PaymentGateRepository;
use App\Service\AppConfig\AppConfigService;
use App\User\UserService;
use Doctrine\Common\Collections\ArrayCollection;
use Exception;
use I2m\Connectors\Service\CwActionApi\ActionAdminService;
use I2m\Connectors\Service\CwActionApi\ActionPortalService;
use I2m\Connectors\Model\CwActionApi\Payment\PaymentStatusInfo;
use I2m\StandardTypes\Enum\Currency;
use I2m\StandardTypes\Enum\Source;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\Translation\Translator;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

use function Sentry\captureException;

/**
 * MobilePaymentManager
 *
 * This service provide methods to operate on mobile payments.
 * Firstly buildMobilePaymentFacade using MobilePaymentFacadeFactory for given pay_system in input array.
 * Then call selected method on built object.
 */
class MobilePaymentManager
{
    public function __construct(
        private MobilePaymentFacadeFactory $mobilePaymentFactory,
        private CarwashRepository $carwashRepository,
        //private CarwashApiConnector $carwashApi,
        private ActionAdminService $actionAdminService,
        private MobilePaymentService $mobilePaymentService,
        private UserService $userService,
        private ExternalCreditCardTokenRepository $externalCreditCardTokenRepository,
        private TranslatorInterface $translator,
        private MobilePaymentRepository $mobilePaymentRepository,
        private ActionPortalService $actionPortalService,
        private PaymentGateRepository $paymentGateRepository,
        private ParameterBagInterface $parameterBag,
        private BkfPayTransferFacade $bkfPayTransferFacade,
        private AppConfigService $appConfigService
    ) {
    }

    /**
     * Init payment
     * 1. Validate needed data
     * 2. Connect to carwash and send request with payment initialization
     * 3. If payment initialization succeed save payment to db(table MobilePayments)
     *
     * e.g.
     * [
     * 'clientId' => 22,
     * 'standId' => '10001011',
     * 'pay_system' => 'BKFPAY',
     * 'timestamp' => date('Y-m-d H:i:s'),
     * 'value' => 1 * 100,
     * 'clientGeolocation' => ['53.4369912', '14.4097558'],
     * ]
     *
     * @throws Exception
     */
    public function initPayment(User $user, array $inputData): array
    {
        $mobilePayment = $this->buildMobilePaymentFacade(Paysystem::from($inputData['pay_system']));

        return $mobilePayment->initPayment($user, $inputData);
    }

    /**
     * Helper function to return suitable object from MobilePaymentFacadeFactory
     */
    private function buildMobilePaymentFacade(Paysystem $paySystem): MobilePaymentFacadeInterface
    {
        return $this->mobilePaymentFactory->createMobilePaymentFacade($paySystem);
    }

    /**
     * Return all available carwashes
     *
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     */
    public function availableCarwashes(): array
    {
        $carwashesList = new ArrayCollection(
            $this->carwashRepository->findBy(
                [
                    'testCarwash' => false,
                    'paymentEnabled' => true,
                ]
            )
        );

        $carwashes = $this->actionAdminService->getList(
            $carwashesList->map(function (Carwash $carwash) {
                return $carwash->getSerialNumber();
            })
                ->toArray(),
        )->getData();

        $onlineCarwashes = [];
        foreach ($carwashes as $carwash) {
            if (!$carwash->getEdgeDevice()?->isMobileOk()) {
                continue;
            }

            $carwashDb = $this->carwashRepository->findOneBy([
                'testCarwash' => false,
                'paymentEnabled' => true,
                'serialNumber' => $carwash->getSn()
            ]);

            $newCarwash = [
                "serialNumber" =>  $carwashDb->getSerialNumber(),
                "name" => $carwashDb->getName(),
                "pingTime" => $carwash->getEdgeDevice()->getLastMobileOnline()?->format('YmdHis'),
                "address" => $carwashDb->getAddress(),
                "lat" => $carwashDb->getLat(),
                "lon" => $carwashDb->getLon(),
                "stands" => []
                ];

            foreach ($carwash->getStands() as $stand) {
                if ($stand->isMobileEnable() && !is_null($stand->getStandCode())) {
                    $newCarwash['stands'][] = [
                        "bayId" => $stand->getBayId(),
                        "standId" => $stand->getStandCode(),
                        "mobileAvailable" => $stand->isMobileEnable(),
                        "name" => $stand->getSource()
                    ];
                }
            }

            $onlineCarwashes[] = $newCarwash;
        }

        return [
            'status' => 'success',
            'data' => $onlineCarwashes,
        ];
    }

    public function getCardsForCarwash(User $user, array $inputData): array
    {
        $this->setTranslatorLocale($inputData);

        $standId = $inputData['standId'];
        // sprawdzam czy myjnia gotowa do doładowania
        try {
            $info = $this->mobilePaymentService->standInfo($standId);
        } catch (\Exception $e) {
            $user->getLiveStatus()->setStandCodeFail($standId, $e->getMessage());
            throw $e;
        }
        $user->getLiveStatus()->setStandCodeSuccess($standId);

        $cards = [];

        // subskrypcje
        $subs = $this->mobilePaymentRepository->getSubscriptionsBalance($user);

        // skrabonka
        $bkfPay = $this->userService->getBalance($user);

        $mergedBalance = $bkfPay;
        foreach ($subs as $sub) {
            $mergedBalance += $sub['balance'];
        }

        $cards[] = $this->createCard(
            paySystem: Paysystem::BKF_PAY,
            token: "tokenBezZnaczenia",
            title: $this->translator->trans("your_balance"),
            pay_type: "bkfPay",
            logo: $this->getLogo("bkfpay.png"),
            balance: $mergedBalance,
            currency: $this->appConfigService->getCurrency()
        );

        // karty trzymane w bazie
        $creditCardTokens = $this->externalCreditCardTokenRepository->getTokensForUser(
            $user
        );

        foreach ($creditCardTokens as $cc) {
            /** @var ExternalCreditCardToken $cc */
            $cards[] = $this->createCard(
                Paysystem::CREDIT_CARD,
                $cc->getCardToken(),
                $this->translator->trans("card"),
                $cc->getMaskedCardNumber(),
                "bankCard"
            );
        }

        // bramki płątnicze z bazy Paysystem::GATE_TOP_UP

        $gates = $this->paymentGateRepository->findBy(['enable' => true]);
        foreach ($gates as $gate) {
            $cards[] = $this->createCard(
                Paysystem::GATE,
                (string)$gate->getId(),
                $this->translator->trans("mobile_payment.payment_gate"),
                $gate->getComment(),
                $gate->getType()->value,
                $gate->getLogo()
            );
        }


        $source = $info->getStand()->getSource();
        $programs = $this->getPrograms($info->getInfo(), $source);

        return [
            'status' => 'success',
            "data" => $cards,
            "source" => $source,
            "type" => ($source === Source::TERMINAL) ? "program" : "value",
            "programs" => $programs
        ];
    }

    public function getCardsForPackage(User $user, ?array $inputData = null)
    {
        $cards = [];

        // karty trzymane w bazie
        $creditCardTokens = $this->externalCreditCardTokenRepository->getTokensForUser(
            $user
        );

        foreach ($creditCardTokens as $cc) {
            /** @var ExternalCreditCardToken $cc */
            $cards[] = $this->createCard(
                Paysystem::CREDIT_CARD_TOP_UP,
                $cc->getCardToken(),
                $this->translator->trans("card"),
                $cc->getMaskedCardNumber(),
                "bankCard"
            );
        }


        // bramki płątnicze z bazy Paysystem::GATE_TOP_UP
        $gates = $this->paymentGateRepository->findBy(['enable' => true]);

        foreach ($gates as $gate) {
            $cards[] = $this->createCard(
                Paysystem::GATE_BKFPAY_PACKAGE,
                (string)$gate->getId(),
                $this->translator->trans("mobile_payment.payment_gate"),
                $gate->getComment(),
                $gate->getType()->value,
                $gate->getLogo()
            );
        }

        return [
            'status' => 'success',
            "data" => $cards
        ];
    }

    public function getCardsForTopUpValue(User $user, ?array $inputData = null)
    {
        $cards = [];

        // karty trzymane w bazie
        $creditCardTokens = $this->externalCreditCardTokenRepository->getTokensForUser(
            $user
        );

        foreach ($creditCardTokens as $cc) {
            /** @var ExternalCreditCardToken $cc */
            $cards[] = $this->createCard(
                Paysystem::CREDIT_CARD_TOP_UP_VALUE,
                $cc->getCardToken(),
                $this->translator->trans("card"),
                $cc->getMaskedCardNumber(),
                "bankCard"
            );
        }

        // bramki płatnicze
        $gates = $this->paymentGateRepository->findBy(['enable' => true]);
        foreach ($gates as $gate) {
            $cards[] = $this->createCard(
                Paysystem::GATE_BKFPAY_VALUE,
                (string)$gate->getId(),
                $this->translator->trans("mobile_payment.payment_gate"),
                $gate->getComment(),
                $gate->getType()->value,
                $gate->getLogo()
            );
        }

        // post_paid, transfer jeśli spełnione wymagania
        try {
            $this->bkfPayTransferFacade->preparePayment($user, []);
            $cards[] = $this->createCard(
                Paysystem::POST_PAY_TOP_UP,
                "tokenBezZnaczenia",
                'Post paid',
                "Post paid",
                "postPaid"
            );
        } catch (TransferPaymentException $e) {
        }

        return [
            'status' => 'success',
            "data" => $cards
        ];
    }

    private function createCard(
        Paysystem $paySystem,
        string $token,
        ?string $title = null,
        ?string $description = null,
        ?string $pay_type = null, // do usuniecia, po aktualizacji aplikacji do 3.10
        ?string $logo = null,
        ?float $balance = null,
        ?Currency $currency = null
    ) {
        return [

        "pay_system" => $paySystem,
        "token" => $token,
        "title" => $title,
        "logo" => $logo,
        "pay_type" => $pay_type,
        "description" => $description,
        'balance' => $balance,
        'currency' => $currency
        ];
    }

    public function preparePayment(User $user, array $inputData)
    {
        $mobilePayment = $this->buildMobilePaymentFacade(Paysystem::from($inputData['pay_system']));

        return $mobilePayment->preparePayment($user, $inputData);
    }

    public function washingState(User $user, int $paymentId): array
    {
        $mp = $this->mobilePaymentRepository->findOneBy(
            [
                'id' => $paymentId,
                'bkfpayUser' => $user
            ]
        );

        $state = $this->actionPortalService->getState($mp->getStandCode());
        $this->checkWashId($state->getWashId(), $mp);

        return [
            'status' => 'success',
            'state' => $state->getPortalState(),
            'info' => $state->getPortalAction()
        ];
    }

    public function washingStop(User $user, int $paymentId): array
    {
        $mp = $this->mobilePaymentRepository->findOneBy(
            [
                'id' => $paymentId,
                'bkfpayUser' => $user
            ]
        );
        $stop = $this->actionPortalService->stopProgram($mp->getStandCode(), $mp->getWashId());
        return [
            'status' => 'success',
        ];
    }
    public function washingResume(User $user, int $paymentId): array
    {
        $mp = $this->mobilePaymentRepository->findOneBy(
            [
                'id' => $paymentId,
                'bkfpayUser' => $user
            ]
        );
        $resume = $this->actionPortalService->resumeProgram($mp->getStandCode(), $mp->getWashId());
        return [
            'status' => 'success',
        ];
    }

    public function checkWashId(?int $portalWashId, MobilePayment $mp): void
    {
        if (is_null($portalWashId)) {
            throw new CarwashParameterException();
        }
        if ($portalWashId != $mp->getWashId()) {
            throw new CarwashParameterException();
        }
    }

    public function getPrograms(PaymentStatusInfo $info, Source $source): array
    {
        if ($source !== Source::TERMINAL) {
            return [];
        }

        $programCurrency = Currency::from($info->getCurrency()->getCode());
        $programs = [];
        foreach ($info->getPrograms() as $program) {
            //if ($programCurrency->getId() != $this->userService->getCurrency()->getId()) {
                // docelowo nie obsługujemy waluty innej niż waluta aplikacji
                // continue;
            //}

            $programs[] = [
                'id' => $program->getId(),
                'name' => $program->getName(),
                'price' => $program->getPrice(),
                'currency' => $programCurrency->symbol()
            ];
        }

        return $programs;
    }



    private function setTranslatorLocale(array $inputData): void
    {
        if (isset($inputData['language'])) {
            /** @var Translator $translator */
            $translator = $this->translator;
            $translator->setLocale($inputData['language']);
        }
    }

    private function getLogo($file): ?string
    {
        $filePath =
            $this->parameterBag->get('kernel.project_dir') .
            "/public/" .
            $this->parameterBag->get('brandingAssets') .
            "/" . $file;

        $fileContent  =  file_get_contents($filePath);
        return $fileContent ? base64_encode($fileContent) : null;
    }
}
