<?php

namespace App\MobilePayment\Exceptions;

use Exception;
use Throwable;

/**
 * Class MobilePaymentIncorectPackageValueException
 *
 * @package App\MobilePayment\Exception
 */
class MobilePaymentIncorectPackageValueException extends Exception
{
    /**
     * MobilePaymentIncorectPackageValueException constructor.
     *
     * @param string $message
     * @param int $code
     * @param Throwable|null $previous
     */
    public function __construct(
        $message = 'PackageValidationError;Payment value is different from package value.',
        $code = 0,
        Throwable $previous = null
    ) {
        parent::__construct($message, $code, $previous);
    }
}
