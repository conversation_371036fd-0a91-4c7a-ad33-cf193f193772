<?php

namespace App\MobilePayment\Exceptions;

use Exception;
use Throwable;

/**
 * Class MobilePaymentPackageNotExistsException
 * @package App\MobilePayment\Exception
 */
class MobilePaymentPackageNotExistsException extends Exception
{
    /**
     * MobilePaymentPackageNotExistsException constructor.
     *
     * @param string $message
     * @param int $code
     * @param Throwable|null $previous
     */
    public function __construct($message = 'Mobile payment package not exists.', $code = 0, Throwable $previous = null)
    {
        parent::__construct($message, $code, $previous);
    }
}
