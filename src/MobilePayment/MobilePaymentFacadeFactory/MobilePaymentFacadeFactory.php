<?php

namespace App\MobilePayment\MobilePaymentFacadeFactory;

use App\Entity\Enum\Issuer;
use App\Entity\ExternalPayment;
use App\Entity\MobilePayment;
use App\MobilePayment\MobilePaymentFacade\Facade\BkfPayCarwashFacade;
use App\MobilePayment\MobilePaymentFacade\Facade\BkfPayPostUsageFacade;
use App\MobilePayment\MobilePaymentFacade\Facade\BkfPayTopUpFacade;
use App\MobilePayment\MobilePaymentFacade\Facade\BkfPayTopUpValueFacade;
use App\MobilePayment\MobilePaymentFacade\Facade\BkfPayTransferFacade;
use App\MobilePayment\MobilePaymentFacade\Facade\ExtPayCarwashFacade;
use App\MobilePayment\MobilePaymentFacade\Facade\VirtualCardCarwashFacade;
use App\MobilePayment\MobilePaymentFacade\MobilePaymentFacadeInterface;

/**
 * MobilePaymentFacadeFactory
 *
 * This factory provide new instance of MobilePaymentFacadeInterface object using specified type as param
 */
class MobilePaymentFacadeFactory
{
    public function __construct(
        private readonly BkfPayTransferFacade $bkfPayTransferFacade,
        private readonly VirtualCardCarwashFacade $virtualCardCarwashFacade,
        private readonly ExtPayCarwashFacade $extPayCarwashFacade,
        private readonly BkfPayTopUpFacade $extPayTopUpFacade,
        private readonly BkfPayCarwashFacade $bkfPayCarwashFacade,
        private readonly BkfPayTopUpValueFacade $bkfPayTopUpValueFacade,
        private readonly BkfPayPostUsageFacade $bkfPayPostUsageFacade
    ) {
    }

    /**
     * Factory Method to generate new instance of MobilePaymentFacadeInterface object using specified type as param
     *
     */
    public function createMobilePaymentFacade(Paysystem $type): MobilePaymentFacadeInterface
    {
        return match ($type) {
            //doładowanie stawnowiska
            Paysystem::BKF_PAY => $this->bkfPayCarwashFacade->setIssuer(Issuer::BKF_PAY),
            Paysystem::SUBSCRIPTION => $this->bkfPayCarwashFacade->setIssuer(Issuer::SUBSCRIPTION),
            Paysystem::CREDIT_CARD => $this->extPayCarwashFacade->setExtPaymentProvider(ExternalPayment::ISSUER_CREDIT_CARD),
            Paysystem::GATE => $this->extPayCarwashFacade->setExtPaymentProvider(ExternalPayment::ISSUER_GATE),
            Paysystem::VIRTUAL_CARD => $this->virtualCardCarwashFacade,
            // doładowanie skarbonki - paczki
            Paysystem::CREDIT_CARD_TOP_UP => $this->extPayTopUpFacade->setExtPaymentProvider(ExternalPayment::ISSUER_CREDIT_CARD),
            Paysystem::GATE_BKFPAY_PACKAGE => $this->extPayTopUpFacade->setExtPaymentProvider(ExternalPayment::ISSUER_GATE),
            // doładowanie skarbonki - wartosc
            Paysystem::CREDIT_CARD_TOP_UP_VALUE => $this->bkfPayTopUpValueFacade->setExtPaymentProvider(ExternalPayment::ISSUER_CREDIT_CARD),
            Paysystem::GATE_BKFPAY_VALUE => $this->bkfPayTopUpValueFacade->setExtPaymentProvider(ExternalPayment::ISSUER_GATE),
            Paysystem::POST_PAY_TOP_UP => $this->bkfPayTransferFacade,
            Paysystem::POST_USAGE_TOP_UP => $this->bkfPayPostUsageFacade
        };
    }
}
