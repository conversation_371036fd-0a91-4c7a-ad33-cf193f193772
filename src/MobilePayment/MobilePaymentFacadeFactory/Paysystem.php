<?php

namespace App\MobilePayment\MobilePaymentFacadeFactory;

enum Paysystem : string
{
    // doładowanie stanowiska
    case GATE = 'GATE';
    case CREDIT_CARD = 'CREDIT_CARD';
    case VIRTUAL_CARD = 'VIRTUAL_CARD';

    case BKF_PAY = 'BKFPAY';
    case SUBSCRIPTION = 'SUBSCRIPTION';

    // doładowanie skarbonki
    case CREDIT_CARD_TOP_UP = 'CREDIT_CARD_TPBP';

    case CREDIT_CARD_TOP_UP_VALUE = 'CREDIT_CARD_TPBP_VALUE';
    case POST_PAY_TOP_UP = 'POST_PAID_TOP_UP_BKFPAY';
    case GATE_BKFPAY_PACKAGE = 'BKFPAY_PACKAGE_I2M_GATE';
    case GATE_BKFPAY_VALUE = 'BKFPAY_VALUE_I2M_GATE';
    case POST_USAGE_TOP_UP = 'BKFPAY_POST_USAGE_TOP_UP';
}
