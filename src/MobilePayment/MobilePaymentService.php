<?php

namespace App\MobilePayment;

use App\Entity\Enum\AggrementStatus;
use App\Entity\Enum\Issuer;
use App\Entity\Enum\MobilePaymentStatus;
use App\Entity\Enum\Product;
use App\Entity\MobilePayment;
use App\Entity\User;
use App\ExternalPayment\Exceptions\ParameterValidationException;
use App\MobilePayment\Exceptions\CarwashParameterException;
use App\Repository\CarwashRepository;
use App\Repository\InvoicesRepository;
use App\Repository\LoggerRepository;
use App\Repository\MobilePaymentRepository;
use App\Repository\MobilePaymentSubscriptionPackageRepository;
use App\Service\AppConfig\AppConfigService;
use App\Service\SalesDocument\SalesDocumentService;
use I2m\Connectors\Service\CwActionApi\ActionPaymentService;
use I2m\Connectors\Service\CwActionApi\ActionPortalService;
use I2m\Connectors\Model\CwActionApi\Payment\PaymentStatusAction;
use I2m\Connectors\Model\CwActionApi\Payment\PaymentTopUpAction;
use I2m\Connectors\Model\CwActionApi\Payment\TransactionStatus;
use I2m\Connectors\Model\CwActionApi\Portal\PortalStartAction;
use I2m\Connectors\Model\CwActionApi\Program;
use I2m\Payment\Enum\Status;
use Symfony\Contracts\Translation\TranslatorInterface;

use function Sentry\captureMessage;

class MobilePaymentService
{
    public function __construct(
        private readonly MobilePaymentRepository $mobilePaymentRepository,
        private readonly CarwashRepository $carwashRepository,
        private readonly SalesDocumentService $salesDocumentService,
        private readonly TranslatorInterface $translator,
        private readonly ActionPaymentService $actionPaymentService,
        private readonly ActionPortalService $actionPortalService,
        private readonly InvoicesRepository $invoicesRepository,
        private readonly LoggerRepository $loggerRepository,
        private readonly MobilePaymentSubscriptionPackageRepository $mobilePaymentSubscriptionPackageRepository,
        private readonly AppConfigService $appConfigService,
    ) {
    }

    public function confirm(MobilePayment $mp, ?\DateTime $confirmTimestamp = null): void
    {
        $confirmTimestamp = $confirmTimestamp ?? new \DateTime();

        if ($mp->getStatus() == MobilePaymentStatus::CONFIRMED) {
            captureMessage("transakcja juz byla potwierdzona");
            return;
        }


        if (is_null($mp->getProduct())) {
            $this->mobilePaymentRepository->errorPayment($mp, "nie wiem za co zaplacil klient!!!");
            return;
        }

        switch ($mp->getProduct()) {
            case Product::BKFPAY_TOPUP:
                $this->mobilePaymentRepository->confirmPayment($mp, $confirmTimestamp);
                $this->ackSubscription($mp);

                // czy to była paczka, i czy jest dla niej jakaś promocja?
                $discount = $mp->getBkfpayCompany()->getPostPaidSettings()?->getDiscount();
                $clientBonus = $discount ? $mp->getValue() * ($discount / 100) : 0;
                $packageBonus = $mp->getPackage()?->getBonus() ?? 0;
                $bonus = max($clientBonus, $packageBonus);
                if ($bonus > 0) {
                    $this->mobilePaymentRepository->addBonus(user: $mp->getBkfpayUser(), bonus: $bonus, issuer: $mp->getIssuer(), subscription: $mp->getSubscription());
                }

                break;

            case Product::CARWASH_TOPUP:
                if (is_null($mp->getStandCode())) {
                    $this->mobilePaymentRepository->errorPayment($mp, "brak numeru stanowiska do doladowania!!!");
                    return;
                }
                // jest przypisane do tego stanowisko, trzeba doładować

                try {
                    $this->topUpCarwash($mp);
                } catch (\Exception $e) {
                    $this->mobilePaymentRepository->errorPayment(
                        mobilePayment: $mp,
                        comment: "Nie udało się doładować myjni, możliwe że trzeba dokonać zwrotu klientowi"
                    );
                    return;
                }
                    $this->mobilePaymentRepository->confirmPayment($mp, $confirmTimestamp);
                    // dokument wystawiam tylko gdy wszystko poszlo ok

                break;

            default:
                $this->loggerRepository->notice(
                    user: $mp->getBkfpayUser(),
                    type:"MOBILE_PAYMENT",
                    identifier: "refund",
                    comment: "nie wiem za co zaplacil klient!!!",
                    sentry: true
                );
                $this->mobilePaymentRepository->errorPayment($mp);
                return;
        }

        $this->salesDocumentService->generateAsync($mp);
    }

    public function refund(MobilePayment $mp): void
    {
        $this->mobilePaymentRepository->refundPayment($mp, "Dokonano zwrotu klientowi");
    }

    public function reject(MobilePayment $mp): void
    {
        $this->mobilePaymentRepository->rejectPayment($mp, "płatność odrzucona");
    }

    public function standInfo(string $standCode): PaymentStatusAction
    {
        try {
            $response = $this->actionPaymentService->status($standCode);
            if (!$response->isReady()) {
                throw new ParameterValidationException(
                    'carwash stand not ready'
                );
            }
        } catch (\Exception $e) {
            throw new ParameterValidationException(
                'carwash stand not ready'
            );
        }

        $carwash = $this->carwashRepository->findOneBySerialNumber(
            $response->getStand()->getCarwash()->getSn()
        );
        if (is_null($carwash)) {
            throw new ParameterValidationException(
                'carwash not found;' .
                $this->translator->trans("mobile_payment.carwash_not_found", ['%standId%' => $standCode])
            );
        }

        if (!$carwash->isPaymentEnabled()) {
            throw new ParameterValidationException(
                'carwash not enabled;' . $this->translator->trans("carwash_does_not_support_mobile_payments")
            );
        }

        $owner = $carwash->getOwner();
        switch ($owner?->getAggrement()) {
            case AggrementStatus::TERMINATED:
                throw new ParameterValidationException(
                    'no valid aggrement with owner;' . $this->translator->trans("carwash_does_not_support_mobile_payments")
                );


            case AggrementStatus::SIGNED:
            case AggrementStatus::TESTING:
                break;

            default:
                captureMessage("Właściciel {$owner?->getName()} ({$owner?->getId()}) ma niepoprawnie skonfigurowane płątnosci");
                break;
        }

        return $response;
    }

    public function topUpCarwash(MobilePayment $mp)
    {
        try {
            if ($mp->getProgramId()) {
                $this->topUpProgram($mp);
            } else {
                $this->topUpValue($mp);
            }

            if ($mp->getCarwash()->getCashback() && $mp->getIssuer() === Issuer::BKF_PAY) {
                $bonus
                    = ($mp->getCarwash()->getCashback() / 100) * abs($mp->getValue());
                $this->mobilePaymentRepository->addBonus(user: $mp->getBkfpayUser(), bonus: $bonus, issuer: $mp->getIssuer(), subscription: $mp->getSubscription());
            }
        } catch (\Exception $e) {
            throw new ParameterValidationException(
                'carwash stand not ready'
            );
        }
    }

    public static function getProgramPrice(array $programs, int $programId): float
    {
        $programs = array_filter($programs, function (Program $program) use ($programId) {
            return $program->getId() == $programId;
        });

        if (empty($programs)) {
            throw new CarwashParameterException();
        }
        /** @var Program $program */
        $program = reset($programs);

        $value = $program->getPrice();
        return $value;
    }


    public function topUpValue(MobilePayment $mp): PaymentTopUpAction
    {
        $response = $this->actionPaymentService->topUp(
            standCode: $mp->getStandCode(),
            value: abs($mp->getValue()), // wartosci doladowan stanowiska są ujemne w bazie
            currency: $this->appConfigService->getCurrency(),
            app: $this->appConfigService->getAppName(),
            appId: (string)$mp->getBkfpayUser()->getId()
        );

        if (
            $response->
            getTransaction()->
            getStatus() != TransactionStatus::success
        ) {
            throw new ParameterValidationException("Transaction error");
        }

        return $response;
    }

    public function topUpProgram(MobilePayment $mp): PortalStartAction
    {
        $response = $this->actionPortalService->startProgram(
            standCode: $mp->getStandCode(),
            programId: $mp->getProgramId(),
            app: $this->appConfigService->getAppName(),
            appId: (string) $mp->getBkfpayUser()->getId()
        );

        $mp->setWashId($response->getWashId());

        return $response;
    }

    public function getWaitingInvoices(User $user)
    {
        return $this->invoicesRepository->getList(
            $user,
            null,
            null,
            null,
            null,
            null,
            [Status::WAITING->value]
        )["items"];
    }

    private function ackSubscription(MobilePayment $mp)
    {
        $subcription = $mp
            ->getSubscription();

        if (is_null($subcription)) {
            return;
        }

        $date = \DateTimeImmutable::createFromInterface($mp->getConfirmedTimestamp());
        $subcription
            ->setStartTime($date)
            ->setEndTime($date->modify("+{$mp->getPackage()->getValidTime()} days"))
            ->setStatus(MobilePaymentStatus::CONFIRMED)
        ;

        $this
            ->mobilePaymentSubscriptionPackageRepository
            ->save($subcription);
    }
}
