<?php

namespace App\MobilePayment\MobilePaymentFacade\Facade;

use App\MobilePayment\Exceptions\VirtualCardException;
use App\MobilePayment\MobilePaymentFacade\MobilePaymentFacadeInterface;
use App\Entity\User;

class VirtualCardCarwashFacade implements MobilePaymentFacadeInterface
{
    public function __construct(
        //        private readonly LoyaltyCardsService $cwLoyaltyCardsService,
        //        private readonly MobilePaymentRepository $mobilePaymentRepository
    ) {
    }

    public function initPayment(User $user, array $inputData): array
    {
//        $standId = $inputData['standId'];
//        $token = $inputData['token'];
//        $appName = $inputData['appName'];
//        $appVersion = $inputData['appVersion'];
//        $value = $inputData['value'] / 100; // zamieniamy grosze na złotówki, etc, centy na euro.
//        $licensePlate = $inputData['licensePlate'];
//
//        // utwórz mobile payment
//        $mp = $this->mobilePaymentRepository->init(
//            Issuer::VIRTUAL_CARD,
//            TransactionType::PAYMENT,
//            $value,
//            null,
//            $user,
//            $appName,
//            $appVersion,
//            $standId,
//            null,
//            $licensePlate
//        );
//
//        try {
//                $this->cwLoyaltyCardsService->payByCardToken(
//                    $token,
//                    $value,
//                    $standId,
//                    $user->getEmail(),
//                    $user->getId()
//                );
//        } catch (\Exception $e) {
//            $this->mobilePaymentRepository->rejectPayment($mp, 'Payment failed: ' . $e->getMessage());
//            throw new VirtualCardException('Payment failed: ' . $e->getMessage());
//        }
//        $this->mobilePaymentRepository->confirmPayment($mp, new \DateTime());
//
//        return ["status" => "success", "paymentId" => $mp->getId()];
        throw new VirtualCardException('not implemented');
    }

    public function preparePayment(User $user, array $inputData): array
    {
        return [];
    }
}
