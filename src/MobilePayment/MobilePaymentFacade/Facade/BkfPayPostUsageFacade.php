<?php

namespace App\MobilePayment\MobilePaymentFacade\Facade;

use App\Entity\Enum\Issuer;
use App\Entity\Enum\Product;
use App\Entity\Enum\TransactionType;
use App\Entity\ExternalPayment;
use App\Entity\Invoices;
use App\Entity\User;
use App\ExternalPayment\Exceptions\ParameterValidationException;
use App\ExternalPayment\Exceptions\TransferPaymentException;
use App\ExternalPayment\ExternalPaymentFactory;
use App\MobilePayment\MobilePaymentFacade\MobilePaymentFacadeInterface;
use App\MobilePayment\MobilePaymentService;
use App\Repository\MobilePaymentRepository;

class BkfPayPostUsageFacade implements MobilePaymentFacadeInterface
{
    public function __construct(
        private readonly ExternalPaymentFactory $externalPaymentFactory,
        private readonly MobilePaymentRepository $mobilePaymentRepository,
        private readonly MobilePaymentService $mobilePaymentService
    ) {
    }
    private const LIMIT_EXCEEDED = 'limit_exceeded';
    private const EXPIRED_INVOICE_FOUND = 'expired_invoice_found';

    /**
     * Funkcja rozpoczyna doładowanie skarbonki BKFPAY przy pomocy zewnętrznego operatora płątności (Espago, P24)
     */
    public function initPayment(User $user, array $inputData): array
    {
        $from = $inputData['from'] ?? null;
        $to = $inputData['to'] ?? null;

        $value = -$this
            ->mobilePaymentRepository
            ->getBkfPayBalance(
                user: $user,
                from: $from,
                to: $to
            );

        if ($value < 0) {
            return [];
        }

        // utwórz mobile payment
        $mp = $this->mobilePaymentRepository->init(
            Issuer::BKF_PAY,
            TransactionType::TOP_UP,
            $value,
            Product::BKFPAY_TOPUP,
            $user
        );

        $prepare = $this->preparePayment($user, []);

        if ($value > $prepare['max']) {
            throw new ParameterValidationException('ValidationError;payment to high');
        }

        if (($denyReason = $prepare['paymentDenyReason']) != null) {
            throw new ParameterValidationException('ValidationError;' . $denyReason);
        }
        $bonusValue = $value * ($user->getPostPaidSettings()->getDiscount() / 100);
        // rozpocznij platnosc
        $extPaymentService = $this->externalPaymentFactory->create(ExternalPayment::ISSUER_TRANSFER);
        $extStatus = $extPaymentService->init(
            $mp,
            $value - $bonusValue,
            null,
            ['paymentTerm' => $user->getPostPaidSettings()->getPaymentTerm()]
        );

        if ($extStatus->isSuccess()) {
            $this->mobilePaymentRepository->confirmPayment($mp, new \DateTime('now'));
        }

        return [
            'paymentId'  => $mp->getId(),
            'paymentUrl' => null,
            'status'     => 'success',
        ];
    }

    private function checkInvoices(array $invoices)
    {
        $expired = false;
        $sum = 0;
        foreach ($invoices as $invoce) {
            /** @var Invoices $invoce */
            if ($invoce->getPaymentDate() < new \DateTime("-14 days")) {
                $expired = true;
            }
            $sum += $invoce->getTotalGross();
        }
        return  [
            "expired" => $expired,
            "summary" => $sum
        ];
    }

    public function preparePayment(User $user, array $inputData): array
    {
        if (!$user->isTrustedPartner()) {
            throw new TransferPaymentException(
                'user is not trusted partner'
            );
        }

        if (!$user->getClient()?->isInvoicedAfterTransaction()) {
            throw new ParameterValidationException(
                "User generate post-paid top up without invoice data {$user->getEmail()}"
            );
        }

        if (is_null($user->getPostPaidSettings())) {
            throw new ParameterValidationException('ValidationError;no settings for user');
        }

        $invoices = $this->mobilePaymentService->getWaitingInvoices($user);
        $invoicesSummary = $this->checkInvoices($invoices);

        $paymentDenyReason = null;

        if ($invoicesSummary['expired']) {
            $paymentDenyReason = BkfPayPostUsageFacade::EXPIRED_INVOICE_FOUND;
        }

        $maxTopUp = $user->getPostPaidSettings()->getMax() - $invoicesSummary["summary"];
        $maxTopUp = (int)(floor($maxTopUp) / 100) * 100;
        if ($maxTopUp <= $user->getPostPaidSettings()->getMin()) {
            $paymentDenyReason = BkfPayPostUsageFacade::LIMIT_EXCEEDED;
        }

        $isPaymentDenied = $paymentDenyReason != null;

        return [
            'min' => $user->getPostPaidSettings()->getMin(),
            'max' => $maxTopUp,
            'invoices' => $invoices,
            'isPaymentDenied' => $isPaymentDenied,
            'paymentDenyReason' => $paymentDenyReason,
            'status' => 'success',
        ];
    }
}
