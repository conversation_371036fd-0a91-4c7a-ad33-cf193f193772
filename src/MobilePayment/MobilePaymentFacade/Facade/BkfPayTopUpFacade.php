<?php

namespace App\MobilePayment\MobilePaymentFacade\Facade;

use App\Entity\Enum\Issuer;
use App\Entity\Enum\MobilePaymentStatus;
use App\Entity\Enum\Product;
use App\Entity\Enum\TransactionType;
use App\Entity\ExternalPayment;
use App\Entity\MobilePayment;
use App\Entity\MobilePaymentPackage;
use App\Entity\MobilePaymentSubscriptionPackage;
use App\ExternalPayment\Exceptions\ParameterValidationException;
use App\ExternalPayment\ExternalPaymentFactory;
use App\MobilePayment\MobilePaymentFacade\MobilePaymentFacadeInterface;
use App\Entity\User;
use App\MobilePayment\MobilePaymentService;
use App\Repository\MobilePaymentPackageRepository;
use App\Repository\MobilePaymentRepository;
use App\Repository\MobilePaymentSubscriptionPackageRepository;

class BkfPayTopUpFacade implements MobilePaymentFacadeInterface
{
    private ?string $provider = null;
    public function __construct(
        private readonly ExternalPaymentFactory $externalPaymentFactory,
        private readonly MobilePaymentRepository $mobilePaymentRepository,
        private readonly MobilePaymentPackageRepository $mobilePaymentPackageRepository,
        private readonly MobilePaymentSubscriptionPackageRepository $mobilePaymentSubscriptionPackageRepository,
        private readonly MobilePaymentService $mobilePaymentService,
    ) {
    }
    public function setExtPaymentProvider(string $provider): self
    {
        $this->provider = $provider;
        return $this;
    }

    /**
     * Funkcja rozpoczyna doładowanie skarbonki BKFPAY przy pomocy zewnętrznego operatora płątności (Espago, P24)
     */
    public function initPayment(User $user, array $inputData): array
    {
        $appName = $inputData['appName'] ?? null;
        $appVersion = $inputData['appVersion'] ?? null;
        $packageId = $inputData['packageId'];
        $inputData['skip3DSecure'] = false;

        $package = $this->mobilePaymentPackageRepository->find($packageId);
        if (is_null($package)) {
            throw new ParameterValidationException('ValidationError;package not found');
        }

        if (!$package->isActive()) {
            throw new ParameterValidationException('ValidationError;package not active');
        }

        $value = $package->getPaymentValue();

        $mp = $this->getMp($value, $user, $appName, $appVersion, $package);

        $extPaymentService = $this->externalPaymentFactory->create($this->provider);
        // rozpocznij platnosc
        $extStatus = $extPaymentService->init(
            $mp,
            $value,
            "Zakup kredytów na myjnie",
            $inputData
        );

        if ($extStatus->isSuccess()) {
            $this->mobilePaymentService->confirm($mp);
        }

        return [
            'paymentId'  => $mp->getId(),
            'paymentUrl' => $extStatus->getRedirectUrl(),
            'status'     => 'success',
        ];
    }

    public function preparePayment(User $user, array $inputData): array
    {
        return [];
    }

    public function getMp(float $value, User $user, string $appName, string $appVersion, MobilePaymentPackage $package): MobilePayment
    {
        $issuer = Issuer::BKF_PAY;
        $subscriptionPackage = null;

        if ($package->getValidTime()) {
            $subscriptionPackage
                = (new MobilePaymentSubscriptionPackage())
                ->setValue($value)
                ->setUser($user)
                ->setFleet($user->getManager() ?? $user)
                ->setStatus(MobilePaymentStatus::INITIATED)
                ->setPackage($package);
            $this->mobilePaymentSubscriptionPackageRepository->save($subscriptionPackage);
            $issuer = Issuer::SUBSCRIPTION;
        }

        // utwórz mobile payment
        $mp = $this->mobilePaymentRepository->init(
            issuer: $issuer,
            transactionType: TransactionType::TOP_UP,
            value: $value,
            product: Product::BKFPAY_TOPUP,
            user: $user,
            appName: $appName,
            appVersion: $appVersion,
            paymentPackage: $package,
            subscriptionPackage: $subscriptionPackage
        );
        return $mp;
    }
}
