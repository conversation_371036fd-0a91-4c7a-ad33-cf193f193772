<?php

namespace App\MobilePayment\MobilePaymentFacade\Facade;

use App\Entity\Enum\Issuer;
use App\Entity\Enum\Product;
use App\Entity\Enum\TransactionType;
use App\Entity\MobilePayment;
use App\Entity\User;
use App\ExternalPayment\Exceptions\ParameterValidationException;
use App\ExternalPayment\ExternalPaymentFactory;
use App\MobilePayment\MobilePaymentFacade\MobilePaymentFacadeInterface;
use App\MobilePayment\MobilePaymentService;
use App\Repository\MobilePaymentPackageRepository;
use App\Repository\MobilePaymentRepository;

class BkfPayTopUpValueFacade implements MobilePaymentFacadeInterface
{
    private ?string $provider = null;
    public function __construct(
        private readonly ExternalPaymentFactory $externalPaymentFactory,
        private readonly MobilePaymentRepository $mobilePaymentRepository,
        private readonly MobilePaymentService $mobilePaymentService,
    ) {
    }
    public function setExtPaymentProvider(string $provider): self
    {
        $this->provider = $provider;
        return $this;
    }

    /**
     * Funkcja rozpoczyna doładowanie skarbonki BKFPAY przy pomocy zewnętrznego operatora płątności (Espago, P24)
     */
    public function initPayment(User $user, array $inputData): array
    {
        $appName = $inputData['appName'] ?? null;
        $appVersion = $inputData['appVersion'] ?? null;
        $value = $inputData['value'] / 100; // zamieniamy grosze na złotówki, etc, centy na euro.
        $inputData['skip3DSecure'] = false;

        // utwórz mobile payment
        $mp = $this->mobilePaymentRepository->init(
            Issuer::BKF_PAY,
            TransactionType::TOP_UP,
            $value,
            Product::BKFPAY_TOPUP,
            $user,
            $appName,
            $appVersion,
        );

        $extPaymentService = $this->externalPaymentFactory->create($this->provider);
        // rozpocznij platnosc
        $extStatus = $extPaymentService->init(
            $mp,
            $value,
            "Zakup kredytów na myjnie",
            $inputData
        );

        if ($extStatus->isSuccess()) {
            $this->mobilePaymentService->confirm($mp);
        }

        return [
            'paymentId'  => $mp->getId(),
            'paymentUrl' => $extStatus->getRedirectUrl(),
            'status'     => 'success',
        ];
    }

    public function preparePayment(User $user, array $inputData): array
    {
        $invoices = $this->mobilePaymentService->getWaitingInvoices($user);

        return [
            'min' => ($user->getManager() ?? $user)->getPostPaidSettings()?->getMin() ?? 10,
            'max' => 1000,
            'invoices' => $invoices,
            'isPaymentDenied' => false,
            'paymentDenyReason' => null,
            'status' => 'success',
        ];
    }
}
