<?php

namespace App\MobilePayment\MobilePaymentFacade\Facade;

use App\Entity\Enum\Issuer;
use App\Entity\Enum\Product;
use App\Entity\Enum\TransactionType;
use App\ExternalPayment\ExternalPaymentFactory;
use App\MobilePayment\MobilePaymentFacade\MobilePaymentFacadeInterface;
use App\Entity\User;
use App\MobilePayment\MobilePaymentService;
use App\Repository\CarwashRepository;
use App\Repository\MobilePaymentRepository;

class ExtPayCarwashFacade implements MobilePaymentFacadeInterface
{
    private ?string $provider = null;
    public function __construct(
        private ExternalPaymentFactory $externalPaymentFactory,
        private MobilePaymentRepository $mobilePaymentRepository,
        private MobilePaymentService $mobilePaymentService,
        private CarwashRepository $carwashRepository,
    ) {
    }

    public function setExtPaymentProvider(string $provider): self
    {
        $this->provider = $provider;
        return $this;
    }

    /**
     * Funkcja rozpoczyna doładowanie stanowiska mycia, przy pomocy zewnętrznego operatora płatności
      */
    public function initPayment(User $user, array $inputData): array
    {

        $standId = $inputData['standId'];
        $appName = $inputData['appName'] ?? null;
        $appVersion = $inputData['appVersion'] ?? null;
        $licensePlate = $inputData['licensePlate'] ?? null; // parametr opcjonalny
        $inputData['skip3DSecure'] = true;

        // sprawdzam czy myjnia gotowa do doładowania
        try {
            $standInfo = $this->mobilePaymentService->standInfo($standId);
        } catch (\Exception $e) {
            $user->getLiveStatus()->setStandCodeFail($standId, $e->getMessage());
            throw $e;
        }
        $user->getLiveStatus()->setStandCodeSuccess($standId);

        $programId = $inputData['programId'] ?? null;
        if ($programId) {
            $value = MobilePaymentService::getProgramPrice($standInfo->getInfo()->getPrograms(), $programId);
        } else {
            $value = $inputData['value'] / 100; // zamieniamy grosze na złotówki, etc, centy na euro.
        }
        $carwash = $this->carwashRepository->findOneBySerialNumber($standInfo->getStand()->getCarwash()->getSn());

        // utwórz mobile payment
        $mp = $this->mobilePaymentRepository->init(
            issuer: Issuer::EXTERNAL_PAYMENT,
            transactionType: TransactionType::PAYMENT,
            value: $value,
            product: Product::CARWASH_TOPUP,
            user: $user,
            appName: $appName,
            appVersion: $appVersion,
            standCode: $standId,
            carwash: $carwash,
            licensePlate: $licensePlate,
            programId: $programId,
            standName: $standInfo->getStand()->getSource()
        );

        $extPaymentService = $this->externalPaymentFactory->create($this->provider);

        // rozpocznij platnosc
        $extStatus = $extPaymentService->init(
            $mp,
            $value,
            "Doładowanie myjni",
            $inputData
        );

        if ($extStatus->isSuccess()) {
            $this->mobilePaymentService->confirm($mp);
        }

        return [
            'paymentId'  => $mp->getId(),
            'paymentUrl' => $extStatus->getRedirectUrl(),
            'status'     => 'success',
        ];
    }

    public function preparePayment(User $user, array $inputData): array
    {
        return [];
    }
}
