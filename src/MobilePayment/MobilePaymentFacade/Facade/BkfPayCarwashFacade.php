<?php

namespace App\MobilePayment\MobilePaymentFacade\Facade;

use AllowDynamicProperties;
use App\Entity\Carwash;
use App\Entity\Enum\Issuer;
use App\Entity\Enum\Product;
use App\Entity\Enum\TransactionType;
use App\Entity\MobilePayment;
use App\Entity\MobilePaymentSubscriptionPackage;
use App\ExternalPayment\Exceptions\InsufficientFundsException;
use App\MobilePayment\Exceptions\BkfPayLimitException;
use App\MobilePayment\MobilePaymentFacade\MobilePaymentFacadeInterface;
use App\Entity\User;
use App\MobilePayment\MobilePaymentService;
use App\Repository\CarwashRepository;
use App\Repository\MobilePaymentRepository;
use App\Repository\MobilePaymentSubscriptionPackageRepository;
use App\User\UserService;
use I2m\StandardTypes\Enum\Source;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;

class BkfPayCarwashFacade implements MobilePaymentFacadeInterface
{
    private Issuer $issuer;

    public function __construct(
        private MobilePaymentRepository $mobilePaymentRepository,
        private MobilePaymentSubscriptionPackageRepository $mobilePaymentSubscriptionPackageRepository,
        private MobilePaymentService $mobilePaymentService,
        private ParameterBagInterface $parameterBag,
        private CarwashRepository $carwashRepository,
        private UserService $userService
    ) {
    }

    /**
     * Funkcja rozpoczyna doładowanie stanowiska mycia, przy pomocy środków z BKFPAY
     */
    public function initPayment(User $user, array $inputData): array
    {
        $standId = $inputData['standId'];
        $appName = $inputData['appName'] ?? null;
        $appVersion = $inputData['appVersion'] ?? null;
        $licensePlate = $inputData['licensePlate'] ?? null; // parametr opcjonalny

        // sprawdzam czy myjnia gotowa do doładowania
        try {
            $standInfo = $this->mobilePaymentService->standInfo($standId);
        } catch (\Exception $e) {
            $user->getLiveStatus()->setStandCodeFail($standId, $e->getMessage());
            throw $e;
        }
        $user->getLiveStatus()->setStandCodeSuccess($standId);


        $programId = $inputData['programId'] ?? null;
        if ($programId) {
            $value = MobilePaymentService::getProgramPrice($standInfo->getInfo()->getPrograms(), $programId);
        } else {
            $value = $inputData['value'] / 100; // zamieniamy grosze na złotówki, etc, centy na euro.
        }

        $carwash = $this->carwashRepository->findOneBySerialNumber($standInfo->getStand()->getCarwash()->getSn());

        // sprawdzamy czy mamy środki w sybskrypcjach i skarbonce
        $this->checkBalance($user, $value);

        // subskrypcje posortowane od najszybciej kończącej się
        $subs = $this->mobilePaymentRepository->getSubscriptionsBalance($user);

        $mpLastId = null;
        $checkLimit = true;
        $valueRemaining = $value;
        // wykorzystujemy subskrypcje
        foreach ($subs as $sub) {
            if ($valueRemaining <= 0) {
                break;
            }

            // wyliczamy ile pobrać z subskrypcji
            $subPaymentValue = $valueRemaining;
            if ($valueRemaining - $sub['balance'] > 0) {
                $subPaymentValue = $sub['balance'];
            }
            $valueRemaining -= $subPaymentValue;

            $subscription = $this->mobilePaymentSubscriptionPackageRepository->findOneBy([
                'id' => $sub['id'],
                'fleet' => $user->getManager() ?? $user
            ]);

            // tworzymy płatność i próbujemy doładować stanowisko
            $mp = $this->makePartialPayment(
                issuer: Issuer::SUBSCRIPTION,
                checkLimit: $checkLimit,
                value: $subPaymentValue,
                user: $user,
                appName: $appName,
                appVersion: $appVersion,
                standCode: $standId,
                carwash: $carwash,
                licensePlate: $licensePlate,
                programId: $programId,
                standName: $standInfo->getStand()->getSource(),
                subscriptionPackage: $subscription
            );

            $mpLastId = $mp->getId();
            $checkLimit = false;
        }

        // gdy subskrypcje nie wystarczą pobieramy ze skarbonki
        if ($valueRemaining > 0) {
            // tworzymy płatność i próbujemy doładować stanowisko
            $mp = $this->makePartialPayment(
                issuer: $this->issuer,
                checkLimit: $checkLimit,
                value: $valueRemaining,
                user: $user,
                appName: $appName,
                appVersion: $appVersion,
                standCode: $standId,
                carwash: $carwash,
                licensePlate: $licensePlate,
                programId: $programId,
                standName: $standInfo->getStand()->getSource(),
            );

            $mpLastId = $mp->getId();
        }

        return [
            'status' => 'success',
            'paymentUrl' => null,
            'paymentId' => $mpLastId
        ];
    }

    private function checkLimit(User $user, MobilePayment $mp): void
    {
        $this->countLimit($this->parameterBag->get('bkfpay_limit_count_10s'), $user, "10 sec", $mp);
        $this->countLimit($this->parameterBag->get('bkfpay_limit_count_60s'), $user, "60 sec", $mp);
    }

    private function countLimit(
        ?int $limit,
        User $user,
        string $time,
        MobilePayment $mp
    ): void {
        if (!$limit) {
            return;
        }

        $count = $this->mobilePaymentRepository->limitValue($user, new \DateTime("-$time"), false)['count'];
        if ($count > $limit) {
            $this->mobilePaymentRepository->rejectPayment($mp, "przekroczona ilość operacji");
            throw new BkfPayLimitException(
                "Przekroczony limit transakcji w ciągu ostatnich $time, użytkownik {$user->getEmail()} $count > $limit}"
            );
        }
    }

    public function preparePayment(User $user, array $inputData): array
    {
        return [];
    }

    public function setIssuer(Issuer $issuer): self
    {
        $this->issuer = $issuer;
        return $this;
    }

    private function checkBalance(User $user, ?float $value): void
    {
        $subs = $this->mobilePaymentRepository->getSubscriptionsBalance($user);

        $balance = $this->userService->getBalance($user);
        foreach ($subs as $sub) {
            $balance += $sub['balance'];
        }

        if ($balance < $value) {
            throw new InsufficientFundsException();
        }
    }

    private function makePartialPayment(
        Issuer $issuer,
        bool $checkLimit,
        ?float $value,
        User $user,
        ?string $appName = null,
        ?string $appVersion = null,
        ?string $standCode = null,
        ?Carwash $carwash = null,
        ?string $licensePlate = null,
        ?int $programId = null,
        ?Source $standName = null,
        ?MobilePaymentSubscriptionPackage $subscriptionPackage = null,
    ): MobilePayment {
        $mp = $this->mobilePaymentRepository->init(
            issuer: $issuer,
            transactionType: TransactionType::PAYMENT,
            value: $value,
            product: Product::CARWASH_TOPUP,
            user: $user,
            appName: $appName,
            appVersion: $appVersion,
            standCode: $standCode,
            carwash: $carwash,
            licensePlate: $licensePlate,
            programId: $programId,
            standName: $standName,
            subscriptionPackage: $subscriptionPackage
        );

        if ($checkLimit) {
            $this->checkLimit($user, $mp);
        }

        // doladowuje stanowisko
        $this->mobilePaymentService->topUpCarwash($mp);

        // potwierdzam
        $this->mobilePaymentRepository->confirmPayment($mp, new \DateTime());

        return $mp;
    }
}
