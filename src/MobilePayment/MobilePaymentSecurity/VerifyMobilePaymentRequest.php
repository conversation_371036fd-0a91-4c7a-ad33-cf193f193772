<?php

namespace App\MobilePayment\MobilePaymentSecurity;

use App\Entity\User;
use App\Repository\LoggerRepository;
use App\Repository\UserRepository;
use Symfony\Component\Config\Definition\Exception\Exception;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Request;

/**
 * Class VerifyMobilePaymentRequest
 *
 * @package App\MobilePayment\MobilePaymentSecurity
 */
class VerifyMobilePaymentRequest
{
    private string $alg = 'md5';

    private string $separator = '?';

    private array $secretIn;



    /**
     * VerifyMobilePaymentRequest constructor.
     *
     */
    public function __construct(ParameterBagInterface $params)
    {
        $this->secretIn = $params->get('mobile_payment_secret_in');
    }

    public function verify(string $recivedDigest, $amount, $id, string $timestamp)
    {
        $calculatedDigest = $this->calculateDigest($amount, $id, $timestamp);

        if ($calculatedDigest !== $recivedDigest) {
            throw new Exception('invalidDigest;Invalid password');
        }
    }

    public function calculateDigest($amount, $id, string $timestamp): string|false
    {
        $secret = $this->secretIn['DEFAULT'];

        $content = implode(
            '|',
            [
                $amount,
                $id
            ]
        );
        return hash_hmac(
            $this->alg,
            $content,
            $secret . $this->separator . $timestamp
        );
    }
}
