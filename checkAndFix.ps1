#!/usr/bin/env pwsh  

param (

)

#$ErrorActionPreference = "Stop"
	


./vendor/bin/phpcbf ./src/

#./vendor/bin/phpstan analyse -c ./phpstan3.neon  --memory-limit 1G
./vendor/bin/phpstan analyse -c ./phpstan5.neon  --memory-limit 1G

php bin/console doctrine:schema:validate
#php bin/console doctrine:database:drop --if-exists --force --env=test
#php bin/console doctrine:database:create --if-not-exists --env=test
#php bin/console doctrine:schema:update --force --env=test
#php bin/console doctrine:fixtures:load -n --env=test
#php bin/phpunit