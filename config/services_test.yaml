# This file is the entry point to configure your own services.
# Files in the packages/ subdirectory configure your dependencies.

# Put parameters here that don't need to change on each machine where the app is deployed
# https://symfony.com/doc/current/best_practices.html#use-parameters-for-application-configuration
parameters:

services:
    App\ExternalConnectors\Przelewy24\Przelewy24ConnectorInterface: '@App\Mock\Classes\ExternalConnectors\Przelewy24\Przelewy24Connector'

    App\ApiConnector\CarwashApiConnector: '@App\Mock\ApiConnector\CarwashApiConnector'

