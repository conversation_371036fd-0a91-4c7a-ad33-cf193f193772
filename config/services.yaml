# This file is the entry point to configure your own services.
# Files in the packages/ subdirectory configure your dependencies.

# Put parameters here that don't need to change on each machine where the app is deployed
# https://symfony.com/doc/current/best_practices.html#use-parameters-for-application-configuration
parameters:
    espago_gateway: '%env(ESPAGO_GATEWAY)%'
    espago_id: '%env(ESPAGO_ID)%'
    espago_password: '%env(ESPAGO_PASSWORD)%'
    espago_publickey: '%env(ESPAGO_PUBLIC_KEY)%'
    espago_currency: '%env(ESPAGO_CURRENCY)%'

    # TODO: Move to .env
    mobile_payment_secret_in:
        DEFAULT: '12345nsauydgyt8967'
        POST_PAID: '6rXaMIJNI28sJl40JTAzsXPB'


    carwash_api_url: '%env(CARWASH_API_URL)%'
    taxpayer_api_url: '%env(TAXPAYER_API_URL)%'


    brandingAssets: 'assets/branding/%env(BRANDING_NAME)%'
    web_url: '%env(WEB_URL)%'

    default_internalization.locale: '%env(DEFAULT_INTERNALIZATION_LOCALE)%'

    #PAY_EX
    pay_ex_merchant_token: '%env(PAY_EX_MERCHANT_TOKEN)%'
    pay_ex_merchant_id: '%env(PAY_EX_MERCHANT_ID)%'
    pay_ex_payee_id: '%env(PAY_EX_PAYEE_ID)%'
    pay_ex_api_url: '%env(PAY_EX_API_URL)%'

    ## BRANDING
    branding_currency_code: '%env(BRANDING_CURRENCY_CODE)%'
    branding_country_code: '%env(BRANDING_COUNTRY_CODE)%'
    branding_timezone: '%env(BRANDING_TIMEZONE)%'
    branding_name: '%env(BRANDING_NAME)%'

    credit_card_provider: '%env(CREDIT_CARD_PROVIDER)%'

    bkf_api_sync_payments_method: '%env(BKF_API_SYNC_PAYMENT_METHODS)%'

    front_url: '%env(FRONT_URL)%'

    #data to issue an invoice
    invoice_issuer_vat: '%env(INVOICE_ISSUER_VAT)%'

    invoice_issuer_support: '%env(INVOICE_ISSUER_CONTACT_EMAIL)%'
    invoice_issuer_bankAccountNumber: '%env(INVOICE_ISSUER_BANK_ACCOUNT_NUMBER)%'
    invoice_receipt_copy_emails: '%env(INVOICE_RECEIPT_COPY_EMAILS)%'
    invoice_transfer_copy_emails: '%env(INVOICE_TRANSFER_COPY_EMAILS)%'
    invoice_issuer_owner_id: '%env(INVOICE_ISSUER_OWNER_ID)%'

    ## dane firmy
    issuer.name: '%env(INVOICE_ISSUER_NAME)%'
    issuer.address: '%env(INVOICE_ISSUER_ADDRESS)%'
    issuer.code: '%env(INVOICE_ISSUER_POSTCODE)%'
    issuer.city: '%env(INVOICE_ISSUER_CITY)%'
    issuer.country: '%env(INVOICE_ISSUER_COUNTRY_NAME)%'
    issuer.taxNumber: '%env(INVOICE_ISSUER_NIP)%'
    issuer.email: '%env(INVOICE_ISSUER_CONTACT_EMAIL)%'

    bkfpay_limit_count_10s: '%env(BKFPAY_LIMIT_COUNT_10S)%'
    bkfpay_limit_count_60s: '%env(BKFPAY_LIMIT_COUNT_60S)%'


    mailer_address_no_replay: '%env(MAILER_ADDRESS_NO_REPLAY)%'
    app_env: '%env(APP_ENV)%'
    app_name: '%env(APP_NAME)%'

    receipt_company: '%env(RECEIPT_COMPANY)%'

    i2m.payment.url: '%env(WEB_URL)%/external_payment/gate'
    i2m.cw-action-api.token: '%env(CW_ACTION_API_TOKEN)%'
    i2m.invoice.storage-path: '%env(APP_NAME)%/invoices'
    i2m.reports.storage-path: '%env(APP_NAME)%/reports'
    i2m.storage.container: 'wla'
    i2m.storage.connection_string: '%env(STORAGE_CONNECTION)%'

    mobile_app.ios: '%env(MOBILE_APP_IOS)%'
    mobile_app.android: '%env(MOBILE_APP_ANDROID)%'

    simple_support_email: '%env(SIMPLE_SUPPORT_EMAIL)%'
services:
    # default configuration for services in *this* file
    _defaults:
        autowire: true      # Automatically injects dependencies in your services.
        autoconfigure: true # Automatically registers your services as commands, event subscribers, etc.
            # pass this value to any $projectDir argument for any service
            # that's created in this file (including controller arguments)

    # makes classes in src/ available to be used as services
    # this creates a service per class whose id is the fully-qualified class name
    App\:
        resource: '../src/'
        exclude:
            - '../src/DependencyInjection/'
            - '../src/Entity/'
            - '../src/Kernel.php'
            - '../src/Tests/'

    App\Fixtures\FixturesLoader:
        arguments: ['%kernel.project_dir%']

    App\Security\LogoutListener:
        tags:
            - name: 'kernel.event_listener'
              event: 'Symfony\Component\Security\Http\Event\LogoutEvent'
              dispatcher: security.event_dispatcher.main

    I2m\Invoices\Interface\I2mInvoiceRepositoryInterface: '@App\Repository\InvoicesRepository'
