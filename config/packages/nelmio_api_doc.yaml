nelmio_api_doc:
    documentation:
        info:
            title: My App
            description: This is an awesome app!
            version: 1.0.0
        components:
            securitySchemes:
                ApiKeyAuth:
                    type: apiKey
                    in: header
                    name: Authorization

        security:
            - ApiKeyAuth: [ ]
    areas: # to filter documented areas
        path_patterns:
            - ^/api(?!/doc$) # Accepts routes under /api except /api/doc
            - ^/sticker
            - ^/license_plates
