twig:
    paths:
        '%kernel.project_dir%/templates' : ~
        '%kernel.project_dir%/vendor/i2m/invoices/templates': i2m_invoices
    globals:
        domainName: '%env(WEB_URL)%'
        projectDir: "%kernel.project_dir%"
        assetsDir: 'assets'
        brandingAssets: 'assets/branding/%env(BRANDING_NAME)%'
        language: '%env(DEFAULT_INTERNALIZATION_LOCALE)%' #domyslny jezyk jesli nie podany inny

        app_name: '%env(APP_NAME)%'

        ## dane firmy
        issuer:
            name: '%env(INVOICE_ISSUER_NAME)%'
            address: '%env(INVOICE_ISSUER_ADDRESS)%'
            code: '%env(INVOICE_ISSUER_POSTCODE)%'
            city: '%env(INVOICE_ISSUER_CITY)%'
            country: '%env(INVOICE_ISSUER_COUNTRY_NAME)%'
            taxNumber: '%env(INVOICE_ISSUER_NIP)%'
            bankAccountNumber: '%env(INVOICE_ISSUER_BANK_ACCOUNT_NUMBER)%'
            bankgiro: '%env(INVOICE_ISSUER_BANKGIRO)%'
        ## aplikacja mobilna
        mobileApp:
            iOS: '%env(MOBILE_APP_IOS)%'
            android: '%env(MOBILE_APP_ANDROID)%'

        ## kolory
        color:
            primary: '%env(COLOR_PRIMARY)%'
            secondary: '%env(COLOR_SECONDARY)%'

when@test:
    twig:
        strict_variables: true
