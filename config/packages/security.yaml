security:
    password_hashers:
        App\Entity\User:
            algorithm: auto

    # https://symfony.com/doc/current/security/experimental_authenticators.html
    enable_authenticator_manager: true
    # https://symfony.com/doc/current/security.html#where-do-users-come-from-user-providers
    providers:
        # used to reload user from session & other features (e.g. switch_user)
        app_user_provider:
            entity:
                class: App\Entity\User
                property: email
    firewalls:
        dev:
            pattern: ^/(_(profiler|wdt)|css|images|js)/
            security: false
        main:
            lazy: true
            provider: app_user_provider
            stateless: true
            custom_authenticator:
                - App\Security\LegacyApiV2Authenticator
                - App\Security\FacebookAuthenticator
                - App\Security\FacebookMobileAuthenticator
                - App\Security\GoogleMobileAuthenticator
                - App\Security\GoogleAuthenticator
                - App\Security\AppleMobileAuthenticator
            logout:
                path: new_user_logout


    # Easy way to control access for large sections of your site
    # Note: Only the *first* access control that matches will be used
    access_control:
        - { path: ^/api/doc, roles: PUBLIC_ACCESS }
        - { path: ^/api/v3/user/login, roles: PUBLIC_ACCESS }
        - { path: ^/api/users/registers, roles: PUBLIC_ACCESS }
        - { path: ^/api/users/resets, roles: PUBLIC_ACCESS }
        - { path: ^/api/users/new-password, roles: PUBLIC_ACCESS }
        - { path: ^/api/users/(.+), roles: IS_AUTHENTICATED_FULLY }
        - { path: ^/api/lists/(.?), roles: PUBLIC_ACCESS }
        - { path: ^/bkfpay/mobilepayment, roles: PUBLIC_ACCESS } # dostÄ™p jest weryfikowany w kontrolerze
        - { path: ^/verify/email, roles: PUBLIC_ACCESS }
        - { path: ^/connect/, roles: PUBLIC_ACCESS }
        - { path: ^/beloyal, roles: PUBLIC_ACCESS }
        - { path: ^/terms/, roles: PUBLIC_ACCESS }
        - { path: ^/external_payment, role: PUBLIC_ACCESS }
        - { path: ^/api/v3/receipt/(.+), role: PUBLIC_ACCESS }
        - { path: ^/api/admin/(.+), role: ROLE_APP_MANAGER }
        - { path: ^/sticker, roles: PUBLIC_ACCESS }
        - { path: ^/stand/(.+), roles: PUBLIC_ACCESS }
        - { path: ^/$, roles: PUBLIC_ACCESS }
        - { path: ^/, roles: ROLE_USER }
        - { path: ^/bkfpay/mobilepayment, roles: PUBLIC_ACCESS } # dostęp jest weryfikowany w kontrolerze

    role_hierarchy:
        ROLE_APP_MANAGER: []
        ROLE_PROMOTIONCODE_GENERATION: []