<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250513122619 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            ALTER TABLE invoices DROP CONSTRAINT fk_6a2f2f958e5a5475
        SQL);
        $this->addSql(<<<'SQL'
            DROP SEQUENCE file_manager_id_seq CASCADE
        SQL);
        $this->addSql(<<<'SQL'
            DROP SEQUENCE invoice_positions_id_seq CASCADE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE SEQUENCE invoice_position_id_seq INCREMENT BY 1 MINVALUE 1 START 1
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE invoice_position (id INT NOT NULL, invoice_id INT NOT NULL, name VARCHAR(255) NOT NULL, quantity DOUBLE PRECISION NOT NULL, gross_price DOUBLE PRECISION NOT NULL, total_gross DOUBLE PRECISION NOT NULL, tax INT DEFAULT NULL, PRIMARY KEY(id))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_5904BEAD2989F1FD ON invoice_position (invoice_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE invoice_position ADD CONSTRAINT FK_5904BEAD2989F1FD FOREIGN KEY (invoice_id) REFERENCES invoices (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE file_manager DROP CONSTRAINT fk_a1429c827e3c61f9
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE invoice_positions DROP CONSTRAINT fk_b33014e02989f1fd
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE file_manager
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE invoice_positions
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX uniq_6a2f2f958e5a5475
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE invoices DROP invoice_file
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE invoices DROP status
        SQL);
     }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            CREATE SCHEMA public
        SQL);
        $this->addSql(<<<'SQL'
            DROP SEQUENCE invoice_position_id_seq CASCADE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE SEQUENCE file_manager_id_seq INCREMENT BY 1 MINVALUE 1 START 1
        SQL);
        $this->addSql(<<<'SQL'
            CREATE SEQUENCE invoice_positions_id_seq INCREMENT BY 1 MINVALUE 1 START 1
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE file_manager (id INT NOT NULL, owner_id INT DEFAULT NULL, ctime TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, mtime TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, status VARCHAR(1) DEFAULT NULL, oryginal_file_name VARCHAR(256) DEFAULT NULL, file_path VARCHAR(512) DEFAULT NULL, file_name VARCHAR(256) DEFAULT NULL, file_extension VARCHAR(8) DEFAULT NULL, file_size INT DEFAULT NULL, file_type VARCHAR(128) DEFAULT NULL, file_description TEXT DEFAULT NULL, cloud_storage BOOLEAN DEFAULT NULL, cloud_storage_path TEXT DEFAULT NULL, PRIMARY KEY(id))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_a1429c827e3c61f9 ON file_manager (owner_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE invoice_positions (id INT NOT NULL, invoice_id INT NOT NULL, name VARCHAR(128) NOT NULL, description VARCHAR(256) DEFAULT NULL, quantity INT NOT NULL, tax_key VARCHAR(10) NOT NULL, tax_value INT NOT NULL, vat_amount NUMERIC(10, 2) DEFAULT NULL, unit_price NUMERIC(10, 2) DEFAULT NULL, price NUMERIC(10, 2) DEFAULT NULL, price_excluded_tax NUMERIC(10, 2) DEFAULT NULL, calculate_net_from_gross BOOLEAN DEFAULT false NOT NULL, PRIMARY KEY(id))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX fk_invoice_positions_invoices ON invoice_positions (invoice_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE file_manager ADD CONSTRAINT fk_a1429c827e3c61f9 FOREIGN KEY (owner_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE invoice_positions ADD CONSTRAINT fk_b33014e02989f1fd FOREIGN KEY (invoice_id) REFERENCES invoices (id) ON DELETE CASCADE NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE invoice_position DROP CONSTRAINT FK_5904BEAD2989F1FD
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE invoice_position
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX UNIQ_8D93D64919EB6921
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_8D93D64919EB6921 ON "user" (client_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE invoices ADD invoice_file INT DEFAULT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE invoices ADD status VARCHAR(1) NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE invoices ADD CONSTRAINT fk_6a2f2f958e5a5475 FOREIGN KEY (invoice_file) REFERENCES file_manager (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE UNIQUE INDEX uniq_6a2f2f958e5a5475 ON invoices (invoice_file)
        SQL);
    }
}
