<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250515124722 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            DROP SEQUENCE invoice_issuer_id_seq CASCADE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE invoice_issuer DROP CONSTRAINT fk_164cd246ad288b01
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE invoice_issuer
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE invoices ADD send_date TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL
        SQL);
        $this->addSql(<<<'SQL'
            COMMENT ON COLUMN invoices.send_date IS '(DC2Type:datetime_immutable)'
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE owners DROP CONSTRAINT fk_427292fa19eb6921
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_427292fa19eb6921
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE owners DROP client_id
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            CREATE SCHEMA public
        SQL);
        $this->addSql(<<<'SQL'
            CREATE SEQUENCE invoice_issuer_id_seq INCREMENT BY 1 MINVALUE 1 START 1
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE invoice_issuer (id INT NOT NULL, vat_tax_id INT DEFAULT NULL, PRIMARY KEY(id))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_164cd246ad288b01 ON invoice_issuer (vat_tax_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE client_20230901 (id INT NOT NULL, invoice_country_id INT DEFAULT NULL, invoice_currency_id INT DEFAULT NULL, name VARCHAR(255) DEFAULT 'NULL::character varying', email VARCHAR(255) DEFAULT 'NULL::character varying', latitude DOUBLE PRECISION DEFAULT NULL, longitude DOUBLE PRECISION DEFAULT NULL, regon VARCHAR(24) DEFAULT 'NULL::character varying', nip VARCHAR(64) DEFAULT 'NULL::character varying', invoice_name VARCHAR(255) DEFAULT 'NULL::character varying', invoice_address VARCHAR(255) DEFAULT 'NULL::character varying', invoice_post_code VARCHAR(12) DEFAULT 'NULL::character varying', invoice_city VARCHAR(128) DEFAULT 'NULL::character varying', invoice_province VARCHAR(128) DEFAULT 'NULL::character varying', invoiced_after_transaction BOOLEAN DEFAULT NULL)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE invoice_issuer ADD CONSTRAINT fk_164cd246ad288b01 FOREIGN KEY (vat_tax_id) REFERENCES vat_tax (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE invoices DROP send_date
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE owners ADD client_id INT DEFAULT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE owners ADD CONSTRAINT fk_427292fa19eb6921 FOREIGN KEY (client_id) REFERENCES client (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_427292fa19eb6921 ON owners (client_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE SEQUENCE token_id_seq
        SQL);
        $this->addSql(<<<'SQL'
            SELECT setval('token_id_seq', (SELECT MAX(id) FROM token))
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE token ALTER id SET DEFAULT nextval('token_id_seq')
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX token_token_index ON token (token)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX UNIQ_8D93D64919EB6921
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE "user" ALTER currency_id DROP NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_8D93D64919EB6921 ON "user" (client_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE client ADD latitude DOUBLE PRECISION DEFAULT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE client ADD longitude DOUBLE PRECISION DEFAULT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE client ADD regon VARCHAR(24) DEFAULT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE client ADD invoice_province VARCHAR(128) DEFAULT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE mobile_payment ALTER app_name DROP DEFAULT
        SQL);
    }
}
