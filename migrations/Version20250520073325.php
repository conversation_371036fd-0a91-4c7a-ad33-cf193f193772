<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250520073325 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            CREATE TABLE user_status (user_id INT NOT NULL, last_mobile_payment_id INT DEFAULT NULL, last_stand_code_success VARCHAR(8) DEFAULT NULL, last_stand_code_success_time TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, last_stand_code_fail VARCHAR(8) DEFAULT NULL, last_stand_code_fail_time TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(user_id))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_1E527E2156016C4D ON user_status (last_mobile_payment_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE user_status ADD CONSTRAINT FK_1E527E21A76ED395 FOREIGN KEY (user_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE user_status ADD CONSTRAINT FK_1E527E2156016C4D FOREIGN KEY (last_mobile_payment_id) REFERENCES mobile_payment (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            CREATE SCHEMA public
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE user_status DROP CONSTRAINT FK_1E527E21A76ED395
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE user_status DROP CONSTRAINT FK_1E527E2156016C4D
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE user_status
        SQL);
    }
}
