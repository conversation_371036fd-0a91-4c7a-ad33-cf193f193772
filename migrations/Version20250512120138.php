<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250512120138 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            ALTER TABLE invoices RENAME COLUMN cloud_storage_path TO file_path
        SQL);

        $this->addSql(<<<'SQL'
            ALTER TABLE invoices RENAME COLUMN number_preformated TO number_formated
        SQL);

        $this->addSql(<<<'SQL'
            ALTER TABLE invoices RENAME COLUMN number_numerical TO seqentional_number
        SQL);

        $this->addSql(<<<'SQL'
            ALTER TABLE invoices RENAME COLUMN price TO total_gross
        SQL);

        $this->addSql(<<<'SQL'
            ALTER TABLE invoices RENAME COLUMN price_excluded_tax TO total_net
        SQL);

        $this->addSql(<<<'SQL'
            ALTER TABLE invoices RENAME COLUMN tax_amount TO total_tax
        SQL);

        $this->addSql(<<<'SQL'
            ALTER TABLE invoices RENAME COLUMN issuance_date TO invoice_date
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            CREATE SCHEMA public
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX UNIQ_8D93D64919EB6921
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_8D93D64919EB6921 ON "user" (client_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE invoices RENAME COLUMN file_path TO cloud_storage_path
        SQL);
    }
}
