<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250429145347 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {

        $this->addSql(<<<'SQL'
            ALTER TABLE client DROP CONSTRAINT fk_c7440455f92f3e70
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_c7440455f92f3e70
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE client DROP country_id
        SQL);
    }

}
