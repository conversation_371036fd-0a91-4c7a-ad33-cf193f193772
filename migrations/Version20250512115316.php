<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250512115316 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            ALTER TABLE bkfpay_promotional_codes_groups ALTER ctime SET NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE invoices ADD issuer_id INT DEFAULT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE invoices ALTER payment_date TYPE TIMESTAMP(0) WITHOUT TIME ZONE
        SQL);
        $this->addSql(<<<'SQL'
            COMMENT ON COLUMN invoices.payment_date IS '(DC2Type:datetime_immutable)'
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE invoices ADD CONSTRAINT FK_6A2F2F95BB9D6FEE FOREIGN KEY (issuer_id) REFERENCES client (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_6A2F2F95BB9D6FEE ON invoices (issuer_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE mobile_payment ALTER transaction_type SET NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE mobile_payment ALTER product TYPE VARCHAR(16)
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            CREATE SCHEMA public
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE mobile_payment ALTER transaction_type DROP NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE mobile_payment ALTER product TYPE VARCHAR(255)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX UNIQ_8D93D64919EB6921
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_8D93D64919EB6921 ON "user" (client_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE invoices DROP CONSTRAINT FK_6A2F2F95BB9D6FEE
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX IDX_6A2F2F95BB9D6FEE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE invoices DROP issuer_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE invoices ALTER payment_date TYPE TIMESTAMP(0) WITHOUT TIME ZONE
        SQL);
        $this->addSql(<<<'SQL'
            COMMENT ON COLUMN invoices.payment_date IS NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE bkfpay_promotional_codes_groups ALTER ctime DROP NOT NULL
        SQL);
    }
}
