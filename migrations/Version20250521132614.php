<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250521132614 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            ALTER TABLE invoices ADD external_payment_id INT DEFAULT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE invoices ADD description TEXT DEFAULT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE invoices ADD CONSTRAINT FK_6A2F2F951E3D2C69 FOREIGN KEY (external_payment_id) REFERENCES external_payment (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE UNIQUE INDEX UNIQ_6A2F2F951E3D2C69 ON invoices (external_payment_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE owners ADD language VARCHAR(8) DEFAULT NULL
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            CREATE SCHEMA public
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE invoices DROP CONSTRAINT FK_6A2F2F951E3D2C69
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX UNIQ_6A2F2F951E3D2C69
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE invoices DROP external_payment_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE invoices DROP description
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX UNIQ_8D93D64919EB6921
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_8D93D64919EB6921 ON "user" (client_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE owners DROP language
        SQL);
    }
}
