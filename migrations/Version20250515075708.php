<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250515075708 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            ALTER TABLE client RENAME COLUMN nip TO tax_number
        SQL);
        $this->addSql(<<<'SQL'
            UPDATE invoices SET issuer_id = null
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE invoices DROP CONSTRAINT FK_6A2F2F95BB9D6FEE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE invoices ADD CONSTRAINT FK_6A2F2F95BB9D6FEE FOREIGN KEY (issuer_id) REFERENCES owners (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE owners ADD tax_number VARCHAR(64) DEFAULT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE owners ADD address VARCHAR(255) DEFAULT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE owners ADD post_code VARCHAR(12) DEFAULT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE owners ADD city VARCHAR(128) DEFAULT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE owners ADD country VARCHAR(8) DEFAULT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE owners ADD email VARCHAR(255) DEFAULT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE owners ADD bank_account_number VARCHAR(32) DEFAULT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE owners ADD logo TEXT DEFAULT NULL
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            CREATE SCHEMA public
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE owners ADD client_id INT DEFAULT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE owners DROP tax_number
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE owners DROP address
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE owners DROP post_code
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE owners DROP city
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE owners DROP country
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE owners DROP email
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE owners DROP bank_account_number
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE owners DROP logo
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE owners ADD CONSTRAINT fk_427292fa19eb6921 FOREIGN KEY (client_id) REFERENCES client (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_427292fa19eb6921 ON owners (client_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE client RENAME COLUMN tax_number TO nip
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX UNIQ_8D93D64919EB6921
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_8D93D64919EB6921 ON "user" (client_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE invoices DROP CONSTRAINT fk_6a2f2f95bb9d6fee
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE invoices ADD CONSTRAINT fk_6a2f2f95bb9d6fee FOREIGN KEY (issuer_id) REFERENCES client (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
    }
}
