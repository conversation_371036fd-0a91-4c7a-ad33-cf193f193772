<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250509081741 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            ALTER TABLE invoices ADD currency VARCHAR(8) DEFAULT NULL
        SQL);

        $this->addSql(<<<'SQL'
            UPDATE invoices
            SET currency = currency_table.code
            FROM currency AS currency_table
            WHERE invoices.currency_id = currency_table.id
        SQL);
    }

    public function down(Schema $schema): void
    {
    }
}
