<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250418132403 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
            ALTER TABLE external_payment ADD ecct_id INT DEFAULT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE external_payment ADD CONSTRAINT FK_B48A5D4E694DE7BC FOREIGN KEY (ecct_id) REFERENCES external_credit_card_token (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_B48A5D4E694DE7BC ON external_payment (ecct_id)
        SQL);
    }

    public function down(Schema $schema): void
    {

    }
}
