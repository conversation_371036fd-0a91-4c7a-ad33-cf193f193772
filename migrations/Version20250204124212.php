<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250204124212 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE SEQUENCE mobile_token_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE TABLE mobile_token (id INT NOT NULL, user_id INT DEFAULT NULL, token VARCHAR(255) NOT NULL, mobile_device VARCHAR(255) NOT NULL, updated_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_11D11077A76ED395 ON mobile_token (user_id)');
        $this->addSql('CREATE INDEX mobile_token_token_idx ON mobile_token (token)');
        $this->addSql('COMMENT ON COLUMN mobile_token.updated_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE mobile_token ADD CONSTRAINT FK_11D11077A76ED395 FOREIGN KEY (user_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
    }

    public function down(Schema $schema): void
    {

    }
}
