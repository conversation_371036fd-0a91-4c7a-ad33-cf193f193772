<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241031113226 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('DROP SEQUENCE invoice_numerator_id_seq CASCADE');
        $this->addSql('ALTER TABLE invoice_numerator DROP CONSTRAINT fk_4ef11701164cd246');
        $this->addSql('DROP TABLE invoice_numerator');
        $this->addSql('ALTER TABLE bkfpay_promotional_codes_groups ALTER count TYPE INT');
        $this->addSql('ALTER TABLE bkfpay_promotional_codes_groups ALTER source DROP DEFAULT');
        $this->addSql('DROP INDEX idx_b48a5d4eff9bf195');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_B48A5D4EFF9BF195 ON external_payment (mobile_payment_id)');
        $this->addSql('DROP INDEX idx_8d8249f82b5ca896');
        $this->addSql('DROP INDEX idx_8d8249f82989f1fd');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_8D8249F82B5CA896 ON mobile_payment (receipt_id)');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_8D8249F82989F1FD ON mobile_payment (invoice_id)');
        $this->addSql('ALTER TABLE receipt DROP CONSTRAINT fk_5399b645ff9bf195');
        $this->addSql('DROP INDEX idx_5399b645ff9bf195');
        $this->addSql('ALTER TABLE receipt DROP mobile_payment_id');
        $this->addSql('ALTER TABLE "user" DROP CONSTRAINT fk_8d93d6491b019555');
        $this->addSql('DROP INDEX uniq_8d93d6491b019555');
        $this->addSql('ALTER TABLE "user" DROP post_paid_settings_id');
        $this->addSql('DROP INDEX idx_5c844c5a76ed395');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_5C844C5A76ED395 ON user_settings (user_id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE SCHEMA public');
        $this->addSql('CREATE SEQUENCE invoice_numerator_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE SEQUENCE products_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE TABLE invoice_numerator (id INT NOT NULL, invoice_issuer INT DEFAULT NULL, translatedformat VARCHAR(64) NOT NULL, currentvalue INT DEFAULT 0 NOT NULL, current_number VARCHAR(64) NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX idx_4ef11701164cd246 ON invoice_numerator (invoice_issuer)');
        $this->addSql('CREATE TABLE products (id INT NOT NULL, currency_id INT DEFAULT NULL, vat_id INT DEFAULT NULL, ctime TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, mtime TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, product_name VARCHAR(64) NOT NULL, index_main VARCHAR(64) DEFAULT NULL, website VARCHAR(64) DEFAULT NULL, motto VARCHAR(64) DEFAULT NULL, status VARCHAR(1) DEFAULT NULL, price NUMERIC(10, 0) DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX idx_b3ba5a5ab5b63a6b ON products (vat_id)');
        $this->addSql('CREATE INDEX idx_b3ba5a5a38248176 ON products (currency_id)');
        $this->addSql('ALTER TABLE invoice_numerator ADD CONSTRAINT fk_4ef11701164cd246 FOREIGN KEY (invoice_issuer) REFERENCES invoice_issuer (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE products ADD CONSTRAINT fk_b3ba5a5a38248176 FOREIGN KEY (currency_id) REFERENCES currency (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE products ADD CONSTRAINT fk_b3ba5a5ab5b63a6b FOREIGN KEY (vat_id) REFERENCES vat_tax (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('DROP INDEX UNIQ_8D8249F82B5CA896');
        $this->addSql('DROP INDEX UNIQ_8D8249F82989F1FD');
        $this->addSql('CREATE INDEX idx_8d8249f82b5ca896 ON mobile_payment (receipt_id)');
        $this->addSql('CREATE INDEX idx_8d8249f82989f1fd ON mobile_payment (invoice_id)');
        $this->addSql('DROP INDEX UNIQ_B48A5D4EFF9BF195');
        $this->addSql('CREATE INDEX idx_b48a5d4eff9bf195 ON external_payment (mobile_payment_id)');
        $this->addSql('ALTER TABLE receipt ADD mobile_payment_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE receipt ADD CONSTRAINT fk_5399b645ff9bf195 FOREIGN KEY (mobile_payment_id) REFERENCES mobile_payment (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX idx_5399b645ff9bf195 ON receipt (mobile_payment_id)');
        $this->addSql('DROP INDEX UNIQ_8D93D64919EB6921');
        $this->addSql('ALTER TABLE "user" ADD post_paid_settings_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE "user" ALTER currency_id SET NOT NULL');
        $this->addSql('ALTER TABLE "user" ADD CONSTRAINT fk_8d93d6491b019555 FOREIGN KEY (post_paid_settings_id) REFERENCES post_paid_settings (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE UNIQUE INDEX uniq_8d93d6491b019555 ON "user" (post_paid_settings_id)');
        $this->addSql('CREATE INDEX idx_8d93d64919eb6921 ON "user" (client_id)');
        $this->addSql('ALTER TABLE bkfpay_promotional_codes_groups ALTER count TYPE DOUBLE PRECISION');
        $this->addSql('ALTER TABLE bkfpay_promotional_codes_groups ALTER source SET DEFAULT \'WASHSTOP\'');
        $this->addSql('DROP INDEX UNIQ_5C844C5A76ED395');
        $this->addSql('CREATE INDEX idx_5c844c5a76ed395 ON user_settings (user_id)');
    }
}
