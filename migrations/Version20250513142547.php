<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250513142547 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            ALTER TABLE owners ADD invoice_type VARCHAR(16) DEFAULT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE owners ADD invoice_config JSON DEFAULT NULL
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            CREATE SCHEMA public
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX UNIQ_8D93D64919EB6921
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_8D93D64919EB6921 ON "user" (client_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE owners DROP invoice_type
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE owners DROP invoice_config
        SQL);
    }
}
