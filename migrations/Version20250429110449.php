<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250429110449 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            ALTER TABLE client ADD country VARCHAR(8) DEFAULT NULL
        SQL);

        $this->addSql(<<<'SQL'
            UPDATE client
            SET country = country_table.short_name
            FROM country AS country_table
            WHERE client.country_id = country_table.id
        SQL);
    }

}
