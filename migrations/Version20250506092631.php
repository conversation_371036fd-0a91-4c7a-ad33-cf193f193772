<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250506092631 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            CREATE SEQUENCE report_file_id_seq INCREMENT BY 1 MINVALUE 1 START 1
        SQL);
        $this->addSql(<<<'SQL'
            CREATE SEQUENCE reports_config_id_seq INCREMENT BY 1 MINVALUE 1 START 1
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE report_file (id INT NOT NULL, user_id INT DEFAULT NULL, cloud_path VARCHAR(255) DEFAULT NULL, ctime TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, etime TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, type VARCHAR(255) NOT NULL, kind VARCHAR(255) DEFAULT NULL, ext VARCHAR(16) NOT NULL, criterias JSON DEFAULT NULL, mtime TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, status VARCHAR(16) NOT NULL, email JSON DEFAULT NULL, title VARCHAR(255) DEFAULT NULL, email_text VARCHAR(255) DEFAULT NULL, info VARCHAR(255) DEFAULT NULL, durration INT DEFAULT NULL, PRIMARY KEY(id))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_FFB3DBEBA76ED395 ON report_file (user_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE reports_config (id INT NOT NULL, user_id INT DEFAULT NULL, status VARCHAR(1) NOT NULL, type VARCHAR(255) DEFAULT NULL, ctime TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, mtime TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, is_enabled BOOLEAN NOT NULL, config JSON DEFAULT NULL, reportinterval VARCHAR(255) DEFAULT NULL, name VARCHAR(255) DEFAULT NULL, email JSON DEFAULT NULL, ext VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_E3F9C2E6A76ED395 ON reports_config (user_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE report_file ADD CONSTRAINT FK_FFB3DBEBA76ED395 FOREIGN KEY (user_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE reports_config ADD CONSTRAINT FK_E3F9C2E6A76ED395 FOREIGN KEY (user_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
    }

    public function down(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
            DROP SEQUENCE report_file_id_seq CASCADE
        SQL);
        $this->addSql(<<<'SQL'
            DROP SEQUENCE reports_config_id_seq CASCADE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE report_file DROP CONSTRAINT FK_FFB3DBEBA76ED395
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE reports_config DROP CONSTRAINT FK_E3F9C2E6A76ED395
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE report_file
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE reports_config
        SQL);
    }
}
