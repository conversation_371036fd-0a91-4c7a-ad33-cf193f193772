<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250428105903 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            ALTER TABLE "user" DROP CONSTRAINT fk_8d93d64982f1baf4
        SQL);
        $this->addSql(<<<'SQL'
            DROP SEQUENCE languages_id_seq CASCADE
        SQL);

        $this->addSql(<<<'SQL'
            DROP TABLE languages
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE external_credit_card_token DROP payment_method
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE external_credit_card_token DROP payment_subject
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE payment_gate ALTER enable DROP DEFAULT
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX idx_8d93d64982f1baf4
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE "user" DROP language_id
        SQL);
    }

}
