<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250512111831 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
            DROP INDEX idx_6a2f2f9538248176
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE invoices ADD kind VARCHAR(20) DEFAULT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE invoices DROP currency_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE invoices ALTER payment_date TYPE TIMESTAMP(0) WITHOUT TIME ZONE
        SQL);
    }

    public function down(Schema $schema): void
    {

    }
}
