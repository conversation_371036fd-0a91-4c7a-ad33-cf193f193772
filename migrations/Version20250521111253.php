<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250521111253 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            CREATE TABLE invoice_franchise (invoice_id INT NOT NULL, confirm_email VARCHAR(255) DEFAULT NULL, confirm_ip VARCHAR(255) DEFAULT NULL, billing_path VARCHAR(255) DEFAULT NULL, PRIMARY KEY(invoice_id))
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE invoice_franchise ADD CONSTRAINT FK_3593FB62989F1FD FOREIGN KEY (invoice_id) REFERENCES invoices (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE invoices ADD "source" VARCHAR(20) DEFAULT NULL
        SQL);
    }

    public function down(Schema $schema): void
    {

    }
}
