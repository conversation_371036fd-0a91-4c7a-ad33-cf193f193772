<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241126123158 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE SEQUENCE mobile_payment_subscription_package_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE TABLE mobile_payment_subscription_package (id INT NOT NULL, user_id INT DEFAULT NULL, fleet_id INT DEFAULT NULL, package_id INT DEFAULT NULL, value DOUBLE PRECISION NOT NULL, start_time TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, end_time TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, status VARCHAR(16) NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_51115D02A76ED395 ON mobile_payment_subscription_package (user_id)');
        $this->addSql('CREATE INDEX IDX_51115D024B061DF9 ON mobile_payment_subscription_package (fleet_id)');
        $this->addSql('CREATE INDEX IDX_51115D02F44CABFF ON mobile_payment_subscription_package (package_id)');
        $this->addSql('ALTER TABLE mobile_payment_subscription_package ADD CONSTRAINT FK_51115D02A76ED395 FOREIGN KEY (user_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE mobile_payment_subscription_package ADD CONSTRAINT FK_51115D024B061DF9 FOREIGN KEY (fleet_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE mobile_payment_subscription_package ADD CONSTRAINT FK_51115D02F44CABFF FOREIGN KEY (package_id) REFERENCES mobile_payment_package (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
    }

    public function down(Schema $schema): void
    {

    }
}
