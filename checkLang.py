#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
import sys
import yaml

# Define the directory to scan for Symfony files
directory = "./src"
templates_directory = "./templates"

# Define the translation directory
translation_directory = "./translations"

# Regular expression to find translation calls
php_re_list = [
    r"\>trans\(\s?['\"](.*?)['\"]\s?[\)]?", # ->trans
    r"['\"]columnName['\"] => ['\"]([^'\"]*)['\"]", # src/Reports columns translations 'columnName' => 'translation_key'
    r"['\"]literal['\"] => ['\"]([^'\"]*)['\"]", # src/Reports columns translations 'literal' => 'translation_key'
]

twig_re_list = [
    r"['\"]([^'\"]*?)['\"]\s?\|\s?trans", # |trans
    r"{%\s?trans[^}]*%}\s*([^\s]*){%\s?endtrans[^}]*%}", # {% trans %} ... {% endtrans %}
]

pattern_php = re.compile( '|'.join(php_re_list) )
pattern_twig = re.compile( '|'.join(twig_re_list) )

# reports title form src/Reports metogd getTitle
missing_keys = {
    'pl': {
        'terms.content-BE_LOYAL',
        'invoices.payment-method.cash',
        'invoices.payment-method.credit-card',
        'invoices.payment-method.mobile-payment',
        'invoices.payment-method.pay-ex',
        'invoices.payment-method.post-paid',
        'invoices.payment-method.transfer',
    },
    'en': {
        'invoices.payment-method.cash',
        'invoices.payment-method.credit-card',
        'invoices.payment-method.mobile-payment',
        'invoices.payment-method.pay-ex',
        'invoices.payment-method.post-paid',
        'invoices.payment-method.transfer',
    },
    'sv': {
        'terms.content-WASHSTOP',
        'invoices.payment-method.cash',
        'invoices.payment-method.credit-card',
        'invoices.payment-method.mobile-payment',
        'invoices.payment-method.pay-ex',
        'invoices.payment-method.post-paid',
        'invoices.payment-method.transfer',
    },
    'ru': {
        '',
        'invoices.payment-method.cash',
        'invoices.payment-method.credit-card',
        'invoices.payment-method.mobile-payment',
        'invoices.payment-method.pay-ex',
        'invoices.payment-method.post-paid',
        'invoices.payment-method.transfer',
    },
    'lt': {
        '',
        'invoices.payment-method.cash',
        'invoices.payment-method.credit-card',
        'invoices.payment-method.mobile-payment',
        'invoices.payment-method.pay-ex',
        'invoices.payment-method.post-paid',
        'invoices.payment-method.transfer',
    },
    'lv': {
        '',
        'invoices.payment-method.cash',
        'invoices.payment-method.credit-card',
        'invoices.payment-method.mobile-payment',
        'invoices.payment-method.pay-ex',
        'invoices.payment-method.post-paid',
        'invoices.payment-method.transfer',
    },
}

ignore_keys = {
    'pl': {
        '',
        'invoices.bankgiro',
    },
    'en': {
        '',
        'invoices.bankgiro',
    },
    'sv': {
        '',
    },
    'ru': {
        '',
    },
    'lt': {
        '',
    },
    'lv': {
        '',
    },
}

# file_path_preview = '../../Desktop/flat/messages.ru.yml'
# with open(file_path_preview, 'r', encoding='utf-8') as file:
#     yaml_data_preview = yaml.safe_load(file)

# Function to load translations from YAML files for a specific language
def load_translations(language):
    translations = set()
    filename = f"messages.{language}.yml"
    with open(os.path.join(translation_directory, filename), "r") as file:
        content = yaml.safe_load(file)
        return extract_keys_from_yaml(content)

def extract_keys_from_yaml(data, parent_key='', sep='.'):
    """Rekursywne wydobywanie kluczy z zagnieżdżonych struktur YAML."""
    keys = set()
    if isinstance(data, dict):
        for k, v in data.items():
            full_key = f"{parent_key}{sep}{k}" if parent_key else k
            keys.add(full_key)
            keys |= extract_keys_from_yaml(v, full_key, sep=sep)
    return keys

# Function to scan Symfony files for translation usage
def scan_for_translations():
    translation_usage = set()

    for root, _, files in os.walk(directory):
        for filename in files:
            if filename.endswith(".php"):
                with open(os.path.join(root, filename), "r", encoding="utf-8") as file:
                    content = file.read()
                    matches = pattern_php.findall(content)
                    for match in matches:
                        translation_usage.update(match)
            elif filename.endswith(".twig"):
                with open(os.path.join(root, filename), "r", encoding="utf-8") as file:
                    content = file.read()
                    matches = pattern_twig.findall(content)
                    for match in matches:
                        translation_usage.update(match)

    for root, _, files in os.walk(templates_directory):
        for filename in files:
            if filename.endswith(".php"):
                with open(os.path.join(root, filename), "r", encoding="utf-8") as file:
                    content = file.read()
                    matches = pattern_php.findall(content)
                    for match in matches:
                        translation_usage.update(match)
            elif filename.endswith(".twig"):
                with open(os.path.join(root, filename), "r", encoding="utf-8") as file:
                    content = file.read()
                    matches = pattern_twig.findall(content)
                    for match in matches:
                        translation_usage.update(match)

    return translation_usage


langs = ['pl']

# przekazanie parametru z językiem
if len(sys.argv) > 1:
    langs = sys.argv[1:]

def check_translations_by_lang(language):
    dekorator="######################################"

    ignored_keys_by_lang = ignore_keys[language] if language in ignore_keys else {}
    missing_keys_by_lang = missing_keys[language] if language in missing_keys else {}

    translations = load_translations(language)
    translation_usage = scan_for_translations().union(missing_keys_by_lang) - ignored_keys_by_lang
    missing_translations = sorted(translation_usage - translations)

    unused_translations = translations - translation_usage
    unused_translations = sorted(unused_translations)

    print(dekorator)
    print(" Sprawdzenie tłumaczeń ", lang)
    print("\n Brakujące tłumaczenia: ", len(missing_translations))

#     print("\n".join(missing_translations))

    print("\n Niewykorzystane tłumaczenia: ", len(unused_translations))

#     print("\n".join(unused_translations))

#     print(dekorator)
#     print("Used:")
#     translation_usage = sorted(translation_usage)
#     print(dekorator)

#     for key in missing_translations:
#         value = yaml_data_preview.get(key, '')
#         if(len(value) > 0):
#             print(f"{key}: {value}")

#     for key in translation_usage:
#         value = yaml_data_preview.get(key, '')
#         print(f"{key}: {value}")

    if (len(unused_translations) + len(missing_translations) > 0):
        return 1
    return 0

return_error = 0

for lang in langs:
    return_error = check_translations_by_lang(lang) or return_error

sys.exit(return_error)
