/*! CSS Used from: Embedded */
html{
    font-family:Roboto,
    sans-serif;
    font-weight:300;
    font-size: 14px;
    line-height: 20px;
}

.row:after{
    clear:both;
}
table{
    border-collapse:collapse;
    border-spacing:0;
}
.fs-18{
    font-size:18px!important;
}
.fs-20{
    font-size:20px!important;
}
.pt-0{
    padding-top:0!important;
}
.pb-0{
    padding-bottom:0!important;
}
.mt-0{
    margin-top:0!important;
}
img{
    border:0;
}
thead{
    border-bottom:1px solid #d0d0d0;
}
body{
    margin:0;
}
hr{
    box-sizing:content-box;
    height:0;
}
*,:after,:before{
    box-sizing:inherit;
}
.row .col{
    box-sizing:border-box;
}
table.striped>tbody>tr:nth-child(odd){
    background-color:#f2f2f2;
}
table,td,th{
    border:none;
}
table{
    width:100%;
    display:table;
}
table.striped>tbody>tr>td{
    border-radius:0;
}
td,th{
    padding:15px 5px;
    display:table-cell;
    text-align:left;
    vertical-align:middle;
    border-radius:2px;
}
.row .col.s12,.row .col.s5,.row .col.s7{
    left:auto;
    right:auto;
}
.left-align{
    text-align:left;
}
.right-align{
    text-align:right;
}
.center-align{
    text-align:center;
}
.right{
    float:right!important;
}
.row .col{
    float:left;
}
.row,.row .col.s12,.row .col.s5,.row .col.s7{
    margin-left:auto;
}
.row:after{
    content:"";
    display:table;
}
.row .col{
    padding:0 1rem;
}
.row .col.s5{
    width:41.66667%;
}
.row .col.s6{
    width:50%;
    margin-left:auto;
    left:auto;
    right:auto;
}
.row .col.s7{
    width:58.33333%;
}
.row .col.s12{
    width:100%;
}
::-webkit-input-placeholder{
    color:#d1d1d1;
}
:-moz-placeholder{
    color:#d1d1d1;
}
::-moz-placeholder{
    color:#d1d1d1;
}
:-ms-input-placeholder{
    color:#d1d1d1;
}
.row{
    margin-top:2rem;
    margin-right:-2rem;
    margin-bottom:-2rem;
}
.row .col{
    padding-bottom:2rem;
    padding-right:2rem;
    padding-left:0;
}
hr{
    margin-bottom:20px;
}
hr{
    margin-top:20px;
    border:0;
    border-top:1px solid #E8E8E8;
}
body{
    overflow:hidden;
}
body{
    position:relative;
}
.bg-white{
    background-color:white;
}
.bold{
    font-weight:600;
}
.tw-1{
    width:5%!important;
}
.tw-2{
    width:10%!important;
}
.tw-3{
    width:15%!important;
}
.tw-5{
    width:25%!important;
}
.thumbnail-100{
    max-height:100px;
    width: auto;
}
.invoice{
    font-size:80%!important;
}
thead{
    display:table-header-group;
}
tr{
    page-break-inside:avoid;
}
