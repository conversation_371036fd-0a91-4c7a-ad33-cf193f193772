stages:
  - check_translations
  - test
  - deploy

test:
  stage: test
  variables:
    APP_ENV: test
  script:
    - php8.3 /usr/bin/composer install
    - ./vendor/bin/phpcs --parallel=4 -p -n
    #- ./vendor/bin/phpstan analyse -c ./phpstan3.neon --no-progress --memory-limit 1G
    - ./vendor/bin/phpstan analyse -c ./phpstan5.neon --no-progress --memory-limit 1G
    - bin/console lint:twig templates
    - php8.3 bin/console doctrine:database:drop --if-exists --force --env=test
    - php8.3 bin/console doctrine:database:create --if-not-exists --env=test
    - php8.3 bin/console doctrine:schema:update --force --env=test
    - php8.3 bin/console doctrine:fixtures:load -n --env=test
    - php8.3 bin/phpunit
  after_script:
    - php8.3 bin/console doctrine:database:drop --if-exists --force --env=test
  tags:
    - i2m-v2

beloyal_api_sandbox:
  variables:
    APP_ENV: prod
    APP_DIR: /srv/${CI_PROJECT_NAME}-dev
    REV_DIR: ${APP_DIR}/${CI_JOB_ID}
  stage: deploy
  script:
    - dirs=`find ${APP_DIR}/* -maxdepth 0 -type d`
    - mkdir -p ${REV_DIR}
    - rsync -a --delete --exclude .git ./ ${REV_DIR}
    - cd ${REV_DIR}
    - composer install
    - composer dump-env prod
    - php bin/console assets:install public

    # migracja wszystkich baz
    - php bin/console doctrine:migrations:migrate --no-interaction --allow-no-migration --env=sandbox
    - chmod -R 777 ./var
    - php bin/console i2m:version --out_file ${REV_DIR}/public/version.h
    - ln -sfn ${CI_JOB_ID} ${APP_DIR}/latest
    - php bin/console messenger:stop-workers --env=sandbox
  tags:
    - bl-prod
  #allow_failure: true
  when: manual

beloyal_api_web_2:
    variables:
      APP_ENV: prod
      APP_DIR: /srv/${CI_PROJECT_NAME}
      REV_DIR: ${APP_DIR}/${CI_JOB_ID}
    stage: deploy
    script:
      - dirs=`find ${APP_DIR}/* -maxdepth 0 -type d`
      - mkdir -p ${REV_DIR}
      - rsync -a --delete --exclude .git ./ ${REV_DIR}
      - cd ${REV_DIR}
      - composer install
      - composer dump-env prod
      - php bin/console assets:install public

      # migracja wszystkich baz
      - php bin/console doctrine:migrations:migrate --no-interaction --allow-no-migration --env=beloyal
      - php bin/console doctrine:migrations:migrate --no-interaction --allow-no-migration --env=washstop
      - php bin/console doctrine:migrations:migrate --no-interaction --allow-no-migration --env=putok_lt
      - php bin/console doctrine:migrations:migrate --no-interaction --allow-no-migration --env=puto_lv
      - php bin/console doctrine:migrations:migrate --no-interaction --allow-no-migration --env=myjnia10_pl
      - php bin/console doctrine:migrations:migrate --no-interaction --allow-no-migration --env=waywash_cz
      - php bin/console cache:warmup --env=beloyal
      - php bin/console cache:warmup --env=washstop
      - php bin/console cache:warmup --env=putok_lt
      - php bin/console cache:warmup --env=puto_lv
      - php bin/console cache:warmup --env=myjnia10_pl
      - php bin/console cache:warmup --env=waywash_cz
      - chmod -R 777 ./var
      - php bin/console i2m:version --out_file ${REV_DIR}/public/version.h
      - ln -sfn ${CI_JOB_ID} ${APP_DIR}/latest
      - php bin/console messenger:stop-workers --env=beloyal
      - php bin/console messenger:stop-workers --env=washstop
      - php bin/console messenger:stop-workers --env=putok_lt
      - php bin/console messenger:stop-workers --env=puto_lv
      - php bin/console messenger:stop-workers --env=myjnia10_pl
      - php bin/console messenger:stop-workers --env=waywash_cz
    tags:
      - bl-prod
    #allow_failure: true
    #when: manual
    only:
      - master

.check_translations_template:
  script:
    - ./checkLang.py $LANG

check_translations:
  stage: test
  allow_failure: true
  parallel:
    matrix:
      - LANG: "pl"
      - LANG: "en"
      - LANG: "sv"
      - LANG: "ru"
      - LANG: "lt"
      - LANG: "lv"
      - LANG: "cs"
  extends: .check_translations_template
  tags:
    - i2m-v2
