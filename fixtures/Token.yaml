App\Entity\Token:
  token (template):
    expires_at: <(new \DateTimeImmutable('2200-11-27 12:33:46+00'))>

  token1 (extends token):
    token: '1234567890'
    user: '@userMichal'

  token2 (extends token):
    token: 'user2Token'
    user: '@userHubert'

  token3 (extends token):
    token: 'user3token'
    user: '@userFleetManager'

  token4 (extends token):
    token: 'userFleetTrustedManagerToken'
    user: '@userFleetTrustedManager'

  token6 (extends token):
    token: 'userFleetMemberToken'
    user: '@userFleetTrustedManager'

  token7 (extends token):
    token: 'user7token'
    user: '@userFleetTrustedManager'

  token9 (extends token):
    token: 'user9token'
    user: '@userFleetTrustedManager2'

  tokenCmApi (extends token):
    token: 'tokenCmApi'
    user: '@userFleetTrustedManager2'