App\Entity\User:
  user (template):
    ctime: <date_create_from_format('Y-m-d H:i:s' ,'2022-01-01 01:01:01')>
    status: 'a'
    currency: '@currencyEUR'

  userMichal (extends user):
    id: 1
    email: <EMAIL>
    password: \$2y\$13\$q/eWmqO5M4ibGwX.WB/fJeWfYbJvi8qQu5S1iGNA52gDkoI0JT3Ke
    isVerified: true
    trustedPartner: false
    tokens: ['@token1']

  userHubert (extends user):
    id: 2
    email: h.<PERSON><PERSON><PERSON><PERSON>@bkf.pl
    password: \$2y\$13\$q/eWmqO5M4ibGwX.WB/fJeWfYbJvi8qQu5S1iGNA52gDkoI0JT3Ke
    isVerified: false
    client: '@client2'
    tokens: ['@token2']

  fleet (template):
    ctime: <date_create_from_format('Y-m-d H:i:s' ,'2022-01-01 10:30:00')>
    password: \$2y\$13\$q/eWmqO5M4ibGwX.WB/fJeWfYbJvi8qQu5S1iGNA52gDkoI0JT3Ke
    isVerified: true
    status: 'a'
    currency: '@currencyEUR'

  userFleetManager (extends fleet):
    id: 3
    client: '@client10'
    email: <EMAIL>
    trustedPartner: false
    tokens: ['@token3']

  userFleetTrustedManager (extends fleet):
    id: 4
    email: <EMAIL>
    trustedPartner: true
    client: '@client11'
    tokens: ['@token4']

  fleetMemberTest5 (extends fleet):
    id: 5
    email: <EMAIL>
    trustedPartner: false
    client: '@client12'
    manager: '@userFleetTrustedManager'

  fleetMemberTest6 (extends fleet):
    id: 6
    email: <EMAIL>
    trustedPartner: false
    manager: '@userFleetTrustedManager'
    tokens: ['@token6']
    client: '@client13'

  testUser (extends user):
    id: 7
    email: <EMAIL>
    password: \$2y\$13\$q/eWmqO5M4ibGwX.WB/fJeWfYbJvi8qQu5S1iGNA52gDkoI0JT3Ke
    roles: ['ROLE_ADMIN', 'ROLE_CM_OWNER', 'ROLE_CM']
    status: 'a'
    isVerified: true
    tokens: ['@token7']

  testUser2 (extends user):
    id: 8
    email: <EMAIL>
    password: \$2y\$13\$q/eWmqO5M4ibGwX.WB/fJeWfYbJvi8qQu5S1iGNA52gDkoI0JT3Ke
    roles: [ 'ROLE_ADMIN', 'ROLE_CM_OWNER', 'ROLE_CM' ]
    status: 'a'
    isVerified: true

  userFleetTrustedManager2 (extends fleet):
    id: 9
    email: "<EMAIL>"
    trustedPartner: true
    client: '@client9'
    tokens: ['@token9']

  userCmApi (extends user):
    id: 10
    email: CM_API
    password: \$2y\$13\$q/eWmqO5M4ibGwX.WB/fJeWfYbJvi8qQu5S1iGNA52gDkoI0JT3Ke
    isVerified: true
    roles: ['ROLE_APP_MANAGER']
    trustedPartner: false
    tokens: ['@tokenCmApi']

  bkfpayUser (extends user):
    email: 'TEST_USER'
    password: 'SECRET_PASSWORD'
