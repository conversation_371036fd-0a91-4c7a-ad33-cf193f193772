App\Entity\Client:
  client1:
    id: 1
    name: Awrakadabra
    email: <EMAIL>
    city: Stargard
    address: "Jagełły 10"
    postCode: "73-100"
    taxNumber: '1000000033'

  client2:
    id: 2
    name: <PERSON><PERSON>
    email: <EMAIL>
    city: Zamość
    address: "Piastów 12"
    postCode: "22-400"
    taxNumber: '1000000022'
    invoicedAfterTransaction: true

  client9:
    id: 9
    name: <PERSON><PERSON>
    email: <EMAIL>
    city: Zamość
    address: "Piastów 12"
    postCode: "22-400"
    taxNumber: '1000000022'
    invoicedAfterTransaction: true

  client10:
    id: 10
    name: <PERSON><PERSON>
    email: <EMAIL>
    city: Zamość
    address: "Piastów 12"
    postCode: "22-400"
    taxNumber: '1000000023'
    invoicedAfterTransaction: true
  client11:
    id: 11
    name: <PERSON><PERSON>
    email: <EMAIL>
    city: Zamość
    address: "Piastów 12"
    postCode: "22-400"
    taxNumber: '1000000023'
    invoicedAfterTransaction: true
  client12:
    id: 12
    name: <PERSON><PERSON>
    email: <EMAIL>
    city: Zamość
    address: "Piastów 12"
    postCode: "22-400"
    taxNumber: '1000000023'
    invoicedAfterTransaction: true
  client13:
    id: 13
    name: Marek
    email: <EMAIL>
    city: Zamość
    address: "Piastów 12"
    postCode: "22-400"
    taxNumber: '1000000023'
    invoicedAfterTransaction: true
