App\Entity\MobilePayment:
  mobilePaymentForInvoiceTest (template):
    mtime: <(new \DateTime('now'))>
    confirmedTimestamp: <(new \DateTime('now'))>
    initiatedTimestamp: <(new \DateTime('now'))>
    status: 'confirmed'
    bkfpayUser: '@userHubert'
    bkfpayCompany: '@userHubert'

  mobilePayment1 (extends mobilePaymentForInvoiceTest):
    issuer: BKFPAY
    transactionType: TOP_UP
    value: 246
    invoice: '@invoice1'

  mobilePayment2 (extends mobilePaymentForInvoiceTest):
    issuer: BKFPAY
    transactionType: PAYMENT
    value: -24.6
    invoice: '@invoice2'
    standCode: '00150019'

  mobilePayment3 (extends mobilePaymentForInvoiceTest):
    issuer: BKFPAY
    transactionType: PAYMENT
    value: -30.135
    invoice: '@invoice3'
    standCode: '00150019'

  mobilePayment4 (extends mobilePaymentForInvoiceTest):
    issuer: BKFPAY
    transactionType: TOP_UP
    value: 246
