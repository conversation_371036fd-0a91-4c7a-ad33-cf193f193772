App\Entity\Invoices:
  invoice (template):
    published: true
    period: '2017-10'

  invoice1 (extends invoice):
    id: 1
    number: 11111
    invoiceDate: <(new \DateTime('2017-10-23T11:03:39+0000'))>
    paymentDate: <(new \DateTime('2017-10-30T11:03:39+0000'))>
    ctime: <(new \DateTime('2017-10-24T11:03:39+0000'))>
  invoice2 (extends invoice):
    id: 2
    number: 11112
    invoiceDate: <(new \DateTime('2017-10-24T11:03:39+0000'))>
    paymentDate: <(new \DateTime('2017-10-31T11:03:39+0000'))>
    ctime: <(new \DateTime('2017-10-23T11:03:39+0000'))>
  invoice3 (extends invoice):
    id: 3
    number: 11114
    invoiceDate: <(new \DateTime('2017-10-22T11:03:39+0000'))>
    paymentDate: <(new \DateTime('2017-10-29T11:03:39+0000'))>
    ctime: <(new \DateTime('2017-10-23T11:03:39+0000'))>
  invoice4 (extends invoice):
    id: 4
    number: 11115
    invoiceDate: <(new \DateTime('2017-05-10T11:03:39+0000'))>
    paymentDate: <(new \DateTime('2017-05-10T11:03:39+0000'))>
    ctime: <(new \DateTime('2017-05-10T11:03:39+0000'))>
